import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
  useTheme
} from '@mui/material';
import {
  LocalLaundryService as LaundryIcon,
  Inventory as InventoryIcon,
  Description as ContractIcon,
  Assessment as RecordIcon
} from '@mui/icons-material';
import { TodayProduction } from '../../services/dashboardService';
import { formatCurrencyToMillions } from '../../utils/formatters';

interface ProductionTodayWidgetProps {
  data: TodayProduction | null;
  loading?: boolean;
}

const ProductionTodayWidget: React.FC<ProductionTodayWidgetProps> = ({
  data,
  loading = false
}) => {
  const theme = useTheme();

  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toLocaleString('vi-VN');
  };

  const formatCurrency = (value: string | number): string => {
    return formatCurrencyToMillions(value);
  };

  const stats = [
    {
      label: 'Tổng số lượng',
      value: data ? formatNumber(data.total_quantity) : '0',
      icon: <LaundryIcon />,
      color: theme.palette.primary.main
    },
    {
      label: 'Tổng giá trị',
      value: data ? formatCurrency(data.total_value) : '0 tr',
      icon: <RecordIcon />,
      color: theme.palette.success.main
    },
    {
      label: 'Loại sản phẩm',
      value: data ? formatNumber(data.product_types) : '0',
      icon: <InventoryIcon />,
      color: theme.palette.info.main
    },
    {
      label: 'Hợp đồng liên quan',
      value: data ? formatNumber(data.contracts_involved) : '0',
      icon: <ContractIcon />,
      color: theme.palette.warning.main
    }
  ];

  return (
    <Card
      elevation={2}
      className="laundry-hover-lift"
      sx={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: '1px solid rgba(14, 165, 233, 0.1)',
        borderRadius: 2,
        fontSize: '0.85rem'
      }}
    >
      <CardContent sx={{ p: 2.5 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <LaundryIcon 
            sx={{ 
              color: theme.palette.primary.main, 
              mr: 1.5,
              fontSize: '1.5rem'
            }} 
          />
          <Typography
            variant="h6"
            component="h3"
            sx={{
              fontWeight: 600,
              color: theme.palette.primary.main,
              fontSize: '1.1rem'
            }}
          >
            Sản lượng hôm nay
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              Đang tải dữ liệu...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Main Stats */}
            <Grid container spacing={2} sx={{ mb: 2 }}>
              {stats.map((stat, index) => (
                <Grid item xs={6} key={index}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 1.5,
                      backgroundColor: `${stat.color}08`,
                      border: `1px solid ${stat.color}20`,
                      textAlign: 'center'
                    }}
                  >
                    <Box
                      sx={{
                        color: stat.color,
                        mb: 0.5,
                        display: 'flex',
                        justifyContent: 'center'
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        color: stat.color,
                        fontSize: '1.1rem',
                        mb: 0.5
                      }}
                    >
                      {stat.value}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      {stat.label}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>

            <Divider sx={{ my: 2 }} />

            {/* Summary */}
            <Box sx={{ textAlign: 'center' }}>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontSize: '0.8rem' }}
              >
                Tổng cộng{' '}
                <strong>{data ? formatNumber(data.total_records) : '0'}</strong>{' '}
                bản ghi sản xuất trong ngày
              </Typography>
            </Box>

            {/* No data message */}
            {data && parseInt(data.total_records) === 0 && (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 3,
                  color: 'text.secondary'
                }}
              >
                <LaundryIcon sx={{ fontSize: '3rem', opacity: 0.3, mb: 1 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  Chưa có sản lượng nào được ghi nhận hôm nay
                </Typography>
                <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                  Hãy bắt đầu nhập sản lượng để theo dõi tiến độ
                </Typography>
              </Box>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductionTodayWidget;
