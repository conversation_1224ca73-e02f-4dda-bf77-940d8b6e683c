const receivableService = require('../services/receivableService');
const { successResponse, errorResponse } = require('../utils/responseUtils');

/**
 * Receivable Controller
 * <PERSON><PERSON> lý các request liên quan đến receivables (<PERSON><PERSON><PERSON> nợ phải thu)
 */

/**
 * <PERSON><PERSON>y danh sách receivables
 * GET /api/receivables
 */
const getReceivables = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      customerId,
      contractId,
      status,
      sortBy = 'transaction_date',
      sortOrder = 'DESC'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search: search.trim(),
      customerId: customerId ? parseInt(customerId) : undefined,
      contractId: contractId ? parseInt(contractId) : undefined,
      status,
      sortBy,
      sortOrder
    };

    const result = await receivableService.getReceivables(options);
    
    return successResponse(res, result, '<PERSON><PERSON><PERSON> danh sách công nợ phải thu thành công');
  } catch (error) {
    console.error('Error in receivableController.getReceivables:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy receivable theo ID
 * GET /api/receivables/:id
 */
const getReceivableById = async (req, res) => {
  try {
    const { id } = req.params;
    const receivable = await receivableService.getReceivableById(parseInt(id));
    
    return successResponse(res, receivable, 'Lấy thông tin công nợ phải thu thành công');
  } catch (error) {
    console.error('Error in receivableController.getReceivableById:', error);
    const statusCode = error.message.includes('không tìm thấy') ? 404 : 500;
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Tạo receivable mới
 * POST /api/receivables
 */
const createReceivable = async (req, res) => {
  try {
    const receivableData = req.body;
    const createdBy = req.user?.id || 1; // Temporary fallback

    const newReceivable = await receivableService.createReceivable(receivableData, createdBy);
    
    return successResponse(res, newReceivable, 'Tạo công nợ phải thu thành công', 201);
  } catch (error) {
    console.error('Error in receivableController.createReceivable:', error);
    const statusCode = error.message.includes('đã tồn tại') ? 409 : 400;
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Cập nhật receivable
 * PUT /api/receivables/:id
 */
const updateReceivable = async (req, res) => {
  try {
    const { id } = req.params;
    const receivableData = req.body;

    const updatedReceivable = await receivableService.updateReceivable(parseInt(id), receivableData);
    
    return successResponse(res, updatedReceivable, 'Cập nhật công nợ phải thu thành công');
  } catch (error) {
    console.error('Error in receivableController.updateReceivable:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    else if (error.message.includes('đã tồn tại')) statusCode = 409;
    else if (error.message.includes('không hợp lệ') || error.message.includes('Thiếu')) statusCode = 400;
    
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Xóa receivable
 * DELETE /api/receivables/:id
 */
const deleteReceivable = async (req, res) => {
  try {
    const { id } = req.params;
    await receivableService.deleteReceivable(parseInt(id));
    
    return successResponse(res, null, 'Xóa công nợ phải thu thành công');
  } catch (error) {
    console.error('Error in receivableController.deleteReceivable:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    else if (error.message.includes('không thể xóa')) statusCode = 400;
    
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Tạo receivables từ dữ liệu sản xuất
 * POST /api/receivables/bulk-create
 */
const createReceivablesFromProduction = async (req, res) => {
  try {
    const { contractId, startDate, endDate, invoicePrefix } = req.body;
    const createdBy = req.user?.id || 1; // Temporary fallback

    // Validate input
    if (!contractId || !startDate || !endDate) {
      return errorResponse(res, 'Thiếu thông tin bắt buộc: contractId, startDate, endDate', 400);
    }

    const options = {
      contractId: parseInt(contractId),
      startDate,
      endDate,
      invoicePrefix
    };

    const result = await receivableService.createReceivablesFromProduction(options, createdBy);
    
    return successResponse(res, result, 'Tạo công nợ từ dữ liệu sản xuất thành công', 201);
  } catch (error) {
    console.error('Error in receivableController.createReceivablesFromProduction:', error);
    return errorResponse(res, error.message, 400);
  }
};

/**
 * Validate receivable data middleware
 */
const validateReceivableData = (req, res, next) => {
  const {
    customer_id,
    contract_id,
    invoice_number,
    transaction_date,
    due_date,
    description,
    original_amount
  } = req.body;

  const errors = [];

  // Kiểm tra các trường bắt buộc
  if (!customer_id) errors.push('customer_id là bắt buộc');
  if (!contract_id) errors.push('contract_id là bắt buộc');
  if (!invoice_number) errors.push('invoice_number là bắt buộc');
  if (!transaction_date) errors.push('transaction_date là bắt buộc');
  if (!due_date) errors.push('due_date là bắt buộc');
  if (!description) errors.push('description là bắt buộc');
  if (!original_amount) errors.push('original_amount là bắt buộc');

  // Validate data types
  if (customer_id && isNaN(customer_id)) errors.push('customer_id phải là số');
  if (contract_id && isNaN(contract_id)) errors.push('contract_id phải là số');
  if (original_amount && (isNaN(original_amount) || original_amount <= 0)) {
    errors.push('original_amount phải là số dương');
  }

  // Validate dates
  if (transaction_date && isNaN(new Date(transaction_date).getTime())) {
    errors.push('transaction_date không hợp lệ');
  }
  if (due_date && isNaN(new Date(due_date).getTime())) {
    errors.push('due_date không hợp lệ');
  }

  // Validate due_date >= transaction_date
  if (transaction_date && due_date) {
    const transactionDateObj = new Date(transaction_date);
    const dueDateObj = new Date(due_date);
    if (dueDateObj < transactionDateObj) {
      errors.push('due_date không thể trước transaction_date');
    }
  }

  if (errors.length > 0) {
    return errorResponse(res, `Dữ liệu không hợp lệ: ${errors.join(', ')}`, 400);
  }

  next();
};

/**
 * Validate ID parameter middleware
 */
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(id) || parseInt(id) <= 0) {
    return errorResponse(res, 'ID không hợp lệ', 400);
  }
  
  next();
};

/**
 * Tạo receivable từ sản lượng đã chọn
 * POST /api/receivables/from-production
 */
const createReceivableFromProduction = async (req, res) => {
  try {
    const receivableData = req.body;
    const createdBy = req.user?.id || 1; // Temporary fallback

    console.log('=== CREATE RECEIVABLE FROM PRODUCTION ===');
    console.log('Request body:', JSON.stringify(receivableData, null, 2));
    console.log('Created by:', createdBy);

    const newReceivable = await receivableService.createReceivableFromProduction(receivableData, createdBy);

    return successResponse(res, newReceivable, 'Tạo công nợ từ sản lượng thành công', 201);
  } catch (error) {
    console.error('Error in receivableController.createReceivableFromProduction:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    else if (error.message.includes('đã tồn tại')) statusCode = 409;
    else if (error.message.includes('không hợp lệ') || error.message.includes('Thiếu')) statusCode = 400;

    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Validate receivable from production data middleware
 */
const validateReceivableFromProductionData = (req, res, next) => {
  console.log('=== VALIDATE RECEIVABLE FROM PRODUCTION DATA ===');
  console.log('Request body:', JSON.stringify(req.body, null, 2));

  const {
    customer_id,
    contract_id,
    invoice_number,
    transaction_date,
    due_date,
    description,
    original_amount,
    production_ids
  } = req.body;

  const errors = [];

  // Kiểm tra các trường bắt buộc
  if (!customer_id) errors.push('customer_id là bắt buộc');
  if (!contract_id) errors.push('contract_id là bắt buộc');
  if (!invoice_number) errors.push('invoice_number là bắt buộc');
  if (!transaction_date) errors.push('transaction_date là bắt buộc');
  if (!due_date) errors.push('due_date là bắt buộc');
  if (!description) errors.push('description là bắt buộc');
  if (!original_amount) errors.push('original_amount là bắt buộc');
  if (!production_ids || !Array.isArray(production_ids) || production_ids.length === 0) {
    errors.push('production_ids phải là mảng không rỗng');
  }

  // Validate data types
  if (customer_id && isNaN(customer_id)) errors.push('customer_id phải là số');
  if (contract_id && isNaN(contract_id)) errors.push('contract_id phải là số');
  if (original_amount && (isNaN(original_amount) || original_amount <= 0)) {
    errors.push('original_amount phải là số dương');
  }

  // Validate production_ids
  if (production_ids && Array.isArray(production_ids)) {
    for (let i = 0; i < production_ids.length; i++) {
      if (isNaN(production_ids[i])) {
        errors.push(`production_ids[${i}] phải là số`);
      }
    }
  }

  // Validate dates
  if (transaction_date && isNaN(new Date(transaction_date).getTime())) {
    errors.push('transaction_date không hợp lệ');
  }
  if (due_date && isNaN(new Date(due_date).getTime())) {
    errors.push('due_date không hợp lệ');
  }

  // Validate due_date >= transaction_date
  if (transaction_date && due_date) {
    const transactionDateObj = new Date(transaction_date);
    const dueDateObj = new Date(due_date);
    if (dueDateObj < transactionDateObj) {
      errors.push('due_date không thể trước transaction_date');
    }
  }

  if (errors.length > 0) {
    return errorResponse(res, `Dữ liệu không hợp lệ: ${errors.join(', ')}`, 400);
  }

  next();
};

module.exports = {
  getReceivables,
  getReceivableById,
  createReceivable,
  updateReceivable,
  deleteReceivable,
  createReceivablesFromProduction,
  createReceivableFromProduction,
  validateReceivableData,
  validateReceivableFromProductionData,
  validateIdParam
};
