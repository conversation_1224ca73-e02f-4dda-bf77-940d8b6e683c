import api from './api';

/**
 * Dashboard Service
 * Xử lý các API calls cho dashboard
 */

// Types for Dashboard API responses
export interface GeneralStats {
  total_customers: string;
  total_products: string;
  total_contracts: string;
  active_contracts: string;
  today_production_records: string;
  today_production_value: string;
}

export interface TodayProduction {
  total_quantity: string;
  total_value: string;
  product_types: string;
  contracts_involved: string;
  total_records: string;
}

export interface TopProduct {
  id: number;
  product_code: string;
  product_name: string;
  unit_type: string;
  total_quantity: string;
  total_value: string;
  production_records: string;
  contracts_count: string;
  production_days?: string;
}

export interface DebtSummary {
  customers_with_debt: string;
  total_receivables: string;
  total_overdue: string;
  overdue_invoices: string;
  active_invoices: string;
  avg_overdue_days: string;
  // Thêm thông tin chi tiết aging buckets
  current_amount: string;
  days_1_30: string;
  days_31_60: string;
  days_61_90: string;
  over_90_days: string;
}

export interface OverdueCustomer {
  customer_id: number;
  customer_name: string;
  short_name: string;
  tax_code: string;
  overdue_amount: string;
  overdue_invoices: string;
  oldest_overdue_date: string;
  max_days_overdue: string;
  total_outstanding: string;
  overdue_percentage: string;
  // Thêm thông tin chi tiết aging buckets
  days_1_30: string;
  days_31_60: string;
  days_61_90: string;
  over_90_days: string;
}

export interface DailyBreakdown {
  date: string;
  quantity: string;
  value: string;
  records_count: string;
  product_types: string;
}

export interface MonthlyProductionStats {
  month: number;
  year: number;
  total_quantity: string;
  total_value: string;
  comparison_with_previous_month: {
    previous_month: number;
    previous_year: number;
    quantity_change_percent: number;
    value_change_percent: number;
    previous_quantity: string;
    previous_value: string;
  };
  daily_breakdown: DailyBreakdown[];
  top_products: TopProduct[];
  production_days: number;
  non_production_days: number;
  total_days_in_month: number;
  total_records: number;
  product_types: number;
  contracts_involved: number;
}

export interface AllDashboardData {
  general_stats: GeneralStats;
  today_production: TodayProduction;
  top_products_today: TopProduct[];
  top_products_week: TopProduct[];
  debt_summary: DebtSummary;
  top_overdue_customers: OverdueCustomer[];
  monthly_production_stats: MonthlyProductionStats;
  last_updated: string;
}

const BASE_URL = '/dashboard';

const dashboardService = {
  /**
   * Lấy thống kê tổng quan hệ thống
   */
  async getGeneralStats(): Promise<GeneralStats> {
    try {
      const response = await api.get(`${BASE_URL}/stats`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching general stats:', error);
      throw error;
    }
  },

  /**
   * Lấy sản lượng hôm nay
   */
  async getTodayProduction(): Promise<TodayProduction> {
    try {
      const response = await api.get(`${BASE_URL}/production-today`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching today production:', error);
      throw error;
    }
  },

  /**
   * Lấy top sản phẩm
   */
  async getTopProducts(period: 'today' | 'week' | 'month' = 'today'): Promise<{ period: string; products: TopProduct[] }> {
    try {
      const response = await api.get(`${BASE_URL}/top-products?period=${period}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching top products:', error);
      throw error;
    }
  },

  /**
   * Lấy tóm tắt tình hình công nợ
   */
  async getDebtSummary(): Promise<DebtSummary> {
    try {
      const response = await api.get(`${BASE_URL}/debt-summary`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching debt summary:', error);
      throw error;
    }
  },

  /**
   * Lấy top khách hàng nợ quá hạn
   */
  async getTopOverdueCustomers(): Promise<OverdueCustomer[]> {
    try {
      const response = await api.get(`${BASE_URL}/top-overdue-customers`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching top overdue customers:', error);
      throw error;
    }
  },

  /**
   * Lấy thống kê sản lượng theo tháng
   */
  async getMonthlyProductionStats(month?: number, year?: number): Promise<MonthlyProductionStats> {
    try {
      const params = new URLSearchParams();
      if (month) params.append('month', month.toString());
      if (year) params.append('year', year.toString());
      
      const response = await api.get(`${BASE_URL}/monthly-production?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching monthly production stats:', error);
      throw error;
    }
  },

  /**
   * Lấy tất cả dữ liệu dashboard
   */
  async getAllDashboardData(): Promise<AllDashboardData> {
    try {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await api.get(`${BASE_URL}/all?_t=${timestamp}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching all dashboard data:', error);
      throw error;
    }
  }
};

export default dashboardService;
