/**
 * Production Page
 * Trang quản lý sản lượng sản xuất hàng ngày
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Snackbar,
} from '@mui/material';
import { useSearchParams } from 'react-router-dom';

// Components
import ProductionGroupTable from '../components/production/ProductionGroupTable';
import ProductionDialog from '../components/production/ProductionDialog';
import ProductionDetailDialog from '../components/production/ProductionDetailDialog';
import ConfirmDialog from '../components/common/ConfirmDialog';

// Types
import { GroupedProduction, DailyProductionFormData, DailyProductionFilterOptions } from '../types/dailyProduction';

// Services
import { dailyProductionService } from '../services/dailyProductionService';

const Production: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [productions, setProductions] = useState<GroupedProduction[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Pagination & Filtering
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState('production_date');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<DailyProductionFilterOptions>({});

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProduction, setSelectedProduction] = useState<GroupedProduction | null>(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dialogError, setDialogError] = useState<string | null>(null);

  // Toast
  const [toast, setToast] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load grouped productions
  const loadProductions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const options = {
        page: page + 1, // API sử dụng 1-based pagination
        limit: rowsPerPage,
        sortBy: orderBy,
        sortOrder: order,
        ...filters,
      };

      const response = await dailyProductionService.getGrouped(options);

      console.log('Grouped Production API Response:', response);

      // Xử lý response data
      if (response && response.success) {
        setProductions(Array.isArray(response.data) ? response.data : []);
        setTotalCount(response.meta?.pagination?.total || 0);
        console.log('Grouped productions loaded:', response.data?.length, 'items');
      } else {
        console.log('No data in response:', response);
        setProductions([]);
        setTotalCount(0);
      }
    } catch (err: any) {
      console.error('Error loading grouped productions:', err);

      // Xử lý lỗi
      const errorMessage = err.response?.data?.error?.message ||
                          err.response?.data?.message ||
                          err.message ||
                          'Có lỗi xảy ra khi tải danh sách sản lượng';

      setError(errorMessage);
      setProductions([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, orderBy, order, filters]);

  // Load productions when dependencies change
  useEffect(() => {
    loadProductions();
  }, [loadProductions]);

  // URL sync
  useEffect(() => {
    const params = new URLSearchParams();
    if (page > 0) params.set('page', (page + 1).toString());
    if (rowsPerPage !== 10) params.set('limit', rowsPerPage.toString());
    if (searchQuery) params.set('search', searchQuery);
    if (orderBy !== 'production_date') params.set('sortBy', orderBy);
    if (order !== 'desc') params.set('order', order);

    setSearchParams(params);
  }, [page, rowsPerPage, searchQuery, orderBy, order, setSearchParams]);

  // Event handlers
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  }, []);

  const handleSort = useCallback((property: string, direction: 'asc' | 'desc') => {
    setOrderBy(property);
    setOrder(direction);
    setPage(0);
  }, []);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(0);
  }, []);

  const handleFilter = useCallback((newFilters: DailyProductionFilterOptions) => {
    setFilters(newFilters);
    setPage(0);
  }, []);

  const handleAdd = useCallback(() => {
    setSelectedProduction(null);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleEdit = useCallback((production: GroupedProduction) => {
    setSelectedProduction(production);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleView = useCallback((production: GroupedProduction) => {
    setSelectedProduction(production);
    setDetailDialogOpen(true);
  }, []);

  const handleDelete = useCallback((production: GroupedProduction) => {
    setSelectedProduction(production);
    setDeleteDialogOpen(true);
  }, []);

  const handleDialogClose = useCallback(() => {
    setDialogOpen(false);
    setSelectedProduction(null);
    setDialogError(null);
  }, []);

  const handleDialogSubmit = useCallback(async (data: DailyProductionFormData) => {
    // TODO: Implement edit functionality for grouped production
    console.log('Edit grouped production:', selectedProduction, data);
    setToast({
      open: true,
      message: 'Chức năng chỉnh sửa sẽ được triển khai sau',
      severity: 'info',
    });
    handleDialogClose();
  }, [selectedProduction, handleDialogClose]);

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedProduction) return;

    // TODO: Implement delete functionality for grouped production
    console.log('Delete grouped production:', selectedProduction);
    setToast({
      open: true,
      message: 'Chức năng xóa sẽ được triển khai sau',
      severity: 'info',
    });
    setDeleteDialogOpen(false);
    setSelectedProduction(null);
  }, [selectedProduction]);



  const handleToastClose = useCallback(() => {
    setToast(prev => ({ ...prev, open: false }));
  }, []);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Quản lý sản lượng
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Xem sản lượng đã nhóm theo hợp đồng và ngày sản xuất
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <ProductionGroupTable
        productions={productions}
        loading={loading}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        orderBy={orderBy}
        order={order}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSort={handleSort}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        onRefresh={loadProductions}
      />

      {/* Production Dialog */}
      <ProductionDialog
        open={dialogOpen}
        production={selectedProduction}
        loading={dialogLoading}
        error={dialogError}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
      />

      {/* Production Detail Dialog */}
      <ProductionDetailDialog
        open={detailDialogOpen}
        production={selectedProduction}
        onClose={() => setDetailDialogOpen(false)}
      />



      {/* Delete Confirmation */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa sản lượng"
        message={`Bạn có chắc chắn muốn xóa tất cả sản lượng của hợp đồng ${selectedProduction?.contract_number} ngày ${selectedProduction?.production_date}? Hành động này không thể hoàn tác.`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
        confirmText="Xóa"
        cancelText="Hủy"
        severity="error"
      />

      {/* Toast Notifications */}
      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={handleToastClose} severity={toast.severity} sx={{ width: '100%' }}>
          {toast.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Production;
