import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Avatar,
  Button,
  Alert,
  Skeleton
} from '@mui/material';
import {
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon,
  LocalLaundryService as LaundryIcon,
  AccountBalance as DebtIcon
} from '@mui/icons-material';

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// import { useAuth } from '../contexts/AuthContext';
// import { formatDateTime } from '../utils/responseUtils';

// Services
import dashboardService, { AllDashboardData } from '../services/dashboardService';

// Utils
import { formatCurrencyToMillions } from '../utils/formatters';

// Components
import StatCard from '../components/common/StatCard';
import {
  KPICard,
  ProductionTodayWidget,
  TopProductsWidget,
  DebtSummaryWidget,
  MonthlyProductionWidget
} from '../components/dashboard';

/**
 * Dashboard Page Component
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 */
const Dashboard: React.FC = () => {
  // State management
  const [dashboardData, setDashboardData] = useState<AllDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock user data - thay thế cho authState khi authentication bị tắt
  const mockUser = {
    name: 'Admin',
    position: 'Quản trị viên',
    last_login: new Date().toISOString()
  };

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await dashboardService.getAllDashboardData();
        setDashboardData(data);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Không thể tải dữ liệu dashboard. Vui lòng thử lại sau.');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format currency for display - using unified formatter
  const formatCurrency = (value: string | number): string => {
    return formatCurrencyToMillions(value);
  };

  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toLocaleString('vi-VN');
  };

  return (
    <Box>
      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Welcome Section - Fresh & Clean Theme */}
      <Paper
        elevation={2}
        className="laundry-gradient-primary laundry-hover-lift"
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'linear-gradient(135deg, #0ea5e9 0%, #22c55e 100%)', // Sky Blue to Green
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* Background decorative elements */}
        <Box
          sx={{
            position: 'absolute',
            top: -50,
            right: -50,
            width: 200,
            height: 200,
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.1)',
            zIndex: 0,
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: -30,
            left: -30,
            width: 150,
            height: 150,
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.05)',
            zIndex: 0,
          }}
        />

        <Grid container spacing={3} alignItems="center" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid item xs={12} md={8}>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 1 }}>
              Xin chào, {mockUser.name}! 👋
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9, mb: 2 }}>
              Chào mừng bạn đến với hệ thống quản lý giặt là.
              Hãy bắt đầu bằng cách thêm khách hàng mới hoặc xem danh sách hợp đồng hiện có.
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              Cập nhật lần cuối: {dashboardData?.last_updated ? new Date(dashboardData.last_updated).toLocaleString('vi-VN') : 'Đang tải...'}
            </Typography>
          </Grid>
          <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                fontSize: '2rem',
                bgcolor: 'rgba(255, 255, 255, 0.2)',
                mx: 'auto',
                mb: 2,
              }}
            >
              {mockUser.name.charAt(0).toUpperCase()}
            </Avatar>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              {mockUser.position}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* KPI Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <KPICard
            title="Khách hàng"
            value={dashboardData?.general_stats.total_customers || '0'}
            subtitle="Tổng số khách hàng"
            icon={<PeopleIcon />}
            color="primary"
            loading={loading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <KPICard
            title="Sản phẩm"
            value={dashboardData?.general_stats.total_products || '0'}
            subtitle="Loại sản phẩm"
            icon={<TrendingUpIcon />}
            color="success"
            loading={loading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <KPICard
            title="Hợp đồng"
            value={dashboardData?.general_stats.active_contracts || '0'}
            subtitle="Đang hoạt động"
            icon={<BusinessIcon />}
            color="info"
            loading={loading}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <KPICard
            title="Sản lượng hôm nay"
            value={dashboardData ? formatCurrency(dashboardData.general_stats.today_production_value) : '0'}
            subtitle="Giá trị sản xuất"
            icon={<LaundryIcon />}
            color="warning"
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Dashboard Widgets */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Production Today Widget */}
        <Grid item xs={12} md={6}>
          <ProductionTodayWidget
            data={dashboardData?.today_production || null}
            loading={loading}
          />
        </Grid>

        {/* Debt Summary Widget */}
        <Grid item xs={12} md={6}>
          <DebtSummaryWidget
            debtSummary={dashboardData?.debt_summary || null}
            topOverdueCustomers={dashboardData?.top_overdue_customers || null}
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Top Products and Monthly Production */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Top Products Today */}
        <Grid item xs={12} md={6}>
          <TopProductsWidget
            data={dashboardData?.top_products_today || null}
            title="Top 5 sản phẩm"
            period="hôm nay"
            loading={loading}
          />
        </Grid>

        {/* Top Products Week */}
        <Grid item xs={12} md={6}>
          <TopProductsWidget
            data={dashboardData?.top_products_week || null}
            title="Top 5 sản phẩm"
            period="tuần này"
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Monthly Production Chart */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <MonthlyProductionWidget
            data={dashboardData?.monthly_production_stats || null}
            loading={loading}
          />
        </Grid>
      </Grid>

      {/* Quick Actions - Compact Version */}
      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12}>
          <Card
            elevation={2}
            className="laundry-hover-lift"
            sx={{
              background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
              border: '1px solid rgba(14, 165, 233, 0.1)',
              borderRadius: 2,
            }}
          >
            <CardContent sx={{ py: 2 }}>
              <Typography variant="h6" component="h2" sx={{ mb: 2, fontWeight: 600, color: 'primary.main', fontSize: '1rem' }}>
                Thao tác nhanh
              </Typography>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<BusinessIcon />}
                  href="/contracts"
                  size="small"
                  sx={{ fontSize: '0.8rem' }}
                >
                  Quản lý hợp đồng
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<TrendingUpIcon />}
                  href="/products"
                  size="small"
                  sx={{ fontSize: '0.8rem' }}
                >
                  Quản lý sản phẩm
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<LaundryIcon />}
                  href="/production"
                  size="small"
                  sx={{ fontSize: '0.8rem' }}
                >
                  Nhập sản lượng
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DebtIcon />}
                  href="/receivables"
                  size="small"
                  sx={{ fontSize: '0.8rem' }}
                >
                  Quản lý công nợ
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
