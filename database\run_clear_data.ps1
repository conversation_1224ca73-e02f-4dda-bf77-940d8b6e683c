# PowerShell script để backup và xóa dữ liệu
# Chạy script này để thực hiện toàn bộ quy trình

Write-Host "=== SCRIPT XÓA DỮ LIỆU ĐỂ CHUẨN BỊ PRODUCTION ===" -ForegroundColor Green
Write-Host ""

# Thiết lập biến môi trường
$env:PGPASSWORD = "110591"
$env:PGCLIENTENCODING = "UTF8"

# Đường dẫn database
$dbHost = "localhost"
$dbPort = "5432"
$dbName = "tinhtam-hp"
$dbUser = "postgres"

Write-Host "Thông tin kết nối:" -ForegroundColor Yellow
Write-Host "- Host: $dbHost"
Write-Host "- Port: $dbPort"
Write-Host "- Database: $dbName"
Write-Host "- User: $dbUser"
Write-Host ""

# Kiểm tra kết nối database
Write-Host "Bước 1: Ki<PERSON>m tra kết nối database..." -ForegroundColor Cyan
try {
    $testConnection = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "SELECT current_database(), version();" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Kết nối database thành công!" -ForegroundColor Green
    } else {
        Write-Host "❌ Lỗi kết nối database:" -ForegroundColor Red
        Write-Host $testConnection
        exit 1
    }
} catch {
    Write-Host "❌ Không thể kết nối database: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Xác nhận từ người dùng
Write-Host "⚠️  CẢNH BÁO: Script này sẽ XÓA TẤT CẢ DỮ LIỆU TEST/DEMO!" -ForegroundColor Red
Write-Host "Chỉ giữ lại:" -ForegroundColor Yellow
Write-Host "- Cấu trúc bảng và ràng buộc"
Write-Host "- Tài khoản admin để đăng nhập"
Write-Host "- Views, functions, triggers"
Write-Host ""

$confirmation = Read-Host "Bạn có chắc chắn muốn tiếp tục? (yes/no)"
if ($confirmation -ne "yes") {
    Write-Host "❌ Hủy bỏ thực hiện." -ForegroundColor Red
    exit 0
}

Write-Host ""

# Bước 2: Backup dữ liệu
Write-Host "Bước 2: Backup dữ liệu hiện tại..." -ForegroundColor Cyan
try {
    $backupResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/backup_before_clear.sql" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backup dữ liệu thành công!" -ForegroundColor Green
    } else {
        Write-Host "❌ Lỗi backup dữ liệu:" -ForegroundColor Red
        Write-Host $backupResult
        exit 1
    }
} catch {
    Write-Host "❌ Không thể backup dữ liệu: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Bước 3: Xóa dữ liệu
Write-Host "Bước 3: Xóa dữ liệu test/demo..." -ForegroundColor Cyan
try {
    $clearResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/clear_data_for_production.sql" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Xóa dữ liệu thành công!" -ForegroundColor Green
    } else {
        Write-Host "❌ Lỗi xóa dữ liệu:" -ForegroundColor Red
        Write-Host $clearResult
        
        # Hỏi có muốn khôi phục không
        $restore = Read-Host "Có muốn khôi phục dữ liệu từ backup? (yes/no)"
        if ($restore -eq "yes") {
            Write-Host "Đang khôi phục dữ liệu..." -ForegroundColor Yellow
            psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/restore_backup.sql"
        }
        exit 1
    }
} catch {
    Write-Host "❌ Không thể xóa dữ liệu: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Bước 4: Kiểm tra kết quả
Write-Host "Bước 4: Kiểm tra kết quả..." -ForegroundColor Cyan
try {
    $checkResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
    SELECT 
        'users' as table_name, COUNT(*) as records FROM users
    UNION ALL
    SELECT 
        'customers' as table_name, COUNT(*) as records FROM customers
    UNION ALL
    SELECT 
        'products' as table_name, COUNT(*) as records FROM products
    UNION ALL
    SELECT 
        'contracts' as table_name, COUNT(*) as records FROM contracts
    UNION ALL
    SELECT 
        'daily_production' as table_name, COUNT(*) as records FROM daily_production;
    " 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "📊 Thống kê dữ liệu sau khi xóa:" -ForegroundColor Green
        Write-Host $checkResult
    }
} catch {
    Write-Host "⚠️  Không thể kiểm tra kết quả, nhưng quá trình xóa đã hoàn thành." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 HOÀN THÀNH!" -ForegroundColor Green
Write-Host ""
Write-Host "Kết quả:" -ForegroundColor Yellow
Write-Host "✅ Dữ liệu test/demo đã được xóa"
Write-Host "✅ Cấu trúc database được giữ nguyên"
Write-Host "✅ Tài khoản admin được giữ lại"
Write-Host "✅ Backup được lưu trong schema backup_data"
Write-Host ""
Write-Host "Thông tin đăng nhập:" -ForegroundColor Cyan
Write-Host "- Email: <EMAIL>"
Write-Host "- Password: 123456"
Write-Host ""
Write-Host "Luu y: Nen doi password admin sau khi production!" -ForegroundColor Red
Write-Host ""
Write-Host "De khoi phuc du lieu neu can:" -ForegroundColor Yellow
Write-Host "psql -U postgres -d tinhtam-hp -f database/restore_backup.sql"
