import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Button,
  Alert
} from '@mui/material';
import contractService from '../../services/contractService';

const TestContractSelect: React.FC = () => {
  const [contracts, setContracts] = useState<any[]>([]);
  const [selectedContract, setSelectedContract] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const loadContracts = async () => {
    try {
      setLoading(true);
      setError('');
      console.log('Loading contracts...');
      
      const response = await contractService.getContracts({ limit: 100 });
      console.log('Raw response:', response);
      
      let contractsArray = [];
      if (response.data && Array.isArray(response.data)) {
        contractsArray = response.data;
      } else if (response.contracts && Array.isArray(response.contracts)) {
        contractsArray = response.contracts;
      } else if (Array.isArray(response)) {
        contractsArray = response;
      }
      
      console.log('Processed contracts:', contractsArray);
      setContracts(contractsArray);
      
    } catch (error: any) {
      console.error('Error loading contracts:', error);
      setError(error.message || 'Không thể tải danh sách hợp đồng');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadContracts();
  }, []);

  const handleChange = (event: any) => {
    const value = event.target.value;
    console.log('Selected value:', value);
    setSelectedContract(value);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 600 }}>
      <Typography variant="h5" sx={{ mb: 3 }}>
        Test Contract Select
      </Typography>
      
      <Button onClick={loadContracts} sx={{ mb: 2 }}>
        Reload Contracts
      </Button>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <FormControl fullWidth size="small">
        <InputLabel>Hợp đồng</InputLabel>
        <Select
          value={selectedContract}
          onChange={handleChange}
          label="Hợp đồng"
          disabled={loading}
        >
          <MenuItem value="">
            {loading ? 'Đang tải...' : 'Chọn hợp đồng'}
          </MenuItem>
          {contracts.map((contract) => (
            <MenuItem key={contract.id} value={contract.id.toString()}>
              {contract.contract_number} - {contract.contract_name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="body2">
          Selected: {selectedContract}
        </Typography>
        <Typography variant="body2">
          Contracts count: {contracts.length}
        </Typography>
        <Typography variant="body2">
          Loading: {loading ? 'Yes' : 'No'}
        </Typography>
      </Box>
      
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2">Debug Info:</Typography>
        <pre style={{ fontSize: '12px', background: '#f5f5f5', padding: '8px' }}>
          {JSON.stringify(contracts.slice(0, 2), null, 2)}
        </pre>
      </Box>
    </Box>
  );
};

export default TestContractSelect;
