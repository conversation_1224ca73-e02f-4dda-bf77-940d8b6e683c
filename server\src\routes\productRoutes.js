const express = require('express');
const { body, param, query } = require('express-validator');
const productController = require('../controllers/productController');
const { authenticateToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

const router = express.Router();

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// Middleware xác thực cho tất cả routes
// router.use(authenticateToken);

/**
 * @route   GET /api/v1/products/unit-types
 * @desc    Lấy danh sách unit types có sẵn
 * @access  Private
 */
router.get('/unit-types', [
  // NOTE: Tạ<PERSON> thời comment out permission check
  // checkPermission('product', 'view')
], productController.getUnitTypes);

/**
 * @route   GET /api/v1/products/next-code
 * @desc    Lấy mã sản phẩm tiếp theo
 * @access  Private
 */
router.get('/next-code', [
  // NOTE: <PERSON><PERSON><PERSON> thời comment out permission check
  // checkPermission('product', 'view')
], productController.getNextProductCode);

/**
 * @route   GET /api/v1/products/check-name
 * @desc    Kiểm tra tên sản phẩm đã tồn tại
 * @access  Private
 */
router.get('/check-name', [
  query('name')
    .notEmpty()
    .withMessage('Tên sản phẩm không được để trống')
    .isLength({ min: 2, max: 200 })
    .withMessage('Tên sản phẩm phải từ 2 đến 200 ký tự'),
  query('excludeId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID loại trừ phải là số nguyên dương'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'view')
], productController.checkProductName);

/**
 * @route   GET /api/v1/products/search
 * @desc    Tìm kiếm sản phẩm
 * @access  Private
 */
router.get('/search', [
  query('q')
    .notEmpty()
    .withMessage('Từ khóa tìm kiếm không được để trống')
    .isLength({ min: 2, max: 255 })
    .withMessage('Từ khóa tìm kiếm phải từ 2 đến 255 ký tự'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Số lượng kết quả phải từ 1 đến 50'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'view')
], productController.searchProducts);

/**
 * @route   GET /api/v1/products
 * @desc    Lấy danh sách sản phẩm với phân trang và tìm kiếm
 * @access  Private
 * @query   page - Số trang (default: 1)
 * @query   limit - Số lượng items per page (default: 10, max: 1000)
 * @query   search - Từ khóa tìm kiếm
 * @query   unit_type - Filter theo loại đơn vị
 * @query   is_active - Filter theo trạng thái (true/false)
 * @query   sort - Cột để sắp xếp (default: created_at)
 * @query   order - Thứ tự sắp xếp ASC/DESC (default: DESC)
 */
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Số trang phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Số lượng items phải từ 1 đến 1000'),
  query('sort')
    .optional()
    .isIn(['id', 'code', 'name', 'unit_type', 'is_active', 'created_at', 'updated_at'])
    .withMessage('Cột sắp xếp không hợp lệ'),
  query('order')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Thứ tự sắp xếp phải là ASC hoặc DESC'),
  query('search')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Từ khóa tìm kiếm không được quá 255 ký tự'),
  query('unit_type')
    .optional()
    .isIn(['piece', 'kg', 'set', 'meter', 'liter', 'box'])
    .withMessage('Loại đơn vị không hợp lệ'),
  query('is_active')
    .optional()
    .isIn(['true', 'false'])
    .withMessage('Trạng thái phải là true hoặc false'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'view')
], productController.getAllProducts);

/**
 * @route   GET /api/v1/products/:id
 * @desc    Lấy thông tin sản phẩm theo ID
 * @access  Private
 */
router.get('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'view')
], productController.getProductById);

/**
 * @route   POST /api/v1/products
 * @desc    Tạo sản phẩm mới (mã sản phẩm sẽ được tự động generate)
 * @access  Private (Manager+)
 */
router.post('/', [
  // Loại bỏ validation cho code vì sẽ tự động generate
  body('name')
    .notEmpty()
    .withMessage('Tên sản phẩm không được để trống')
    .isLength({ min: 1, max: 200 })
    .withMessage('Tên sản phẩm phải từ 1 đến 200 ký tự')
    .trim(),
  body('description')
    .optional({ nullable: true })
    .isLength({ max: 1000 })
    .withMessage('Mô tả không được quá 1000 ký tự')
    .trim(),
  body('unit_type')
    .notEmpty()
    .withMessage('Loại đơn vị không được để trống')
    .isIn(['piece', 'kg', 'set', 'meter', 'liter', 'box'])
    .withMessage('Loại đơn vị phải là một trong: piece, kg, set, meter, liter, box'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'create')
], productController.createProduct);

/**
 * @route   PUT /api/v1/products/:id
 * @desc    Cập nhật thông tin sản phẩm
 * @access  Private (Manager+)
 */
router.put('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('name')
    .notEmpty()
    .withMessage('Tên sản phẩm không được để trống')
    .isLength({ min: 1, max: 200 })
    .withMessage('Tên sản phẩm phải từ 1 đến 200 ký tự')
    .trim(),
  body('description')
    .optional({ nullable: true })
    .isLength({ max: 1000 })
    .withMessage('Mô tả không được quá 1000 ký tự')
    .trim(),
  body('unit_type')
    .notEmpty()
    .withMessage('Loại đơn vị không được để trống')
    .isIn(['piece', 'kg', 'set', 'meter', 'liter', 'box'])
    .withMessage('Loại đơn vị phải là một trong: piece, kg, set, meter, liter, box'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('Trạng thái phải là true hoặc false'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'edit')
], productController.updateProduct);

/**
 * @route   DELETE /api/v1/products/:id
 * @desc    Xóa sản phẩm (soft delete)
 * @access  Private (Admin only)
 */
router.delete('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  // NOTE: Tạm thời comment out permission check
  // checkPermission('product', 'delete')
], productController.deleteProduct);

module.exports = router;
