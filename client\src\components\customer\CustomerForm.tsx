import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Grid,
  Typography,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Customer, CustomerFormData, CUSTOMER_FORM_FIELDS } from '../../types/customer';
import { customerService } from '../../services/customerService';

/**
 * Customer Form Props Interface
 */
export interface CustomerFormProps {
  customer?: Customer | null;
  mode: 'create' | 'edit';
  loading?: boolean;
  onSubmit: (data: CustomerFormData) => void;
  onValidationChange?: (isValid: boolean) => void;
}

/**
 * Customer Form Component
 * Reusable form for creating and editing customers
 */
const CustomerForm: React.FC<CustomerFormProps> = ({
  customer,
  mode,
  loading = false,
  onSubmit,
  onValidationChange,
}) => {
  // Form state
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    tax_code: '',
    short_name: '',
    address: '',
    contact_person: '',
    phone: '',
    email: '',
  });

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  /**
   * Initialize form data when customer prop changes
   */
  useEffect(() => {
    if (customer && mode === 'edit') {
      setFormData({
        name: customer.name || '',
        tax_code: customer.tax_code || '',
        short_name: customer.short_name || '',
        address: customer.address || '',
        contact_person: customer.contact_person || '',
        phone: customer.phone || '',
        email: customer.email || '',
      });
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        tax_code: '',
        short_name: '',
        address: '',
        contact_person: '',
        phone: '',
        email: '',
      });
    }
    setErrors({});
    setTouched({});
  }, [customer, mode]);

  /**
   * Validate form data
   */
  const validateForm = (data: CustomerFormData): Record<string, string> => {
    const validationResult = customerService.validateCustomerData(data);
    const fieldErrors: Record<string, string> = {};

    if (!validationResult.isValid) {
      validationResult.errors.forEach(error => {
        // Map error messages to field names
        if (error.includes('Tên khách hàng')) {
          fieldErrors['name'] = error;
        } else if (error.includes('Mã số thuế')) {
          fieldErrors['tax_code'] = error;
        } else if (error.includes('Tên viết tắt')) {
          fieldErrors['short_name'] = error;
        } else if (error.includes('Địa chỉ')) {
          fieldErrors['address'] = error;
        } else if (error.includes('Người liên hệ')) {
          fieldErrors['contact_person'] = error;
        } else if (error.includes('Số điện thoại')) {
          fieldErrors['phone'] = error;
        } else if (error.includes('Email')) {
          fieldErrors['email'] = error;
        }
      });
    }

    return fieldErrors;
  };

  /**
   * Handle input change
   */
  const handleInputChange = (field: keyof CustomerFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    const newFormData = { ...formData, [field]: value };
    setFormData(newFormData);

    // Validate on change if field was touched
    if (touched[field]) {
      const newErrors = validateForm(newFormData);
      setErrors(newErrors);

      // Notify parent about validation status
      if (onValidationChange) {
        onValidationChange(Object.keys(newErrors).length === 0);
      }
    }
  };

  /**
   * Handle input blur
   */
  const handleInputBlur = (field: keyof CustomerFormData) => () => {
    setTouched({ ...touched, [field]: true });
    const newErrors = validateForm(formData);
    setErrors(newErrors);

    // Notify parent about validation status
    if (onValidationChange) {
      onValidationChange(Object.keys(newErrors).length === 0);
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    // Mark all fields as touched
    const allTouched = CUSTOMER_FORM_FIELDS.reduce((acc, field) => {
      acc[field.name] = true;
      return acc;
    }, {} as Record<string, boolean>);
    setTouched(allTouched);

    // Validate form
    const formErrors = validateForm(formData);
    setErrors(formErrors);

    // Submit if no errors
    if (Object.keys(formErrors).length === 0) {
      onSubmit(formData);
    }
  };

  /**
   * Get field error message
   */
  const getFieldError = (fieldName: string): string => {
    return touched[fieldName] && errors[fieldName] ? errors[fieldName] : '';
  };

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      {/* Form Title */}
      <Typography
        variant="h6"
        sx={{
          mb: 3,
          fontSize: '1.1rem',
          fontWeight: 600,
          color: 'text.primary',
        }}
      >
        {mode === 'create' ? 'Thêm khách hàng mới' : 'Chỉnh sửa thông tin khách hàng'}
      </Typography>

      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 1,
          }}
        >
          <CircularProgress size={40} />
        </Box>
      )}

      {/* Form Fields */}
      <Grid container spacing={3}>
        {CUSTOMER_FORM_FIELDS.map((field) => {
          // Xác định grid size dựa trên field name
          let gridSize = { xs: 12, sm: 6 }; // Default

          if (field.name === 'name') {
            gridSize = { xs: 12 }; // Tên khách hàng - full width
          } else if (field.name === 'tax_code' || field.name === 'short_name') {
            gridSize = { xs: 12, sm: 6 }; // Mã số thuế và tên viết tắt - 6 columns
          } else if (field.name === 'address') {
            gridSize = { xs: 12 }; // Địa chỉ - full width
          } else if (field.name === 'contact_person' || field.name === 'phone' || field.name === 'email') {
            gridSize = { xs: 12, sm: 4 }; // Người liên hệ, SĐT, Email - 4 columns
          }

          return (
            <Grid item {...gridSize} key={field.name}>
              <TextField
                fullWidth
                label={field.label}
                placeholder={field.placeholder}
                value={formData[field.name] || ''}
                onChange={handleInputChange(field.name)}
                onBlur={handleInputBlur(field.name)}
                error={!!getFieldError(field.name)}
                helperText={getFieldError(field.name) || field.helperText}
                required={field.required}
                type={field.type}
                multiline={field.multiline}
                rows={field.rows}
                disabled={loading}
                inputProps={{
                  maxLength: field.maxLength,
                }}
                size="medium"
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: '0.9rem',
                    minHeight: '48px', // Tăng chiều cao input cho thoải mái hơn
                  },
                  '& .MuiInputLabel-root': {
                    fontSize: '0.9rem',
                  },
                  '& .MuiFormHelperText-root': {
                    fontSize: '0.8rem',
                  },
                }}
              />
            </Grid>
          );
        })}
      </Grid>

      {/* General Error Message */}
      {Object.keys(errors).length > 0 && Object.keys(touched).length > 0 && (
        <Alert severity="error" sx={{ mt: 2, fontSize: '0.85rem' }}>
          Vui lòng kiểm tra lại thông tin đã nhập
        </Alert>
      )}
    </Box>
  );
};

export default CustomerForm;
