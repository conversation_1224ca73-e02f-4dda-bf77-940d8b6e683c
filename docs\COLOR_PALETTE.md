# Laundry Management System - Color Palette

## 🎨 "Fresh & Clean" Theme

This color palette is specifically designed for the laundry management system, emphasizing cleanliness, freshness, and eco-friendliness.

## Primary Colors

### Sky Blue - Cleanliness & Freshness
- **Main**: `#0ea5e9` - Represents cleanliness and fresh water
- **Light**: `#38bdf8` - Light sky blue for hover states
- **Dark**: `#0284c7` - Darker blue for pressed states
- **Usage**: Primary buttons, links, active states, brand elements

### Green - Eco-friendly & Natural
- **Main**: `#22c55e` - Represents eco-friendliness and nature
- **Light**: `#4ade80` - Light green for success states
- **Dark**: `#16a34a` - Dark green for emphasis
- **Usage**: Secondary buttons, success indicators, eco-friendly features

### Cyan - Fresh & Information
- **Main**: `#06b6d4` - Fresh and informational
- **Light**: `#22d3ee` - Light cyan for highlights
- **Dark**: `#0891b2` - Dark cyan for depth
- **Usage**: Information alerts, accent elements, fresh indicators

## Background Colors

### Clean & Minimal
- **Default**: `#f8fafc` - Very light gray for main background
- **Paper**: `#ffffff` - Pure white for cards and forms
- **Surface**: `#f1f5f9` - Light gray for surface elements

## Text Colors

### Professional & Readable
- **Primary**: `#1e293b` - Dark slate for main text
- **Secondary**: `#475569` - Medium slate for secondary text
- **Disabled**: `#94a3b8` - Light slate for disabled text

## Status Colors

### Semantic Colors
- **Success**: `#22c55e` - Fresh green for completed/clean status
- **Warning**: `#f59e0b` - Warm orange for attention needed
- **Error**: `#ef4444` - Clean red for errors/issues
- **Info**: `#06b6d4` - Cyan for information/fresh status

## WCAG Accessibility Compliance

All color combinations meet WCAG 2.1 AA standards:

### Contrast Ratios
- **Primary text on white**: 12.6:1 (AAA)
- **Secondary text on white**: 7.8:1 (AAA)
- **Primary button text**: 4.8:1 (AA)
- **Secondary button text**: 4.5:1 (AA)

## Usage Guidelines

### Do's ✅
- Use sky blue for primary actions and brand elements
- Use green for eco-friendly features and success states
- Use cyan for information and fresh indicators
- Maintain sufficient contrast for accessibility
- Use white backgrounds for clean, minimal feel

### Don'ts ❌
- Don't use colors that conflict with the clean aesthetic
- Don't use low contrast combinations
- Don't overuse bright colors - maintain balance
- Don't use colors that suggest dirt or uncleanliness

## Implementation

### CSS Variables
```css
:root {
  --primary-main: #0ea5e9;
  --primary-light: #38bdf8;
  --primary-dark: #0284c7;
  --secondary-main: #22c55e;
  --secondary-light: #4ade80;
  --secondary-dark: #16a34a;
  --accent-main: #06b6d4;
  --accent-light: #22d3ee;
  --accent-dark: #0891b2;
}
```

### MUI Theme
The colors are implemented in the MUI theme configuration:
- `client/src/theme.ts`
- `frontend/src/theme/index.ts`

### Custom CSS
Additional styling is available in:
- `client/src/styles/laundry-theme.css`

## Brand Association

This color palette creates associations with:
- 💧 **Clean water** (Sky Blue)
- 🌿 **Eco-friendliness** (Green)
- ✨ **Freshness** (Cyan)
- 🏢 **Professionalism** (Dark Slate)
- 🧼 **Cleanliness** (White/Light Gray)

Perfect for a laundry management system that emphasizes quality, cleanliness, and environmental responsibility.
