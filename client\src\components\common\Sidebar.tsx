import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  useTheme,
  useMediaQuery,
  Collapse,
  Toolbar
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  ChevronLeft as ChevronLeftIcon,
  ExpandLess,
  ExpandMore,
  Person as PersonIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
  MarkEmailRead as MarkEmailReadIcon,
  ViewList as ViewListIcon,
  CloudUpload as CloudUploadIcon,
  Business as BusinessIcon,
  FormatListBulleted as FormatListBulletedIcon,
  WorkOutline as WorkOutlineIcon,
  MenuOpen as MenuOpenIcon,
  ChevronRight as ChevronRightIcon,
  Api as ApiIcon,
  Inventory as InventoryIcon,
  Article as ArticleIcon,
  AttachMoney as PricingIcon,
  Factory as ProductionIcon,
  AccountBalance as ReceivablesIcon,
  CheckCircle as ConfirmIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
// import { useAuth } from '../../contexts/AuthContext';
// import PermissionCheck from '../permission/PermissionCheck';

// Giảm chiều rộng sidebar từ 260px xuống 240px
const drawerWidth = 240;

interface SidebarProps {
  children: React.ReactNode;
}

const Sidebar: React.FC<SidebarProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  // const { user, logout } = useAuth();
  const user = { name: 'Demo User', email: '<EMAIL>' }; // Mock user for development
  const logout = () => console.log('Logout disabled during development');
  const navigate = useNavigate();
  const [anchorElUser, setAnchorElUser] = useState<null | HTMLElement>(null);
  const [contractMenuOpen, setContractMenuOpen] = useState(true); // State for contract submenu

  // Xử lý toggle sidebar trên mobile
  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  // Xử lý ẩn/hiện sidebar
  const handleToggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
    // Lưu trạng thái vào localStorage để giữ nguyên khi refresh
    localStorage.setItem('sidebarCollapsed', (!sidebarCollapsed).toString());
  };

  // Đọc trạng thái từ localStorage khi component được mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState !== null) {
      setSidebarCollapsed(savedState === 'true');
    }
  }, []);

  const handleOpenUserMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorElUser(event.currentTarget);
  };

  const handleCloseUserMenu = () => {
    setAnchorElUser(null);
  };

  const handleLogout = () => {
    logout();
    handleCloseUserMenu();
  };

  const handleNavigate = (path: string) => {
    navigate(path);
    if (isMobile) {
      setMobileOpen(false);
    }
  };

  const handleToggleContractMenu = () => {
    setContractMenuOpen(!contractMenuOpen);
  };

  const drawer = (
    <>
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          px: [1],
          py: 0.5, // Giảm padding theo chiều dọc
          minHeight: '48px', // Giảm chiều cao tối thiểu
          background: 'linear-gradient(135deg, #0ea5e9 0%, #22c55e 100%)', // Fresh & Clean gradient
          color: 'white',
        }}
      >
        <Typography
          variant="subtitle1" // Giảm kích thước từ h6 xuống subtitle1
          noWrap
          component="div"
          sx={{
            fontWeight: 700,
            display: 'flex',
            alignItems: 'center',
            fontSize: '0.9rem', // Giảm kích thước font
          }}
        >
          <BusinessIcon sx={{ mr: 0.5, fontSize: '1.2rem' }} /> Quản lý Giặt Là
        </Typography>
        {isMobile && (
          <IconButton onClick={handleDrawerToggle} sx={{ color: 'white', padding: '4px' }}>
            <ChevronLeftIcon fontSize="small" />
          </IconButton>
        )}
      </Toolbar>
      <Divider />
      <Box sx={{ overflow: 'auto', height: '100%', display: 'flex', flexDirection: 'column' }}>
        <List component="nav" sx={{ flexGrow: 1, px: 1.5, py: 0.25 }}>
          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36, // Giảm chiều cao tối thiểu xuống 36px
                px: 1.5, // Giữ nguyên padding ngang
                py: 0.25, // Giảm padding dọc xuống 0.25
                borderRadius: 1.5, // Giữ nguyên border radius
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/dashboard')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5, // Giữ nguyên margin
                  color: 'primary.main',
                  fontSize: '1.1rem', // Giảm kích thước icon thêm chút nữa
                }}
              >
                <DashboardIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Dashboard"
                primaryTypographyProps={{ fontSize: '0.85rem' }} // Giảm kích thước font xuống 0.85rem
              />
            </ListItemButton>
          </ListItem>

          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36,
                px: 1.5,
                py: 0.25,
                borderRadius: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/customers')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5,
                  color: 'primary.main',
                  fontSize: '1.1rem',
                }}
              >
                <BusinessIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Quản lý Khách hàng"
                primaryTypographyProps={{ fontSize: '0.85rem' }}
              />
            </ListItemButton>
          </ListItem>

          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36,
                px: 1.5,
                py: 0.25,
                borderRadius: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/products')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5,
                  color: 'primary.main',
                  fontSize: '1.1rem',
                }}
              >
                <InventoryIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Quản lý Sản phẩm"
                primaryTypographyProps={{ fontSize: '0.85rem' }}
              />
            </ListItemButton>
          </ListItem>

          {/* Contract Management with nested submenu */}
          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36,
                px: 1.5,
                py: 0.25,
                borderRadius: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/contracts')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5,
                  color: 'primary.main',
                  fontSize: '1.1rem',
                }}
              >
                <ArticleIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Quản lý Hợp đồng"
                primaryTypographyProps={{ fontSize: '0.85rem' }}
              />
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleContractMenu();
                }}
                sx={{
                  ml: 'auto',
                  p: 0.25,
                  color: 'text.secondary'
                }}
              >
                {contractMenuOpen ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
              </IconButton>
            </ListItemButton>
          </ListItem>

          {/* Nested submenu items */}
          <Collapse in={contractMenuOpen} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
                <ListItemButton
                  sx={{
                    minHeight: 32,
                    px: 1.5,
                    py: 0.25,
                    pl: 4, // Indent child items
                    borderRadius: 1.5,
                    '&:hover': {
                      backgroundColor: 'rgba(99, 102, 241, 0.08)',
                    },
                  }}
                  onClick={() => handleNavigate('/pricing')}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: 1.5,
                      color: 'text.secondary',
                      fontSize: '1rem',
                    }}
                  >
                    <PricingIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Quản lý Đơn giá"
                    primaryTypographyProps={{
                      fontSize: '0.8rem',
                      color: 'text.secondary'
                    }}
                  />
                </ListItemButton>
              </ListItem>

              <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
                <ListItemButton
                  sx={{
                    minHeight: 32,
                    px: 1.5,
                    py: 0.25,
                    pl: 4, // Indent child items
                    borderRadius: 1.5,
                    '&:hover': {
                      backgroundColor: 'rgba(99, 102, 241, 0.08)',
                    },
                  }}
                  onClick={() => handleNavigate('/production')}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: 1.5,
                      color: 'text.secondary',
                      fontSize: '1rem',
                    }}
                  >
                    <ProductionIcon fontSize="small" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Quản lý Sản lượng"
                    primaryTypographyProps={{
                      fontSize: '0.8rem',
                      color: 'text.secondary'
                    }}
                  />
                </ListItemButton>
              </ListItem>
            </List>
          </Collapse>

          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36,
                px: 1.5,
                py: 0.25,
                borderRadius: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/production-confirmation')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5,
                  color: 'primary.main',
                  fontSize: '1.1rem',
                }}
              >
                <ConfirmIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Xác nhận Sản lượng"
                primaryTypographyProps={{ fontSize: '0.85rem' }}
              />
            </ListItemButton>
          </ListItem>

          <ListItem disablePadding sx={{ display: 'block', mb: 0.25 }}>
            <ListItemButton
              sx={{
                minHeight: 36,
                px: 1.5,
                py: 0.25,
                borderRadius: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.08)',
                },
              }}
              onClick={() => handleNavigate('/receivables')}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: 1.5,
                  color: 'primary.main',
                  fontSize: '1.1rem',
                }}
              >
                <ReceivablesIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText
                primary="Quản lý Công nợ"
                primaryTypographyProps={{ fontSize: '0.85rem' }}
              />
            </ListItemButton>
          </ListItem>

        </List>

        <Divider />
        <Box sx={{ p: 0.75 }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              p: 0.75,
              borderRadius: 1.5,
              backgroundColor: 'rgba(99, 102, 241, 0.08)',
            }}
          >
            <Avatar
              alt={user?.name}
              src="/static/images/avatar/1.jpg"
              sx={{ width: 28, height: 28, mr: 1.25 }} // Giảm kích thước avatar thêm
            />
            <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <Typography variant="body2" noWrap sx={{ fontWeight: 600, fontSize: '0.8rem' }}>
                {user?.name}
              </Typography>
              <Typography variant="caption" color="text.secondary" noWrap sx={{ fontSize: '0.7rem' }}>
                {user?.email}
              </Typography>
            </Box>
            <Tooltip title="Tùy chọn">
              <IconButton
                onClick={handleOpenUserMenu}
                size="small"
                sx={{ ml: 0.25, p: 0.25 }}
              >
                <ExpandMore fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      <Menu
        sx={{
          mt: '10px',
          '& .MuiPaper-root': {
            boxShadow: '0 8px 16px rgba(0, 0, 0, 0.1)',
            borderRadius: 2
          }
        }}
        id="menu-appbar"
        anchorEl={anchorElUser}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        keepMounted
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        open={Boolean(anchorElUser)}
        onClose={handleCloseUserMenu}
      >
        <MenuItem onClick={() => { handleCloseUserMenu(); navigate('/profile'); }}>
          <ListItemIcon>
            <PersonIcon fontSize="small" />
          </ListItemIcon>
          <Typography textAlign="center">Hồ sơ</Typography>
        </MenuItem>
        <MenuItem onClick={handleLogout}>
          <ListItemIcon>
            <LogoutIcon fontSize="small" />
          </ListItemIcon>
          <Typography textAlign="center">Đăng xuất</Typography>
        </MenuItem>
      </Menu>
    </>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      {/* Nút toggle sidebar cho thiết bị di động - hiển thị ở góc trên bên phải */}
      {isMobile && !mobileOpen && (
        <IconButton
          color="inherit"
          aria-label="open drawer"
          edge="start"
          onClick={handleDrawerToggle}
          sx={{
            position: 'fixed',
            top: 10,
            right: 10,
            zIndex: 1200,
            backgroundColor: 'primary.main',
            color: 'white',
            '&:hover': {
              backgroundColor: 'primary.dark',
            },
            boxShadow: 2,
            padding: '8px'
          }}
          size="small"
        >
          <MenuIcon fontSize="small" />
        </IconButton>
      )}

      <Box
        component="nav"
        sx={{
          width: { md: sidebarCollapsed ? 0 : drawerWidth },
          flexShrink: { md: 0 },
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true, // Better open performance on mobile
          }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              transform: sidebarCollapsed ? `translateX(-${drawerWidth}px)` : 'translateX(0)',
              transition: theme.transitions.create('transform', {
                easing: theme.transitions.easing.sharp,
                duration: theme.transitions.duration.enteringScreen,
              }),
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Nút toggle sidebar */}
      {!isMobile && (
        <IconButton
          onClick={handleToggleSidebar}
          sx={{
            position: 'fixed',
            left: sidebarCollapsed ? 16 : drawerWidth - 28,
            top: 16,
            zIndex: 1200,
            backgroundColor: 'primary.main',
            color: 'white',
            '&:hover': {
              backgroundColor: 'primary.dark',
            },
            boxShadow: 2,
            transition: theme.transitions.create('left', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          }}
          size="small"
        >
          {sidebarCollapsed ? <MenuOpenIcon fontSize="small" /> : <ChevronLeftIcon fontSize="small" />}
        </IconButton>
      )}

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 2, // Giảm padding
          width: { md: sidebarCollapsed ? '100%' : `calc(100% - ${drawerWidth}px)` },
          mt: 0, // Loại bỏ margin-top vì không còn AppBar
          transition: theme.transitions.create(['width', 'margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default Sidebar;
