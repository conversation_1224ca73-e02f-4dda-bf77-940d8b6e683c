import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { authService } from '../services/authService';
import { User } from '../types/auth';

/**
 * Auth State Interface
 */
interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

/**
 * Auth Actions
 */
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string; refreshToken: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_USER'; payload: User }
  | { type: 'TOKEN_REFRESH'; payload: { token: string; refreshToken: string } };

/**
 * Auth Context Interface
 */
interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  updateUser: (user: User) => void;
}

/**
 * Initial State
 */
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  refreshToken: localStorage.getItem('refreshToken'),
  isLoading: false,
  isAuthenticated: false,
  error: null,
};

/**
 * Auth Reducer
 */
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        error: null,
      };

    case 'AUTH_FAILURE':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null,
        error: action.payload,
      };

    case 'LOGOUT':
      return {
        ...state,
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
      };

    case 'TOKEN_REFRESH':
      return {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
      };

    default:
      return state;
  }
};

/**
 * Create Auth Context
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Auth Provider Props
 */
interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Auth Provider Component
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  /**
   * Initialize auth state on app start
   */
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('token');
      const refreshToken = localStorage.getItem('refreshToken');

      if (token && refreshToken) {
        try {
          dispatch({ type: 'AUTH_START' });
          
          // Verify token by getting current user
          const user = await authService.getCurrentUser();
          
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: { user, token, refreshToken }
          });
        } catch (error) {
          // Token might be expired, try to refresh
          try {
            const refreshResponse = await authService.refreshToken(refreshToken);
            const user = await authService.getCurrentUser();
            
            localStorage.setItem('token', refreshResponse.token);
            localStorage.setItem('refreshToken', refreshResponse.refreshToken);
            
            dispatch({
              type: 'AUTH_SUCCESS',
              payload: {
                user,
                token: refreshResponse.token,
                refreshToken: refreshResponse.refreshToken
              }
            });
          } catch (refreshError) {
            // Both token and refresh failed, clear storage
            localStorage.removeItem('token');
            localStorage.removeItem('refreshToken');
            dispatch({ type: 'LOGOUT' });
          }
        }
      }
    };

    initializeAuth();
  }, []);

  /**
   * Login function
   */
  const login = async (email: string, password: string): Promise<void> => {
    try {
      dispatch({ type: 'AUTH_START' });
      
      const response = await authService.login(email, password);
      
      // Store tokens in localStorage
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      dispatch({
        type: 'AUTH_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
          refreshToken: response.refreshToken
        }
      });
    } catch (error: any) {
      const errorMessage = error.response?.data?.error?.message || 'Đăng nhập thất bại';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      throw error;
    }
  };

  /**
   * Logout function
   */
  const logout = (): void => {
    // Call logout API (optional)
    authService.logout().catch(console.error);
    
    // Clear localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    
    // Update state
    dispatch({ type: 'LOGOUT' });
  };

  /**
   * Refresh token function
   */
  const refreshToken = async (): Promise<void> => {
    try {
      const currentRefreshToken = state.refreshToken || localStorage.getItem('refreshToken');
      
      if (!currentRefreshToken) {
        throw new Error('No refresh token available');
      }
      
      const response = await authService.refreshToken(currentRefreshToken);
      
      // Store new tokens
      localStorage.setItem('token', response.token);
      localStorage.setItem('refreshToken', response.refreshToken);
      
      dispatch({
        type: 'TOKEN_REFRESH',
        payload: {
          token: response.token,
          refreshToken: response.refreshToken
        }
      });
    } catch (error) {
      // Refresh failed, logout user
      logout();
      throw error;
    }
  };

  /**
   * Clear error function
   */
  const clearError = (): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  /**
   * Update user function
   */
  const updateUser = (user: User): void => {
    dispatch({ type: 'SET_USER', payload: user });
  };

  const contextValue: AuthContextType = {
    state,
    login,
    logout,
    refreshToken,
    clearError,
    updateUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use Auth Context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
