/**
 * QuickPriceForm Component
 * Form cập nhật nhanh gi<PERSON> sản phẩm cho hợp đồng
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
  IconButton,
  InputAdornment,
  Button,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { vi } from 'date-fns/locale';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  MonetizationOn as MoneyIcon
} from '@mui/icons-material';

// Types
import { Contract } from '../../types/contract';
import { Product } from '../../types/product';
import { Customer } from '../../types/customer';

// Services
import { contractService } from '../../services/contractService';
import { productService } from '../../services/productService';
import { customerService } from '../../services/customerService';

// Utils
import { formatCurrencyVN, handleCurrencyInputVN, convertDateToISOString, convertISOStringToDate } from '../../utils/vietnameseFormatters';

// Define interfaces
interface PriceItem {
  product_id: number;
  price: number;
}

export interface QuickPriceFormData {
  contract_id: number;
  effective_date: string;
  expiry_date?: string;
  items: PriceItem[];
}

interface QuickPriceFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: QuickPriceFormData) => Promise<void>;
  loading?: boolean;
  error?: string | null;
  title?: string;
  subtitle?: string;
  // Prefilled values
  prefilledContractId?: number;
  prefilledCustomerId?: number;
}

// Utility function - DEPRECATED: Use convertDateToISOString instead
const formatDateForStorage = (date: Date): string => {
  return convertDateToISOString(date);
};

const initialFormData: QuickPriceFormData = {
  contract_id: 0,
  effective_date: formatDateForStorage(new Date()),
  expiry_date: '',
  items: []
};

const QuickPriceForm: React.FC<QuickPriceFormProps> = ({
  open,
  onClose,
  onSubmit,
  loading = false,
  error = null,
  title = 'Thiết lập giá nhanh',
  subtitle = 'Cập nhật giá sản phẩm cho hợp đồng',
  prefilledContractId,
  prefilledCustomerId
}) => {
  // State
  const [formData, setFormData] = useState<QuickPriceFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);
  const [formSubmitting, setFormSubmitting] = useState(false);
  // State để quản lý display values cho price inputs (với formatting)
  const [priceDisplayValues, setPriceDisplayValues] = useState<Record<number, string>>({});

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load customers
        const customersResponse = await customerService.getAll({ limit: 1000 });
        if (customersResponse.success) {
          setCustomers(customersResponse.data);
        }

        // Load products
        const productsResponse = await productService.getAll({ limit: 1000 });
        if (productsResponse.success) {
          setProducts(productsResponse.data);
        }

        // Load contracts if customer is prefilled
        if (prefilledCustomerId) {
          setSelectedCustomerId(prefilledCustomerId);
          const contractsResponse = await contractService.getAll({
            customer_id: prefilledCustomerId,
            limit: 1000
          });
          if (contractsResponse.success) {
            setContracts(contractsResponse.data);
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    if (open) {
      loadData();
    }
  }, [open, prefilledCustomerId]);

  // Initialize form data
  useEffect(() => {
    if (open) {
      let newFormData = { ...initialFormData };

      if (prefilledContractId) {
        newFormData.contract_id = prefilledContractId;
      }

      // Add an empty item
      if (newFormData.items.length === 0) {
        newFormData.items = [{
          product_id: 0,
          price: 0
        }];
      }

      setFormData(newFormData);
      setFormErrors({});

      // Khởi tạo display values cho price inputs
      const initialDisplayValues: Record<number, string> = {};
      newFormData.items.forEach((item, index) => {
        initialDisplayValues[index] = item.price > 0 ? formatCurrencyVN(item.price) : '';
      });
      setPriceDisplayValues(initialDisplayValues);
    }
  }, [open, prefilledContractId]);

  // Handle customer change
  const handleCustomerChange = async (customerId: number) => {
    setSelectedCustomerId(customerId);
    setFormData(prev => ({ ...prev, contract_id: 0 }));

    if (customerId) {
      try {
        const response = await contractService.getAll({
          customer_id: customerId,
          limit: 1000
        });
        if (response.success) {
          setContracts(response.data);
        }
      } catch (error) {
        console.error('Error fetching contracts:', error);
      }
    } else {
      setContracts([]);
    }
  };

  // Handle product change
  const handleProductChange = (index: number, productId: number) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      product_id: productId
    };
    setFormData(prev => ({ ...prev, items: updatedItems }));
  };

  // Handle price change with formatting
  const handlePriceChange = (index: number, inputValue: string) => {
    // Sử dụng utility function để format và parse
    const { displayValue, numericValue } = handleCurrencyInputVN(inputValue);

    // Cập nhật display value
    setPriceDisplayValues(prev => ({
      ...prev,
      [index]: displayValue
    }));

    // Cập nhật numeric value trong form data
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      price: numericValue
    };
    setFormData(prev => ({ ...prev, items: updatedItems }));
  };

  // Add new item
  const handleAddItem = () => {
    const newIndex = formData.items.length;
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          product_id: 0,
          price: 0
        }
      ]
    }));

    // Thêm display value cho item mới
    setPriceDisplayValues(prev => ({
      ...prev,
      [newIndex]: ''
    }));
  };

  // Remove item
  const handleRemoveItem = (index: number) => {
    const updatedItems = [...formData.items];
    updatedItems.splice(index, 1);
    setFormData(prev => ({ ...prev, items: updatedItems }));

    // Xóa display value tương ứng và cập nhật lại index
    setPriceDisplayValues(prev => {
      const newDisplayValues: Record<number, string> = {};
      Object.keys(prev).forEach(key => {
        const keyIndex = parseInt(key);
        if (keyIndex < index) {
          newDisplayValues[keyIndex] = prev[keyIndex];
        } else if (keyIndex > index) {
          newDisplayValues[keyIndex - 1] = prev[keyIndex];
        }
      });
      return newDisplayValues;
    });
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.contract_id) {
      errors.contract_id = 'Hợp đồng là bắt buộc';
    }

    if (!formData.effective_date) {
      errors.effective_date = 'Ngày hiệu lực là bắt buộc';
    }

    if (formData.expiry_date && new Date(formData.expiry_date) <= new Date(formData.effective_date)) {
      errors.expiry_date = 'Ngày hết hạn phải sau ngày hiệu lực';
    }

    // Validate items
    formData.items.forEach((item, index) => {
      if (!item.product_id) {
        errors[`items[${index}].product_id`] = 'Sản phẩm là bắt buộc';
      }

      if (item.price <= 0) {
        errors[`items[${index}].price`] = 'Giá phải lớn hơn 0';
      }
    });

    if (formData.items.length === 0) {
      errors.items = 'Phải có ít nhất một sản phẩm';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setFormSubmitting(true);
    try {
      await onSubmit(formData);
    } finally {
      setFormSubmitting(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { minHeight: 600 }
        }}
      >
        <DialogTitle>
          <Typography variant="h6">{title}</Typography>
          <Typography variant="body2" color="textSecondary">
            {subtitle}
          </Typography>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 1 }}>
            {error && (
              <Typography color="error" sx={{ mb: 2 }}>
                {error}
              </Typography>
            )}

            <Grid container spacing={3}>
              {/* Customer Selection - Only show if not prefilled */}
              {!prefilledCustomerId && (
                <Grid item xs={12}>
                  <FormControl fullWidth required size="small">
                    <InputLabel>Khách hàng</InputLabel>
                    <Select
                      value={selectedCustomerId || ''}
                      onChange={(e) => handleCustomerChange(e.target.value as number)}
                      label="Khách hàng"
                    >
                      <MenuItem value={0} disabled>Chọn khách hàng</MenuItem>
                      {customers.map((customer) => (
                        <MenuItem key={customer.id} value={customer.id}>
                          {customer.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              )}

              {/* Main Row: Contract, Effective Date, Expiry Date */}
              <Grid item xs={12} md={4}>
                <FormControl
                  fullWidth
                  required
                  error={!!formErrors.contract_id}
                  size="small"
                  disabled={!prefilledContractId && !selectedCustomerId}
                >
                  <InputLabel>Hợp đồng</InputLabel>
                  <Select
                    value={formData.contract_id || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, contract_id: e.target.value as number }))}
                    label="Hợp đồng"
                    disabled={!!prefilledContractId}
                  >
                    <MenuItem value={0} disabled>Chọn hợp đồng</MenuItem>
                    {contracts.map((contract) => (
                      <MenuItem key={contract.id} value={contract.id}>
                        {contract.contract_number} - {contract.contract_name}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.contract_id && <FormHelperText>{formErrors.contract_id}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <DatePicker
                  label="Ngày hiệu lực"
                  value={convertISOStringToDate(formData.effective_date)}
                  onChange={(date) => {
                    setFormData(prev => ({
                      ...prev,
                      effective_date: convertDateToISOString(date)
                    }));
                  }}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      size: "small",
                      error: !!formErrors.effective_date,
                      helperText: formErrors.effective_date,
                      placeholder: 'dd/mm/yyyy'
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <DatePicker
                  label="Ngày hết hạn (tùy chọn)"
                  value={convertISOStringToDate(formData.expiry_date)}
                  onChange={(date) => {
                    setFormData(prev => ({
                      ...prev,
                      expiry_date: convertDateToISOString(date)
                    }));
                  }}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      size: "small",
                      error: !!formErrors.expiry_date,
                      helperText: formErrors.expiry_date,
                      placeholder: 'dd/mm/yyyy'
                    }
                  }}
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Danh sách sản phẩm và giá
              </Typography>
              <Divider />
            </Box>

            {formErrors.items && (
              <FormHelperText error sx={{ mb: 2 }}>{formErrors.items}</FormHelperText>
            )}

            {/* Product Items - Compact Layout */}
            <Box sx={{
              mb: 2,
              border: '1px solid',
              borderColor: 'grey.200',
              borderRadius: 1,
              backgroundColor: 'grey.50'
            }}>
              {formData.items.map((item, index) => (
                <Box
                  key={index}
                  sx={{
                    p: 1.5
                  }}
                >
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={6}>
                      <Autocomplete
                        options={products}
                        getOptionLabel={(option) => `${option.code} - ${option.name}`}
                        value={products.find(p => p.id === item.product_id) || null}
                        onChange={(_, newValue) => {
                          handleProductChange(index, newValue ? newValue.id : 0);
                        }}
                        renderOption={(props, option) => (
                          <Box component="li" {...props}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {option.code} - {option.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Đơn vị: {option.unit_type}
                              </Typography>
                            </Box>
                          </Box>
                        )}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Sản phẩm"
                            required
                            size="small"
                            error={!!formErrors[`items[${index}].product_id`]}
                            helperText={formErrors[`items[${index}].product_id`]}
                            placeholder="Gõ để tìm sản phẩm..."
                          />
                        )}
                        noOptionsText="Không tìm thấy sản phẩm"
                        clearOnBlur
                        selectOnFocus
                        handleHomeEndKeys
                      />
                    </Grid>

                    <Grid item xs={12} sm={5}>
                      <TextField
                        label="Đơn giá"
                        type="text"
                        value={priceDisplayValues[index] || ''}
                        onChange={(e) => handlePriceChange(index, e.target.value)}
                        fullWidth
                        required
                        error={!!formErrors[`items[${index}].price`]}
                        helperText={formErrors[`items[${index}].price`]}
                        size="small"
                        placeholder="Nhập số tiền..."
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <MoneyIcon fontSize="small" />
                            </InputAdornment>
                          )
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={1}>
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={formData.items.length <= 1}
                        size="small"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Grid>
                  </Grid>
                </Box>
              ))}
            </Box>

            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddItem}
                disabled={!formData.contract_id}
              >
                Thêm sản phẩm
              </Button>
            </Box>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading || formSubmitting}>
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || formSubmitting}
          >
            {loading || formSubmitting ? 'Đang xử lý...' : 'Lưu giá'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default QuickPriceForm;
