/**
 * Production Import Dialog Component
 * Dialog để import sản lượng từ file Excel
 */

import React, { useState, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import * as XLSX from 'xlsx';
import {
  formatDateVN,
  parseDateVN,
  convertVNDateToISO,
  isValidVNDate,
  formatCurrencyVN
} from '../../utils/vietnameseFormatters';

// Types
interface ImportRow {
  rowIndex: number;
  contract_number: string;
  production_date: string;
  product_code: string;
  quantity: number;
  notes?: string;
  status: 'valid' | 'error' | 'warning';
  errors: string[];
  warnings: string[];
}

interface ProductionImportDialogProps {
  open: boolean;
  onClose: () => void;
  onImport: (data: ImportRow[]) => Promise<void>;
  loading?: boolean;
}

const TEMPLATE_HEADERS = [
  'Số hợp đồng',
  'Ngày sản xuất',
  'Mã sản phẩm',
  'Số lượng',
  'Ghi chú'
];

const SAMPLE_DATA = [
  ['HD001/2025', '10/06/2025', 'SP001', 10, 'Sản xuất đợt 1'],
  ['HD001/2025', '10/06/2025', 'SP002', 5, ''],
  ['HD002/2025', '11/06/2025', 'SP003', 8, 'Sản xuất đợt 2'],
];

const ProductionImportDialog: React.FC<ProductionImportDialogProps> = ({
  open,
  onClose,
  onImport,
  loading = false,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [file, setFile] = useState<File | null>(null);
  const [importData, setImportData] = useState<ImportRow[]>([]);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const steps = ['Chọn file Excel', 'Xem trước dữ liệu', 'Xác nhận import'];

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setActiveStep(0);
      setFile(null);
      setImportData([]);
      setError(null);
    }
  }, [open]);

  // Download template Excel
  const handleDownloadTemplate = () => {
    const wb = XLSX.utils.book_new();
    const wsData = [TEMPLATE_HEADERS, ...SAMPLE_DATA];
    const ws = XLSX.utils.aoa_to_sheet(wsData);
    
    // Set column widths
    ws['!cols'] = [
      { width: 15 }, // Số hợp đồng
      { width: 15 }, // Ngày sản xuất
      { width: 12 }, // Mã sản phẩm
      { width: 10 }, // Số lượng
      { width: 25 }, // Ghi chú
    ];

    XLSX.utils.book_append_sheet(wb, ws, 'Template');
    XLSX.writeFile(wb, 'template_import_san_luong.xlsx');
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setError(null);
    }
  };

  // Process Excel file
  const handleProcessFile = async () => {
    if (!file) return;

    setProcessing(true);
    setError(null);

    try {
      const data = await file.arrayBuffer();
      const workbook = XLSX.read(data);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      // Skip header row
      const rows = jsonData.slice(1) as any[][];
      const processedData: ImportRow[] = [];

      rows.forEach((row, index) => {
        if (row.length === 0 || !row[0]) return; // Skip empty rows

        const importRow: ImportRow = {
          rowIndex: index + 2, // +2 because we skip header and array is 0-indexed
          contract_number: String(row[0] || '').trim(),
          production_date: String(row[1] || '').trim(),
          product_code: String(row[2] || '').trim(),
          quantity: Number(row[3]) || 0,
          notes: String(row[4] || '').trim(),
          status: 'valid',
          errors: [],
          warnings: [],
        };

        // Validate data
        validateRow(importRow);
        processedData.push(importRow);
      });

      setImportData(processedData);
      setActiveStep(1);
    } catch (err) {
      setError('Lỗi khi đọc file Excel. Vui lòng kiểm tra định dạng file.');
      console.error('Excel processing error:', err);
    } finally {
      setProcessing(false);
    }
  };

  // Validate import row
  const validateRow = (row: ImportRow) => {
    // Required fields validation
    if (!row.contract_number) {
      row.errors.push('Số hợp đồng là bắt buộc');
    }
    if (!row.production_date) {
      row.errors.push('Ngày sản xuất là bắt buộc');
    }
    if (!row.product_code) {
      row.errors.push('Mã sản phẩm là bắt buộc');
    }
    if (row.quantity <= 0) {
      row.errors.push('Số lượng phải lớn hơn 0');
    }

    // Date format validation (Vietnamese format dd/mm/yyyy)
    if (row.production_date && !isValidVNDate(row.production_date)) {
      row.errors.push('Ngày sản xuất không đúng định dạng (dd/mm/yyyy)');
    }

    // Set status based on validation
    if (row.errors.length > 0) {
      row.status = 'error';
    } else if (row.warnings.length > 0) {
      row.status = 'warning';
    }
  };

  // Handle import confirmation
  const handleConfirmImport = async () => {
    const validRows = importData.filter(row => row.status !== 'error');
    if (validRows.length === 0) {
      setError('Không có dữ liệu hợp lệ để import');
      return;
    }

    try {
      await onImport(validRows);
      setActiveStep(2);
    } catch (err) {
      setError('Lỗi khi import dữ liệu. Vui lòng thử lại.');
      console.error('Import error:', err);
    }
  };

  // Remove file
  const handleRemoveFile = () => {
    setFile(null);
    setImportData([]);
    setActiveStep(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Get status icon
  const getStatusIcon = (status: ImportRow['status']) => {
    switch (status) {
      case 'valid':
        return <SuccessIcon color="success" fontSize="small" />;
      case 'warning':
        return <WarningIcon color="warning" fontSize="small" />;
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />;
    }
  };

  // Get status color
  const getStatusColor = (status: ImportRow['status']) => {
    switch (status) {
      case 'valid':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const validCount = importData.filter(row => row.status === 'valid').length;
  const errorCount = importData.filter(row => row.status === 'error').length;
  const warningCount = importData.filter(row => row.status === 'warning').length;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Typography variant="h6">Import sản lượng từ Excel</Typography>
        <Typography variant="body2" color="text.secondary">
          Import hàng loạt dữ liệu sản lượng từ file Excel
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Step 1: File Selection */}
        {activeStep === 0 && (
          <Box>
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Hướng dẫn:</strong>
              </Typography>
              <Typography variant="body2" component="div">
                1. Tải template Excel mẫu<br/>
                2. Điền dữ liệu theo đúng định dạng (ngày: dd/mm/yyyy)<br/>
                3. Đơn giá sẽ tự động lấy theo giá đã thiết lập cho hợp đồng<br/>
                4. Upload file để xem trước và import
              </Typography>
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                onClick={handleDownloadTemplate}
              >
                Tải template Excel
              </Button>
            </Box>

            <Box
              sx={{
                border: '2px dashed',
                borderColor: file ? 'success.main' : 'grey.300',
                borderRadius: 2,
                p: 3,
                textAlign: 'center',
                backgroundColor: file ? 'success.50' : 'grey.50',
                cursor: 'pointer',
              }}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
              
              {file ? (
                <Box>
                  <SuccessIcon color="success" sx={{ fontSize: 48, mb: 1 }} />
                  <Typography variant="h6" color="success.main">
                    {file.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Kích thước: {(file.size / 1024).toFixed(1)} KB
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center' }}>
                    <Button
                      variant="contained"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleProcessFile();
                      }}
                      disabled={processing}
                    >
                      {processing ? 'Đang xử lý...' : 'Xử lý file'}
                    </Button>
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveFile();
                      }}
                    >
                      Xóa file
                    </Button>
                  </Box>
                </Box>
              ) : (
                <Box>
                  <UploadIcon sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                  <Typography variant="h6" color="text.secondary">
                    Chọn file Excel để upload
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Hỗ trợ định dạng .xlsx, .xls
                  </Typography>
                </Box>
              )}
            </Box>

            {processing && (
              <Box sx={{ mt: 2 }}>
                <LinearProgress />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Đang xử lý file Excel...
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Step 2: Data Preview */}
        {activeStep === 1 && (
          <Box>
            <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
              <Typography variant="h6">
                Xem trước dữ liệu ({importData.length} dòng)
              </Typography>
              <Chip
                label={`${validCount} hợp lệ`}
                color="success"
                size="small"
              />
              {warningCount > 0 && (
                <Chip
                  label={`${warningCount} cảnh báo`}
                  color="warning"
                  size="small"
                />
              )}
              {errorCount > 0 && (
                <Chip
                  label={`${errorCount} lỗi`}
                  color="error"
                  size="small"
                />
              )}
            </Box>

            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Dòng</TableCell>
                    <TableCell>Trạng thái</TableCell>
                    <TableCell>Số hợp đồng</TableCell>
                    <TableCell>Ngày sản xuất</TableCell>
                    <TableCell>Mã sản phẩm</TableCell>
                    <TableCell align="right">Số lượng</TableCell>
                    <TableCell>Ghi chú</TableCell>
                    <TableCell>Lỗi/Cảnh báo</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {importData.map((row) => (
                    <TableRow
                      key={row.rowIndex}
                      sx={{
                        backgroundColor: row.status === 'error' ? 'error.50' : 
                                       row.status === 'warning' ? 'warning.50' : 'inherit'
                      }}
                    >
                      <TableCell>{row.rowIndex}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getStatusIcon(row.status)}
                          <Chip
                            label={row.status === 'valid' ? 'Hợp lệ' : 
                                   row.status === 'warning' ? 'Cảnh báo' : 'Lỗi'}
                            color={getStatusColor(row.status)}
                            size="small"
                          />
                        </Box>
                      </TableCell>
                      <TableCell>{row.contract_number}</TableCell>
                      <TableCell>{row.production_date}</TableCell>
                      <TableCell>{row.product_code}</TableCell>
                      <TableCell align="right">{formatCurrencyVN(row.quantity)}</TableCell>
                      <TableCell>{row.notes}</TableCell>
                      <TableCell>
                        {[...row.errors, ...row.warnings].map((msg, index) => (
                          <Typography
                            key={index}
                            variant="caption"
                            color={row.errors.includes(msg) ? 'error' : 'warning.main'}
                            display="block"
                          >
                            {msg}
                          </Typography>
                        ))}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            {errorCount > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Có {errorCount} dòng dữ liệu lỗi sẽ bị bỏ qua. Chỉ {validCount} dòng hợp lệ sẽ được import.
              </Alert>
            )}
          </Box>
        )}

        {/* Step 3: Success */}
        {activeStep === 2 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <SuccessIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
            <Typography variant="h5" color="success.main" gutterBottom>
              Import thành công!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Đã import {validCount} dòng dữ liệu sản lượng.
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          {activeStep === 2 ? 'Đóng' : 'Hủy'}
        </Button>
        
        {activeStep === 1 && (
          <>
            <Button
              onClick={() => setActiveStep(0)}
              disabled={loading}
            >
              Quay lại
            </Button>
            <Button
              onClick={handleConfirmImport}
              variant="contained"
              disabled={loading || validCount === 0}
            >
              {loading ? 'Đang import...' : `Import ${validCount} dòng`}
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default ProductionImportDialog;
