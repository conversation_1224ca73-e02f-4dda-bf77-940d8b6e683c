/**
 * Contract Detail Component
 * <PERSON><PERSON><PERSON> thị thông tin chi tiết của hợp đồng với tabs layout
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Divider,
  Alert,
  Skeleton,
  Snackbar,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  Business as BusinessIcon,
  Description as DescriptionIcon,
  Assignment as ContractIcon,
  AttachMoney as PriceIcon,
  Assessment as ProductionIcon,
  Info as InfoIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import {
  Contract,
  ContractDetail as ContractDetailType,
  CONTRACT_STATUS_OPTIONS
} from '../../types/contract';

// Import Quick Forms
import QuickProductionForm from '../production/QuickProductionForm';

// Import Tab Components
import PricingTab from './PricingTab';

// Services
import { contractPriceService } from '../../services/contractPriceService';
import { dailyProductionService } from '../../services/dailyProductionService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`contract-tabpanel-${index}`}
      aria-labelledby={`contract-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `contract-tab-${index}`,
    'aria-controls': `contract-tabpanel-${index}`,
  };
}

interface ContractDetailProps {
  open: boolean;
  contract: ContractDetailType | null;
  loading?: boolean;
  onClose: () => void;
  onEdit?: (contract: Contract) => void;
  onDelete?: (contract: Contract) => void;
  onRefresh?: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
}

const ContractDetail: React.FC<ContractDetailProps> = ({
  open,
  contract,
  loading = false,
  onClose,
  onEdit,
  onDelete,
  onRefresh,
  canEdit = true,
  canDelete = true,
}) => {
  const [tabValue, setTabValue] = useState(0);

  // Quick forms state (only for production)
  const [quickProductionOpen, setQuickProductionOpen] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Reset tab when dialog opens
  useEffect(() => {
    if (open) {
      setTabValue(0);
    }
  }, [open]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusInfo = (status: string) => {
    return CONTRACT_STATUS_OPTIONS.find(opt => opt.value === status);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Không giới hạn';
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  // Quick form handlers
  const handleQuickProductionOpen = () => {
    setQuickProductionOpen(true);
    setFormError(null);
  };

  const handleQuickProductionSubmit = async (data: any) => {
    setFormLoading(true);
    setFormError(null);

    try {
      console.log('Quick production submit data:', data);

      // Create multiple production entries for each item
      const promises = data.items.map((item: any) =>
        dailyProductionService.create({
          contract_id: data.contract_id,
          product_id: item.product_id,
          production_date: data.production_date,
          quantity: item.quantity,
          unit_price: item.unit_price,
          notes: data.notes, // Chỉ sử dụng ghi chú chung
          auto_get_price: false // Đã có unit_price từ form
        })
      );

      const results = await Promise.all(promises);
      console.log('Quick production submit results:', results);

      setSuccessMessage('Nhập sản lượng thành công!');
      setQuickProductionOpen(false);
      onRefresh?.(); // Refresh contract data
    } catch (error) {
      console.error('Error submitting quick production:', error);
      setFormError('Có lỗi xảy ra khi nhập sản lượng');
    } finally {
      setFormLoading(false);
    }
  };

  if (!open) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: 600, maxHeight: '90vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" component="div">
              Chi tiết hợp đồng
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {contract?.contract_number || 'Đang tải...'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {canEdit && contract && (
              <Tooltip title="Chỉnh sửa">
                <IconButton onClick={() => onEdit?.(contract)} color="primary">
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}
            {canDelete && contract && (
              <Tooltip title="Xóa">
                <IconButton onClick={() => onDelete?.(contract)} color="error">
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Đóng">
              <IconButton onClick={onClose}>
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="contract detail tabs">
            <Tab
              label="Thông tin chung"
              icon={<InfoIcon />}
              iconPosition="start"
              {...a11yProps(0)}
            />
            <Tab
              label="Bảng giá"
              icon={<PriceIcon />}
              iconPosition="start"
              {...a11yProps(1)}
            />
            <Tab
              label="Sản lượng"
              icon={<ProductionIcon />}
              iconPosition="start"
              {...a11yProps(2)}
            />
          </Tabs>
        </Box>

        <Box sx={{ px: 3 }}>
          <TabPanel value={tabValue} index={0}>
            {loading || !contract ? (
              <Grid container spacing={3}>
                {Array.from(new Array(6)).map((_, index) => (
                  <Grid item xs={12} sm={6} key={index}>
                    <Skeleton variant="rectangular" height={80} />
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Grid container spacing={3}>
                {/* Thông tin cơ bản */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <ContractIcon color="primary" />
                        Thông tin hợp đồng
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Số hợp đồng
                            </Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {contract.contract_number}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Trạng thái
                            </Typography>
                            <Chip
                              label={getStatusInfo(contract.status)?.label || contract.status}
                              color={getStatusInfo(contract.status)?.color || 'default'}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Grid>

                        <Grid item xs={12}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Tên hợp đồng
                            </Typography>
                            <Typography variant="h6" color="primary">
                              {contract.contract_name}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Ngày bắt đầu
                            </Typography>
                            <Typography variant="body1">
                              {formatDate(contract.start_date)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Ngày kết thúc
                            </Typography>
                            <Typography variant="body1">
                              {formatDate(contract.end_date)}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Thông tin khách hàng */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <BusinessIcon color="primary" />
                        Thông tin khách hàng
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Tên công ty
                            </Typography>
                            <Typography variant="body1" fontWeight={600}>
                              {contract.customer?.name || contract.customer_name || '-'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Tên viết tắt
                            </Typography>
                            <Typography variant="body1">
                              {contract.customer?.short_name || '-'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Mã số thuế
                            </Typography>
                            <Typography variant="body1" fontFamily="monospace">
                              {contract.customer?.tax_code || '-'}
                            </Typography>
                          </Box>
                        </Grid>

                        {contract.customer?.address && (
                          <Grid item xs={12}>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" display="block" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <LocationIcon fontSize="small" />
                                Địa chỉ
                              </Typography>
                              <Typography variant="body2">
                                {contract.customer.address}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        {contract.customer?.contact_person && (
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" display="block" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <PersonIcon fontSize="small" />
                                Người liên hệ
                              </Typography>
                              <Typography variant="body2">
                                {contract.customer.contact_person}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        {contract.customer?.contact_phone && (
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" display="block" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <PhoneIcon fontSize="small" />
                                Số điện thoại
                              </Typography>
                              <Typography variant="body2" fontFamily="monospace">
                                {contract.customer.contact_phone}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        {contract.customer?.contact_email && (
                          <Grid item xs={12} sm={4}>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" display="block" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <EmailIcon fontSize="small" />
                                Email
                              </Typography>
                              <Typography variant="body2">
                                {contract.customer.contact_email}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* Ghi chú */}
                {contract.notes && (
                  <Grid item xs={12}>
                    <Card variant="outlined">
                      <CardContent>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <DescriptionIcon color="primary" />
                          Ghi chú
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                          {contract.notes}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                )}

                {/* Thống kê */}
                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CalendarIcon color="primary" />
                        Thống kê & Thông tin hệ thống
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Số sản phẩm có giá
                            </Typography>
                            <Typography variant="h6" color="primary">
                              {contract.prices_count || 0}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Số lần sản xuất
                            </Typography>
                            <Typography variant="h6" color="primary">
                              {contract.production_count || 0}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Tổng doanh thu
                            </Typography>
                            <Typography variant="h6" color="success.main">
                              {contract.total_production_amount ? formatCurrency(contract.total_production_amount) : '0 ₫'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6} md={3}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Sản xuất gần nhất
                            </Typography>
                            <Typography variant="body2">
                              {contract.last_production_date ? formatDate(contract.last_production_date) : 'Chưa có'}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Ngày tạo
                            </Typography>
                            <Typography variant="body2">
                              {formatDateTime(contract.created_at)}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              Ngày cập nhật
                            </Typography>
                            <Typography variant="body2">
                              {formatDateTime(contract.updated_at)}
                            </Typography>
                          </Box>
                        </Grid>

                        {contract.created_by_name && (
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="caption" color="text.secondary" display="block">
                                Người tạo
                              </Typography>
                              <Typography variant="body2">
                                {contract.created_by_name}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: 2 }}>
                            <Typography variant="caption" color="text.secondary" display="block">
                              ID hệ thống
                            </Typography>
                            <Typography variant="body2" fontFamily="monospace">
                              #{contract.id}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {/* Pricing Tab Content */}
            <PricingTab
              contract={contract}
              loading={loading}
              onRefresh={onRefresh}
            />
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            {/* Production Tab Content */}
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ProductionIcon color="primary" />
                  Sản lượng & Doanh thu
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={handleQuickProductionOpen}
                  disabled={!contract || loading}
                >
                  Nhập sản lượng nhanh
                </Button>
              </Box>

              {loading ? (
                <Box>
                  {Array.from(new Array(3)).map((_, index) => (
                    <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 1 }} />
                  ))}
                </Box>
              ) : contract ? (
                <Box>
                  {/* Quick Production Summary */}
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" color="primary" sx={{ fontWeight: 'bold' }}>
                          {contract.production_count || 0}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Lần sản xuất
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" color="success.main" sx={{ fontWeight: 'bold' }}>
                          {contract.total_production_amount ? formatCurrency(contract.total_production_amount) : '0 ₫'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Tổng doanh thu
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                        <Typography variant="h4" color="info.main" sx={{ fontWeight: 'bold' }}>
                          {contract.last_production_date ? formatDate(contract.last_production_date) : 'N/A'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          Sản xuất gần nhất
                        </Typography>
                      </Card>
                    </Grid>
                    <Grid item xs={12} sm={6} md={3}>
                      <Card variant="outlined" sx={{ textAlign: 'center', p: 2 }}>
                        <Button
                          variant="outlined"
                          fullWidth
                          onClick={() => window.open(`/production?contract_id=${contract.id}`, '_blank')}
                          sx={{ height: '100%' }}
                        >
                          Xem chi tiết
                        </Button>
                      </Card>
                    </Grid>
                  </Grid>

                  <Alert severity="success" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      ✅ <strong>Tính năng Quick Production Input đã sẵn sàng!</strong>
                      <br />
                      • Click "Nhập sản lượng nhanh" để nhập sản lượng nhiều sản phẩm cùng lúc
                      <br />
                      • Giá sẽ được tự động load từ bảng giá hiện tại
                      <br />
                      • Thành tiền được tính toán tự động
                      <br />
                      • Xem báo cáo chi tiết tại trang Production
                    </Typography>
                  </Alert>
                </Box>
              ) : null}
            </Box>
          </TabPanel>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
          {canEdit && contract && (
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => onEdit?.(contract)}
            >
              Chỉnh sửa
            </Button>
          )}
          <Button
            variant="contained"
            onClick={onClose}
          >
            Đóng
          </Button>
        </Box>
      </DialogActions>

      {/* Quick Forms */}
      {contract && (
        <QuickProductionForm
          open={quickProductionOpen}
          onClose={() => setQuickProductionOpen(false)}
          onSubmit={handleQuickProductionSubmit}
          loading={formLoading}
          error={formError}
          prefilledContractId={contract.id}
          prefilledCustomerId={contract.customer_id}
        />
      )}

      {/* Success Notification */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setSuccessMessage(null)} severity="success">
          {successMessage}
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default ContractDetail;
