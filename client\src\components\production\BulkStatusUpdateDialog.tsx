/**
 * Bulk Status Update Dialog Component
 * Dialog để cập nhật trạng thái hàng loạt cho sản lượng
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import { 
  CheckCircleOutlined as ConfirmIcon,
  WarningAmberOutlined as WarningIcon 
} from '@mui/icons-material';
import {
  ProductionStatus,
  PRODUCTION_STATUS_CONFIG,
  DailyProduction,
  BulkStatusUpdateResult
} from '../../types/dailyProduction';
import ProductionStatusChip from './ProductionStatusChip';

interface BulkStatusUpdateDialogProps {
  open: boolean;
  onClose: () => void;
  selectedProductions: DailyProduction[];
  onUpdate: (newStatus: ProductionStatus) => Promise<BulkStatusUpdateResult>;
  loading?: boolean;
}

const BulkStatusUpdateDialog: React.FC<BulkStatusUpdateDialogProps> = ({
  open,
  onClose,
  selectedProductions,
  onUpdate,
  loading = false
}) => {
  const [newStatus, setNewStatus] = useState<ProductionStatus>('Đã xác nhận');
  const [updating, setUpdating] = useState(false);
  const [result, setResult] = useState<BulkStatusUpdateResult | null>(null);

  const handleClose = () => {
    if (!updating) {
      setResult(null);
      onClose();
    }
  };

  const handleUpdate = async () => {
    if (selectedProductions.length === 0) return;

    setUpdating(true);
    try {
      const updateResult = await onUpdate(newStatus);
      setResult(updateResult);
      
      if (updateResult.success && updateResult.errors.length === 0) {
        // Tự động đóng dialog sau 2 giây nếu thành công hoàn toàn
        setTimeout(() => {
          handleClose();
        }, 2000);
      }
    } catch (error) {
      console.error('Error updating status:', error);
      setResult({
        success: false,
        updated_count: 0,
        updated_records: [],
        errors: [error instanceof Error ? error.message : 'Có lỗi xảy ra'],
        skipped_count: selectedProductions.length
      });
    } finally {
      setUpdating(false);
    }
  };

  const getValidationWarnings = () => {
    const warnings: string[] = [];
    const statusCounts = selectedProductions.reduce((acc, prod) => {
      acc[prod.status] = (acc[prod.status] || 0) + 1;
      return acc;
    }, {} as Record<ProductionStatus, number>);

    // Kiểm tra chuyển đổi trạng thái không hợp lệ
    if (newStatus === 'Đã ghi nhận công nợ') {
      const draftCount = statusCounts['Mới tạo'] || 0;
      if (draftCount > 0) {
        warnings.push(`${draftCount} bản ghi có trạng thái "Mới tạo" không thể chuyển trực tiếp sang "Đã ghi nhận công nợ"`);
      }
    }

    const invoicedCount = statusCounts['Đã ghi nhận công nợ'] || 0;
    if (invoicedCount > 0 && newStatus !== 'Đã ghi nhận công nợ') {
      warnings.push(`${invoicedCount} bản ghi đã "Đã ghi nhận công nợ" không thể thay đổi trạng thái`);
    }

    return warnings;
  };

  const warnings = getValidationWarnings();
  const canUpdate = selectedProductions.length > 0 && !updating;

  if (result) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            {result.success ? (
              <ConfirmIcon color="success" />
            ) : (
              <WarningIcon color="warning" />
            )}
            Kết quả cập nhật trạng thái
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box mb={2}>
            {result.success ? (
              <Alert severity="success">
                Đã cập nhật thành công {result.updated_count} bản ghi
              </Alert>
            ) : (
              <Alert severity="error">
                Cập nhật thất bại
              </Alert>
            )}
          </Box>

          {result.errors.length > 0 && (
            <Box mb={2}>
              <Typography variant="subtitle2" color="error" gutterBottom>
                Lỗi ({result.errors.length}):
              </Typography>
              <List dense>
                {result.errors.map((error, index) => (
                  <ListItem key={index}>
                    <ListItemText 
                      primary={error}
                      primaryTypographyProps={{ fontSize: '0.85rem' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {result.skipped_count > 0 && (
            <Alert severity="warning" sx={{ mt: 1 }}>
              {result.skipped_count} bản ghi bị bỏ qua do không thể cập nhật
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} variant="contained">
            Đóng
          </Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Cập nhật trạng thái hàng loạt
      </DialogTitle>
      <DialogContent>
        <Box mb={3}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Đã chọn {selectedProductions.length} bản ghi sản lượng
          </Typography>
          
          {/* Hiển thị thống kê trạng thái hiện tại */}
          <Box display="flex" gap={1} flexWrap="wrap" mt={1}>
            {Object.entries(
              selectedProductions.reduce((acc, prod) => {
                acc[prod.status] = (acc[prod.status] || 0) + 1;
                return acc;
              }, {} as Record<ProductionStatus, number>)
            ).map(([status, count]) => (
              <Box key={status} display="flex" alignItems="center" gap={0.5}>
                <ProductionStatusChip 
                  status={status as ProductionStatus} 
                  showTooltip={false}
                />
                <Typography variant="caption">({count})</Typography>
              </Box>
            ))}
          </Box>
        </Box>

        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Trạng thái mới</InputLabel>
          <Select
            value={newStatus}
            label="Trạng thái mới"
            onChange={(e) => setNewStatus(e.target.value as ProductionStatus)}
            disabled={updating}
          >
            {Object.entries(PRODUCTION_STATUS_CONFIG).map(([status, config]) => (
              <MenuItem key={status} value={status}>
                <Box display="flex" alignItems="center" gap={1}>
                  <ProductionStatusChip 
                    status={status as ProductionStatus}
                    showTooltip={false}
                  />
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {warnings.length > 0 && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Cảnh báo:
            </Typography>
            <List dense>
              {warnings.map((warning, index) => (
                <ListItem key={index} sx={{ py: 0 }}>
                  <ListItemText 
                    primary={warning}
                    primaryTypographyProps={{ fontSize: '0.85rem' }}
                  />
                </ListItem>
              ))}
            </List>
          </Alert>
        )}

        <Typography variant="body2" color="text.secondary">
          Chỉ những bản ghi có thể cập nhật sẽ được thay đổi trạng thái.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={updating}>
          Hủy
        </Button>
        <Button 
          onClick={handleUpdate}
          variant="contained"
          disabled={!canUpdate}
          startIcon={updating ? <CircularProgress size={16} /> : <ConfirmIcon />}
        >
          {updating ? 'Đang cập nhật...' : 'Cập nhật'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkStatusUpdateDialog;
