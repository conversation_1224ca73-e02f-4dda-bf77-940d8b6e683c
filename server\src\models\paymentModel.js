const { pool } = require('../db');

/**
 * Payment Model
 * Xử lý các thao tác CRUD cho bảng payments (Thanh toán)
 */

/**
 * Lấy tất cả payments với phân trang và tìm kiếm
 * @param {Object} options - Tùy chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {number} options.customerId - Lọc theo khách hàng
 * @param {string} options.status - Lọc theo trạng thái
 * @param {string} options.paymentMethod - Lọc theo phương thức thanh toán
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllPayments = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    customerId,
    status,
    paymentMethod,
    sortBy = 'payment_date',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'payment_date', 'amount', 'payment_method', 'status', 'created_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'payment_date';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    if (search && search.trim()) {
      whereClause += ` AND (
        p.reference_number ILIKE $${paramIndex} OR 
        p.description ILIKE $${paramIndex} OR
        p.bank_account ILIKE $${paramIndex} OR
        c.name ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search.trim()}%`);
      paramIndex++;
    }

    if (customerId) {
      whereClause += ` AND p.customer_id = $${paramIndex}`;
      queryParams.push(customerId);
      paramIndex++;
    }

    if (status) {
      whereClause += ` AND p.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    if (paymentMethod) {
      whereClause += ` AND p.payment_method = $${paramIndex}`;
      queryParams.push(paymentMethod);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM payments p
      JOIN customers c ON p.customer_id = c.id
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT
        p.id,
        p.customer_id,
        p.payment_date,
        p.amount,
        p.payment_method,
        p.reference_number,
        p.bank_account,
        p.description,
        p.status,
        p.created_at,
        p.updated_at,
        c.name as customer_name,
        c.tax_code as customer_tax_code,
        u.name as created_by_name
      FROM payments p
      JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users u ON p.created_by = u.id
      ${whereClause}
      ORDER BY p.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      payments: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllPayments:', error);
    throw error;
  }
};

/**
 * Lấy payment theo ID
 * @param {number} id - ID của payment
 * @returns {Object|null} Thông tin payment hoặc null nếu không tìm thấy
 */
const getPaymentById = async (id) => {
  try {
    const query = `
      SELECT 
        p.*,
        c.name as customer_name,
        c.tax_code as customer_tax_code,
        u.name as created_by_name
      FROM payments p
      JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users u ON p.created_by = u.id
      WHERE p.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getPaymentById:', error);
    throw error;
  }
};

/**
 * Tạo payment mới
 * @param {Object} paymentData - Dữ liệu payment
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin payment vừa tạo
 */
const createPayment = async (paymentData, createdBy) => {
  const {
    customer_id,
    payment_date,
    amount,
    payment_method,
    reference_number,
    bank_account,
    description,
    status = 'confirmed'
  } = paymentData;

  try {
    const query = `
      INSERT INTO payments (
        customer_id, payment_date, amount, payment_method, reference_number,
        bank_account, description, status, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;

    const values = [
      customer_id,
      payment_date,
      amount,
      payment_method,
      reference_number || null,
      bank_account || null,
      description || null,
      status,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createPayment:', error);
    throw error;
  }
};

/**
 * Cập nhật thông tin payment
 * @param {number} id - ID của payment
 * @param {Object} paymentData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin payment sau khi cập nhật
 */
const updatePayment = async (id, paymentData) => {
  const {
    customer_id,
    payment_date,
    amount,
    payment_method,
    reference_number,
    bank_account,
    description,
    status
  } = paymentData;

  try {
    const query = `
      UPDATE payments 
      SET 
        customer_id = $1,
        payment_date = $2,
        amount = $3,
        payment_method = $4,
        reference_number = $5,
        bank_account = $6,
        description = $7,
        status = $8,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $9
      RETURNING *
    `;

    const values = [
      customer_id,
      payment_date,
      amount,
      payment_method,
      reference_number,
      bank_account,
      description,
      status,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updatePayment:', error);
    throw error;
  }
};

/**
 * Xóa payment
 * @param {number} id - ID của payment
 * @returns {boolean} True nếu xóa thành công
 */
const deletePayment = async (id) => {
  try {
    // Xóa payment allocations trước
    await pool.query('DELETE FROM payment_allocations WHERE payment_id = $1', [id]);
    
    // Xóa payment
    const query = 'DELETE FROM payments WHERE id = $1 RETURNING id';
    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deletePayment:', error);
    throw error;
  }
};

/**
 * Lấy chi tiết phân bổ thanh toán
 * @param {number} paymentId - ID của payment
 * @returns {Array} Danh sách phân bổ thanh toán
 */
const getPaymentAllocations = async (paymentId) => {
  try {
    const query = `
      SELECT 
        pa.id,
        pa.payment_id,
        pa.receivable_id,
        pa.allocated_amount,
        pa.allocation_order,
        pa.created_at,
        r.invoice_number,
        r.transaction_date,
        r.due_date,
        r.original_amount,
        r.description as receivable_description
      FROM payment_allocations pa
      JOIN receivables r ON pa.receivable_id = r.id
      WHERE pa.payment_id = $1
      ORDER BY pa.allocation_order
    `;

    const result = await pool.query(query, [paymentId]);
    return result.rows;
  } catch (error) {
    console.error('Error in getPaymentAllocations:', error);
    throw error;
  }
};

/**
 * Lấy danh sách phương thức thanh toán
 * @returns {Array} Danh sách phương thức thanh toán
 */
const getPaymentMethods = async () => {
  try {
    const query = `
      SELECT DISTINCT payment_method
      FROM payments
      WHERE payment_method IS NOT NULL
      ORDER BY payment_method
    `;

    const result = await pool.query(query);
    return result.rows.map(row => row.payment_method);
  } catch (error) {
    console.error('Error in getPaymentMethods:', error);
    throw error;
  }
};

module.exports = {
  getAllPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
  getPaymentAllocations,
  getPaymentMethods
};
