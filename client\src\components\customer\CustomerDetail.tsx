import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Chip,
  Divider,
  Avatar,
} from '@mui/material';
import {
  Business as BusinessIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Badge as BadgeIcon,
} from '@mui/icons-material';
import { Customer } from '../../types/customer';
import { formatDate } from '../../utils/responseUtils';

/**
 * Customer Detail Props Interface
 */
export interface CustomerDetailProps {
  customer?: Customer | null;
}

/**
 * Detail Item Component
 */
interface DetailItemProps {
  icon: React.ReactNode;
  label: string;
  value: string | null | undefined;
  type?: 'text' | 'email' | 'phone' | 'date';
}

const DetailItem: React.FC<DetailItemProps> = ({ icon, label, value, type = 'text' }) => {
  const formatValue = (val: string | null | undefined, itemType: string): string => {
    if (!val) return 'Chưa có thông tin';
    
    switch (itemType) {
      case 'date':
        return formatDate(val);
      case 'phone':
        return val.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
      default:
        return val;
    }
  };

  const getValueColor = (): string => {
    return value ? 'text.primary' : 'text.secondary';
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, py: 1.5 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: 32,
          height: 32,
          borderRadius: 1,
          backgroundColor: 'primary.light',
          color: 'primary.contrastText',
          flexShrink: 0,
        }}
      >
        {icon}
      </Box>
      <Box sx={{ flex: 1, minWidth: 0 }}>
        <Typography
          variant="body2"
          sx={{
            fontSize: '0.8rem',
            fontWeight: 500,
            color: 'text.secondary',
            mb: 0.5,
          }}
        >
          {label}
        </Typography>
        <Typography
          variant="body1"
          sx={{
            fontSize: '0.9rem',
            color: getValueColor(),
            fontStyle: value ? 'normal' : 'italic',
            wordBreak: 'break-word',
          }}
        >
          {formatValue(value, type)}
        </Typography>
      </Box>
    </Box>
  );
};

/**
 * Customer Detail Component
 * Displays detailed information about a customer
 */
const CustomerDetail: React.FC<CustomerDetailProps> = ({ customer }) => {
  if (!customer) {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 200,
        }}
      >
        <Typography variant="body1" color="text.secondary">
          Không có thông tin khách hàng
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Customer Header */}
      <Paper
        elevation={1}
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
          color: 'white',
          borderRadius: 2,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              width: 56,
              height: 56,
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              fontSize: '1.5rem',
            }}
          >
            {customer.name?.charAt(0)?.toUpperCase() || 'K'}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                fontSize: '1.3rem',
                mb: 0.5,
              }}
            >
              {customer.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
              <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.85rem' }}>
                ID: {customer.id}
              </Typography>
              {customer.tax_code && (
                <>
                  <Typography variant="body2" sx={{ opacity: 0.7 }}>•</Typography>
                  <Chip
                    label={`MST: ${customer.tax_code}`}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      fontSize: '0.75rem',
                      height: 24,
                    }}
                  />
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Customer Details */}
      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3, height: 'fit-content' }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                mb: 2,
                color: 'primary.main',
              }}
            >
              Thông tin cơ bản
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <DetailItem
              icon={<BusinessIcon fontSize="small" />}
              label="Tên khách hàng"
              value={customer.name}
            />
            
            <DetailItem
              icon={<BadgeIcon fontSize="small" />}
              label="Tên viết tắt"
              value={customer.short_name}
            />
            
            <DetailItem
              icon={<BusinessIcon fontSize="small" />}
              label="Mã số thuế"
              value={customer.tax_code}
            />
            
            <DetailItem
              icon={<LocationIcon fontSize="small" />}
              label="Địa chỉ"
              value={customer.address}
            />
          </Paper>
        </Grid>

        {/* Contact Information */}
        <Grid item xs={12} md={6}>
          <Paper elevation={1} sx={{ p: 3, height: 'fit-content' }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                mb: 2,
                color: 'primary.main',
              }}
            >
              Thông tin liên hệ
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <DetailItem
              icon={<PersonIcon fontSize="small" />}
              label="Người liên hệ"
              value={customer.contact_person}
            />
            
            <DetailItem
              icon={<PhoneIcon fontSize="small" />}
              label="Số điện thoại"
              value={customer.phone}
              type="phone"
            />
            
            <DetailItem
              icon={<EmailIcon fontSize="small" />}
              label="Email"
              value={customer.email}
              type="email"
            />
          </Paper>
        </Grid>

        {/* System Information */}
        <Grid item xs={12}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Typography
              variant="h6"
              sx={{
                fontSize: '1rem',
                fontWeight: 600,
                mb: 2,
                color: 'primary.main',
              }}
            >
              Thông tin hệ thống
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <DetailItem
                  icon={<CalendarIcon fontSize="small" />}
                  label="Ngày tạo"
                  value={customer.created_at}
                  type="date"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DetailItem
                  icon={<CalendarIcon fontSize="small" />}
                  label="Ngày cập nhật"
                  value={customer.updated_at}
                  type="date"
                />
              </Grid>
              {customer.created_by_name && (
                <Grid item xs={12} sm={6}>
                  <DetailItem
                    icon={<PersonIcon fontSize="small" />}
                    label="Người tạo"
                    value={customer.created_by_name}
                  />
                </Grid>
              )}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CustomerDetail;
