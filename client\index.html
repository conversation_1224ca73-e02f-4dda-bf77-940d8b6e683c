<!doctype html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Customer Management App</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Customer Management Application - Quản lý khách hàng" />
    <meta name="keywords" content="customer, management, quản lý khách hàng" />
    <meta name="author" content="IVC" />
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Customer Management App" />
    <meta property="og:description" content="Ứng dụng quản lý khách hàng" />
    <meta property="og:type" content="website" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    
    <style>
      /* Loading styles */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f8f9fa;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #6366f1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading when app is ready */
      .app-ready #loading {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- Loading indicator -->
    <div id="loading">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // Hide loading when app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-ready');
        }, 100);
      });
    </script>
  </body>
</html>
