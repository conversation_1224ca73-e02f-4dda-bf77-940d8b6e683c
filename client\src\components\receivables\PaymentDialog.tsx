import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  MenuItem,
  Typography,
  Box,
  Alert,
  Chip,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { vi } from 'date-fns/locale';
import { convertDateToISOString } from '../../utils/vietnameseFormatters';

// Services
import paymentService from '../../services/paymentService';

// Types
import { Payment, AllocationPreviewData } from '../../types/payment';
import { Customer } from '../../types/customer';

// Components
import AllocationPreview from './AllocationPreview';

interface PaymentDialogProps {
  open: boolean;
  payment: Payment | null;
  customers: Customer[];
  onClose: () => void;
  onSave: (payment: Payment) => void;
  onError: (error: string) => void;
}

interface FormData {
  customer_id: number | '';
  amount: string;
  payment_date: Date;
  payment_method: string;
  reference_number: string;
  description: string;
  status: string;
}

const PAYMENT_METHODS = [
  { value: 'Tiền mặt', label: 'Tiền mặt' },
  { value: 'Chuyển khoản', label: 'Chuyển khoản' },
  { value: 'Séc', label: 'Séc' },
  { value: 'Thẻ tín dụng', label: 'Thẻ tín dụng' },
  { value: 'Khác', label: 'Khác' }
];

const PaymentDialog: React.FC<PaymentDialogProps> = ({
  open,
  payment,
  customers,
  onClose,
  onSave,
  onError
}) => {
  const [formData, setFormData] = useState<FormData>({
    customer_id: '',
    amount: '',
    payment_date: new Date(),
    payment_method: 'Chuyển khoản',
    reference_number: '',
    description: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const isEdit = !!payment;

  useEffect(() => {
    if (payment) {
      setFormData({
        customer_id: payment.customer_id || '',
        amount: payment.amount?.toString() || '',
        payment_date: payment.payment_date ? new Date(payment.payment_date) : new Date(),
        payment_method: payment.payment_method || 'Chuyển khoản',
        reference_number: payment.reference_number || '',
        description: payment.description || ''
      });
    } else {
      // Reset form for new payment
      setFormData({
        customer_id: '',
        amount: '',
        payment_date: new Date(),
        payment_method: 'Chuyển khoản',
        reference_number: '',
        description: ''
      });
    }
    setErrors({});
  }, [payment, open]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customer_id) {
      newErrors.customer_id = 'Vui lòng chọn khách hàng';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Vui lòng nhập số tiền';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Số tiền phải là số dương';
      }
    }

    if (!formData.payment_method) {
      newErrors.payment_method = 'Vui lòng chọn phương thức thanh toán';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const paymentData = {
        customer_id: formData.customer_id as number,
        amount: parseFloat(formData.amount),
        payment_date: convertDateToISOString(formData.payment_date),
        payment_method: formData.payment_method,
        reference_number: formData.reference_number.trim() || null,
        description: formData.description.trim() || null
      };

      let savedPayment: Payment;

      if (isEdit && payment) {
        savedPayment = await paymentService.updatePayment(payment.id, paymentData);
      } else {
        savedPayment = await paymentService.createPayment(paymentData);
      }

      onSave(savedPayment);
      onClose();
    } catch (error: any) {
      onError(error.message || 'Không thể lưu thanh toán');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  const selectedCustomer = customers.find(c => c.id === formData.customer_id);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: '450px' }
        }}
      >
        <DialogTitle>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {isEdit ? 'Cập nhật Thanh toán' : 'Thêm Thanh toán mới'}
          </Typography>
        </DialogTitle>

        <DialogContent dividers>
          <Grid container spacing={3}>
            {/* Customer Selection */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                select
                label="Khách hàng *"
                value={formData.customer_id}
                onChange={(e) => handleInputChange('customer_id', parseInt(e.target.value) || '')}
                error={!!errors.customer_id}
                helperText={errors.customer_id}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              >
                <MenuItem value="">Chọn khách hàng</MenuItem>
                {Array.isArray(customers) && customers.map((customer) => (
                  <MenuItem key={customer.id} value={customer.id}>
                    <Box>
                      <Typography variant="body2">{customer.name}</Typography>
                      {customer.tax_code && (
                        <Typography variant="caption" color="text.secondary">
                          MST: {customer.tax_code}
                        </Typography>
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Số tiền *"
                type="number"
                value={formData.amount}
                onChange={(e) => handleInputChange('amount', e.target.value)}
                error={!!errors.amount}
                helperText={errors.amount}
                placeholder="0"
                InputProps={{
                  endAdornment: <Typography variant="body2" color="text.secondary">VNĐ</Typography>
                }}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Payment Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Ngày thanh toán *"
                value={formData.payment_date}
                onChange={(date) => handleInputChange('payment_date', date || new Date())}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.payment_date,
                    helperText: errors.payment_date,
                    sx: { '& .MuiInputBase-input': { fontSize: '0.85rem' } }
                  }
                }}
              />
            </Grid>

            {/* Payment Method */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Phương thức thanh toán *"
                value={formData.payment_method}
                onChange={(e) => handleInputChange('payment_method', e.target.value)}
                error={!!errors.payment_method}
                helperText={errors.payment_method}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              >
                {PAYMENT_METHODS.map((method) => (
                  <MenuItem key={method.value} value={method.value}>
                    {method.label}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Reference Number */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Số tham chiếu"
                value={formData.reference_number}
                onChange={(e) => handleInputChange('reference_number', e.target.value)}
                placeholder="VD: TXN123456, CHK001"
                helperText="Số giao dịch, số séc, v.v."
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mô tả"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Mô tả chi tiết về thanh toán..."
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Customer Info Display */}
            {selectedCustomer && (
              <Grid item xs={12}>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                    Thông tin khách hàng:
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip 
                      label={`Tên: ${selectedCustomer.name}`} 
                      size="small" 
                      variant="outlined" 
                    />
                    {selectedCustomer.tax_code && (
                      <Chip 
                        label={`MST: ${selectedCustomer.tax_code}`} 
                        size="small" 
                        variant="outlined" 
                      />
                    )}
                    {selectedCustomer.short_name && (
                      <Chip 
                        label={`Tên ngắn: ${selectedCustomer.short_name}`} 
                        size="small" 
                        variant="outlined" 
                      />
                    )}
                  </Box>
                </Box>
              </Grid>
            )}

            {/* Allocation Preview */}
            {!isEdit && formData.customer_id && formData.amount && parseFloat(formData.amount) > 0 && (
              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
                <Accordion defaultExpanded>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                      Preview Phân bổ FIFO
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <AllocationPreview
                      previewData={{
                        customerId: formData.customer_id as number,
                        amount: parseFloat(formData.amount),
                        paymentDate: convertDateToISOString(formData.payment_date)
                      }}
                      onError={onError}
                    />
                  </AccordionDetails>
                </Accordion>
              </Grid>
            )}

            {/* Info Alert */}
            {!isEdit && (
              <Grid item xs={12}>
                <Alert severity="info" sx={{ fontSize: '0.85rem' }}>
                  <Typography variant="body2">
                    <strong>Phân bổ thanh toán FIFO:</strong> Hệ thống tự động áp dụng phương thức
                    FIFO (First In, First Out) để phân bổ thanh toán vào các công nợ cũ nhất trước.
                    Thanh toán sẽ được tạo với trạng thái "Chờ xác nhận" và cần được xác nhận để kích hoạt phân bổ.
                  </Typography>
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button 
            onClick={handleClose}
            disabled={loading}
            sx={{ fontSize: '0.85rem' }}
          >
            Hủy
          </Button>
          <Button 
            variant="contained"
            onClick={handleSave}
            disabled={loading}
            sx={{ fontSize: '0.85rem' }}
          >
            {loading ? 'Đang lưu...' : (isEdit ? 'Cập nhật' : 'Tạo mới')}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default PaymentDialog;
