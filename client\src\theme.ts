import { createTheme } from '@mui/material/styles';

// Laundry Management System Theme - "Fresh & Clean"
// Color palette inspired by cleanliness, freshness, and eco-friendliness
const theme = createTheme({
  // Palette - Fresh & Clean color scheme for laundry business
  palette: {
    // Primary: Sky Blue - represents cleanliness and freshness
    primary: {
      main: '#0ea5e9',    // Sky Blue
      light: '#38bdf8',   // Light Sky Blue
      dark: '#0284c7',    // Dark Sky Blue
      contrastText: '#ffffff',
    },
    // Secondary: Green - eco-friendly and natural
    secondary: {
      main: '#22c55e',    // Green
      light: '#4ade80',   // Light Green
      dark: '#16a34a',    // Dark Green
      contrastText: '#ffffff',
    },
    // Background: Clean whites and light grays
    background: {
      default: '#f8fafc', // Very light gray - clean and minimal
      paper: '#ffffff',   // Pure white - cleanliness
    },
    // Text: Professional dark grays for readability
    text: {
      primary: '#1e293b',   // Dark slate - professional
      secondary: '#475569', // Medium slate - readable
      disabled: '#94a3b8',  // Light slate - subtle
    },
    // Divider: Subtle gray for clean separation
    divider: '#e2e8f0',
    // Status colors with laundry-appropriate tones
    success: {
      main: '#22c55e',    // Fresh green - completed/clean
      light: '#4ade80',
      dark: '#16a34a',
      contrastText: '#ffffff',
    },
    warning: {
      main: '#f59e0b',    // Warm orange - attention needed
      light: '#fbbf24',
      dark: '#d97706',
      contrastText: '#ffffff',
    },
    error: {
      main: '#ef4444',    // Clean red - errors/issues
      light: '#f87171',
      dark: '#dc2626',
      contrastText: '#ffffff',
    },
    info: {
      main: '#06b6d4',    // Cyan - information/fresh
      light: '#22d3ee',
      dark: '#0891b2',
      contrastText: '#ffffff',
    },
  },
  // Typography - giảm kích thước font
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '1.8rem', // Giảm từ 2.125rem
      fontWeight: 600,
    },
    h2: {
      fontSize: '1.6rem', // Giảm từ 1.875rem
      fontWeight: 600,
    },
    h3: {
      fontSize: '1.4rem', // Giảm từ 1.5rem
      fontWeight: 600,
    },
    h4: {
      fontSize: '1.2rem', // Giảm từ 1.25rem
      fontWeight: 600,
    },
    h5: {
      fontSize: '1.1rem', // Giảm từ 1.125rem
      fontWeight: 600,
    },
    h6: {
      fontSize: '1rem', // Giảm từ 1rem
      fontWeight: 600,
    },
    body1: {
      fontSize: '0.85rem', // Giảm từ 0.875rem
      fontWeight: 500, // Thêm font weight đậm hơn
    },
    body2: {
      fontSize: '0.8rem', // Giảm từ 0.875rem
      fontWeight: 500, // Thêm font weight đậm hơn
    },
    button: {
      fontSize: '0.8rem', // Giảm từ 0.85rem
      textTransform: 'none',
    },
    caption: {
      fontSize: '0.7rem', // Giảm từ 0.75rem
    },
  },
  // Shape - giảm border radius
  shape: {
    borderRadius: 6, // Giảm từ 8
  },
  // Spacing - giảm khoảng cách mặc định thêm nữa
  spacing: (factor: number) => `${0.6 * factor}rem`, // Giảm từ 0.75 xuống 0.6
  // Components - tùy chỉnh các component
  components: {
    // Button
    MuiButton: {
      defaultProps: {
        size: 'small', // Mặc định sử dụng size small
      },
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
          borderRadius: 6,
          padding: '6px 12px', // Giảm padding
          minHeight: 32, // Giảm chiều cao tối thiểu
        },
        sizeSmall: {
          padding: '4px 8px',
          fontSize: '0.75rem',
          minHeight: 28,
        },
        sizeMedium: {
          padding: '6px 12px',
          fontSize: '0.8rem',
          minHeight: 32,
        },
      },
    },
    // TextField
    MuiTextField: {
      defaultProps: {
        size: 'small',
        variant: 'outlined',
      },
      styleOverrides: {
        root: {
          '& .MuiInputBase-root': {
            fontSize: '0.85rem',
          },
          '& .MuiInputLabel-root': {
            fontSize: '0.85rem',
          },
        },
      },
    },
    // Table
    MuiTableCell: {
      styleOverrides: {
        root: {
          fontSize: '0.8rem', // Giảm font size trong table
          padding: '8px 12px', // Giảm padding
          fontWeight: 500, // Font đậm hơn cho nội dung table
        },
        head: {
          fontWeight: 700, // Font đậm hơn cho header
          backgroundColor: '#f8f9fa',
          color: '#1e293b', // Màu chữ đậm hơn
        },
      },
    },
    // Paper
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        },
        elevation1: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        },
        elevation2: {
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)',
        },
        elevation3: {
          boxShadow: '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    // Chip
    MuiChip: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          fontSize: '0.75rem',
          height: 24, // Giảm chiều cao
        },
      },
    },
    // Dialog
    MuiDialog: {
      styleOverrides: {
        paper: {
          borderRadius: 8,
        },
      },
    },
    // Card
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        },
      },
    },
    // AppBar
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)',
        },
      },
    },
    // Drawer
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: '1px solid #e5e7eb',
          boxShadow: 'none',
        },
      },
    },
    // List - Updated for laundry theme
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '2px 8px',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            backgroundColor: 'rgba(14, 165, 233, 0.08)', // Sky blue hover
            transform: 'translateX(2px)',
          },
          '&.Mui-selected': {
            backgroundColor: 'rgba(14, 165, 233, 0.12)', // Sky blue selected
            borderLeft: '3px solid #0ea5e9',
            '&:hover': {
              backgroundColor: 'rgba(14, 165, 233, 0.16)',
            },
          },
        },
      },
    },
    // Pagination
    MuiPagination: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          '& .MuiPaginationItem-root': {
            fontSize: '0.8rem',
            minWidth: 28,
            height: 28,
          },
        },
      },
    },
  },
});

export default theme;
