const receivableModel = require('../models/receivableModel');
const { pool } = require('../db');

/**
 * Receivable Service
 * Xử lý logic nghiệp vụ cho receivables (Công nợ phải thu)
 */

/**
 * <PERSON><PERSON><PERSON> danh sách receivables với filtering và pagination
 * @param {Object} options - Tùy chọn truy vấn
 * @returns {Object} Kết quả với receivables và pagination info
 */
const getReceivables = async (options) => {
  try {
    return await receivableModel.getAllReceivables(options);
  } catch (error) {
    console.error('Error in receivableService.getReceivables:', error);
    throw new Error('Không thể lấy danh sách công nợ phải thu');
  }
};

/**
 * Lấy receivable theo ID
 * @param {number} id - ID của receivable
 * @returns {Object|null} Thông tin receivable
 */
const getReceivableById = async (id) => {
  try {
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    const receivable = await receivableModel.getReceivableById(id);
    if (!receivable) {
      throw new Error('Không tìm thấy công nợ phải thu');
    }

    return receivable;
  } catch (error) {
    console.error('Error in receivableService.getReceivableById:', error);
    throw error;
  }
};

/**
 * Tạo receivable mới
 * @param {Object} receivableData - Dữ liệu receivable
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Receivable vừa tạo
 */
const createReceivable = async (receivableData, createdBy) => {
  try {
    // Validate dữ liệu đầu vào
    await validateReceivableData(receivableData);

    // Kiểm tra invoice number đã tồn tại
    const invoiceExists = await receivableModel.checkInvoiceNumberExists(receivableData.invoice_number);
    if (invoiceExists) {
      throw new Error('Số hóa đơn đã tồn tại');
    }

    // Validate customer và contract tồn tại
    await validateCustomerAndContract(receivableData.customer_id, receivableData.contract_id);

    // Tạo receivable
    const newReceivable = await receivableModel.createReceivable(receivableData, createdBy);
    
    // Lấy thông tin đầy đủ của receivable vừa tạo
    return await receivableModel.getReceivableById(newReceivable.id);
  } catch (error) {
    console.error('Error in receivableService.createReceivable:', error);
    throw error;
  }
};

/**
 * Cập nhật receivable
 * @param {number} id - ID của receivable
 * @param {Object} receivableData - Dữ liệu cập nhật
 * @returns {Object} Receivable sau khi cập nhật
 */
const updateReceivable = async (id, receivableData) => {
  try {
    // Validate ID
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    // Kiểm tra receivable tồn tại
    const existingReceivable = await receivableModel.getReceivableById(id);
    if (!existingReceivable) {
      throw new Error('Không tìm thấy công nợ phải thu');
    }

    // Validate dữ liệu đầu vào
    await validateReceivableData(receivableData);

    // Kiểm tra invoice number đã tồn tại (trừ chính nó)
    if (receivableData.invoice_number !== existingReceivable.invoice_number) {
      const invoiceExists = await receivableModel.checkInvoiceNumberExists(receivableData.invoice_number, id);
      if (invoiceExists) {
        throw new Error('Số hóa đơn đã tồn tại');
      }
    }

    // Validate customer và contract tồn tại
    await validateCustomerAndContract(receivableData.customer_id, receivableData.contract_id);

    // Cập nhật receivable
    const updatedReceivable = await receivableModel.updateReceivable(id, receivableData);
    if (!updatedReceivable) {
      throw new Error('Không thể cập nhật công nợ phải thu');
    }

    // Lấy thông tin đầy đủ của receivable sau khi cập nhật
    return await receivableModel.getReceivableById(id);
  } catch (error) {
    console.error('Error in receivableService.updateReceivable:', error);
    throw error;
  }
};

/**
 * Xóa receivable
 * @param {number} id - ID của receivable
 * @returns {boolean} True nếu xóa thành công
 */
const deleteReceivable = async (id) => {
  try {
    // Validate ID
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    // Kiểm tra receivable tồn tại
    const existingReceivable = await receivableModel.getReceivableById(id);
    if (!existingReceivable) {
      throw new Error('Không tìm thấy công nợ phải thu');
    }

    // Kiểm tra xem receivable đã có payment allocation chưa
    const hasPayments = await checkReceivableHasPayments(id);
    if (hasPayments) {
      throw new Error('Không thể xóa công nợ đã có thanh toán. Vui lòng hủy các thanh toán trước.');
    }

    // Xóa receivable
    const deleted = await receivableModel.deleteReceivable(id);
    if (!deleted) {
      throw new Error('Không thể xóa công nợ phải thu');
    }

    return true;
  } catch (error) {
    console.error('Error in receivableService.deleteReceivable:', error);
    throw error;
  }
};

/**
 * Tạo receivables từ production data
 * @param {Object} options - Tùy chọn tạo bulk
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Kết quả tạo bulk
 */
const createReceivablesFromProduction = async (options, createdBy) => {
  const { contractId, startDate, endDate, invoicePrefix = 'INV' } = options;

  try {
    // Validate input
    if (!contractId || !startDate || !endDate) {
      throw new Error('Thiếu thông tin bắt buộc: contractId, startDate, endDate');
    }

    // Lấy dữ liệu production trong khoảng thời gian
    const productionQuery = `
      SELECT 
        dp.production_date,
        dp.contract_id,
        c.customer_id,
        SUM(dp.total_amount) as total_amount,
        STRING_AGG(DISTINCT p.name, ', ') as products_description
      FROM daily_production dp
      JOIN contracts c ON dp.contract_id = c.id
      JOIN products p ON dp.product_id = p.id
      WHERE dp.contract_id = $1 
      AND dp.production_date BETWEEN $2 AND $3
      GROUP BY dp.production_date, dp.contract_id, c.customer_id
      ORDER BY dp.production_date
    `;

    const productionResult = await pool.query(productionQuery, [contractId, startDate, endDate]);
    
    if (productionResult.rows.length === 0) {
      throw new Error('Không tìm thấy dữ liệu sản xuất trong khoảng thời gian này');
    }

    const createdReceivables = [];
    const errors = [];

    // Tạo receivable cho mỗi ngày sản xuất
    for (const production of productionResult.rows) {
      try {
        const invoiceNumber = `${invoicePrefix}-${production.production_date.toISOString().slice(0, 10).replace(/-/g, '')}`;
        const dueDate = new Date(production.production_date);
        dueDate.setDate(dueDate.getDate() + 30); // Mặc định 30 ngày

        const receivableData = {
          customer_id: production.customer_id,
          contract_id: production.contract_id,
          invoice_number: invoiceNumber,
          transaction_date: production.production_date,
          due_date: dueDate,
          description: `Hóa đơn sản xuất ngày ${production.production_date.toISOString().slice(0, 10)} - ${production.products_description}`,
          original_amount: production.total_amount,
          currency: 'VND',
          payment_terms: 30,
          notes: 'Tạo tự động từ dữ liệu sản xuất'
        };

        // Kiểm tra invoice number đã tồn tại
        const invoiceExists = await receivableModel.checkInvoiceNumberExists(invoiceNumber);
        if (!invoiceExists) {
          const newReceivable = await receivableModel.createReceivable(receivableData, createdBy);
          createdReceivables.push(newReceivable);
        } else {
          errors.push(`Hóa đơn ${invoiceNumber} đã tồn tại`);
        }
      } catch (error) {
        errors.push(`Lỗi tạo hóa đơn cho ngày ${production.production_date}: ${error.message}`);
      }
    }

    return {
      success: createdReceivables.length,
      errors: errors.length,
      createdReceivables,
      errorMessages: errors
    };
  } catch (error) {
    console.error('Error in receivableService.createReceivablesFromProduction:', error);
    throw error;
  }
};

/**
 * Validate dữ liệu receivable
 * @param {Object} receivableData - Dữ liệu cần validate
 */
const validateReceivableData = async (receivableData) => {
  const {
    customer_id,
    contract_id,
    invoice_number,
    transaction_date,
    due_date,
    description,
    original_amount
  } = receivableData;

  // Kiểm tra các trường bắt buộc
  if (!customer_id || !contract_id || !invoice_number || !transaction_date || !due_date || !description || !original_amount) {
    throw new Error('Thiếu thông tin bắt buộc');
  }

  // Validate số tiền
  if (isNaN(original_amount) || original_amount <= 0) {
    throw new Error('Số tiền phải là số dương');
  }

  // Validate ngày
  const transactionDate = new Date(transaction_date);
  const dueDateObj = new Date(due_date);
  
  if (isNaN(transactionDate.getTime()) || isNaN(dueDateObj.getTime())) {
    throw new Error('Ngày không hợp lệ');
  }

  if (dueDateObj < transactionDate) {
    throw new Error('Ngày đến hạn không thể trước ngày giao dịch');
  }
};

/**
 * Validate customer và contract tồn tại
 * @param {number} customerId - ID khách hàng
 * @param {number} contractId - ID hợp đồng
 */
const validateCustomerAndContract = async (customerId, contractId) => {
  // Kiểm tra customer tồn tại
  const customerQuery = 'SELECT id FROM customers WHERE id = $1 AND is_active = true';
  const customerResult = await pool.query(customerQuery, [customerId]);
  if (customerResult.rows.length === 0) {
    throw new Error('Khách hàng không tồn tại hoặc đã bị vô hiệu hóa');
  }

  // Kiểm tra contract tồn tại và thuộc về customer
  const contractQuery = 'SELECT id FROM contracts WHERE id = $1 AND customer_id = $2';
  const contractResult = await pool.query(contractQuery, [contractId, customerId]);
  if (contractResult.rows.length === 0) {
    throw new Error('Hợp đồng không tồn tại hoặc không thuộc về khách hàng này');
  }
};

/**
 * Kiểm tra receivable đã có payment allocation
 * @param {number} receivableId - ID receivable
 * @returns {boolean} True nếu đã có payment
 */
const checkReceivableHasPayments = async (receivableId) => {
  try {
    const query = 'SELECT id FROM payment_allocations WHERE receivable_id = $1 LIMIT 1';
    const result = await pool.query(query, [receivableId]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkReceivableHasPayments:', error);
    return false;
  }
};

/**
 * Tạo receivable từ sản lượng đã chọn
 * @param {Object} receivableData - Dữ liệu receivable từ production
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Receivable vừa tạo
 */
const createReceivableFromProduction = async (receivableData, createdBy) => {
  try {

    // Validate dữ liệu đầu vào
    await validateReceivableData(receivableData);

    // Kiểm tra invoice number đã tồn tại
    const invoiceExists = await receivableModel.checkInvoiceNumberExists(receivableData.invoice_number);
    if (invoiceExists) {
      throw new Error('Số hóa đơn đã tồn tại');
    }

    // Validate customer và contract tồn tại
    await validateCustomerAndContract(receivableData.customer_id, receivableData.contract_id);

    // Validate production IDs tồn tại và chưa được ghi sổ
    const { production_ids } = receivableData;
    if (!production_ids || !Array.isArray(production_ids) || production_ids.length === 0) {
      throw new Error('Danh sách ID sản lượng không hợp lệ');
    }

    // Kiểm tra các production records tồn tại và có thể tạo receivable
    const productionCheckQuery = `
      SELECT id, status, total_amount
      FROM daily_production
      WHERE id = ANY($1) AND contract_id = $2
    `;
    const productionCheckResult = await pool.query(productionCheckQuery, [production_ids, receivableData.contract_id]);

    if (productionCheckResult.rows.length !== production_ids.length) {
      throw new Error('Một số bản ghi sản lượng không tồn tại hoặc không thuộc hợp đồng này');
    }

    // Kiểm tra status của production records
    const invalidStatusRecords = productionCheckResult.rows.filter(row => row.status === 'invoiced');
    if (invalidStatusRecords.length > 0) {
      throw new Error('Một số bản ghi sản lượng đã được ghi sổ, không thể tạo công nợ');
    }

    // Tạo receivable
    const newReceivable = await receivableModel.createReceivable(receivableData, createdBy);

    // Kiểm tra tất cả production phải có status = 'Đã xác nhận'
    const checkStatusQuery = `
      SELECT id, status
      FROM daily_production
      WHERE id = ANY($1)
    `;
    const statusCheck = await pool.query(checkStatusQuery, [production_ids]);

    const invalidProductions = statusCheck.rows.filter(p => p.status !== 'Đã xác nhận');
    if (invalidProductions.length > 0) {
      throw new Error(`Không thể tạo công nợ từ sản lượng chưa được xác nhận. IDs: ${invalidProductions.map(p => p.id).join(', ')}`);
    }

    // Cập nhật status của production records thành 'Đã ghi nhận công nợ'
    const updateProductionQuery = `
      UPDATE daily_production
      SET status = 'Đã ghi nhận công nợ', updated_at = CURRENT_TIMESTAMP
      WHERE id = ANY($1)
    `;
    console.log('Updating production status for IDs:', production_ids);
    const updateResult = await pool.query(updateProductionQuery, [production_ids]);
    console.log('Production update result:', updateResult.rowCount, 'rows affected');

    // Lấy thông tin đầy đủ của receivable vừa tạo
    return await receivableModel.getReceivableById(newReceivable.id);
  } catch (error) {
    console.error('Error in receivableService.createReceivableFromProduction:', error);
    throw error;
  }
};

module.exports = {
  getReceivables,
  getReceivableById,
  createReceivable,
  updateReceivable,
  deleteReceivable,
  createReceivablesFromProduction,
  createReceivableFromProduction
};
