import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// Pages
import Dashboard from './pages/Dashboard';
import Customers from './pages/Customers';
import Products from './pages/Products';
import Contracts from './pages/Contracts';
import Pricing from './pages/Pricing';
import Production from './pages/Production';
import ProductionConfirmation from './pages/ProductionConfirmation';
import ReceivablesManagement from './pages/ReceivablesManagement';
import TestQuickPrice from './pages/TestQuickPrice';
import TestContractSelect from './components/test/TestContractSelect';

// Components
import Sidebar from './components/common/Sidebar';

// Theme
import theme from './theme';

// Import custom CSS for laundry theme
import './styles/laundry-theme.css';

/**
 * Main App Component
 * Cấu hình routing và layout chính của ứng dụng
 *
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 */
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          {/* Routes with sidebar - no authentication required */}
          <Route path="/dashboard" element={
            <Sidebar>
              <Dashboard />
            </Sidebar>
          } />

          <Route path="/customers" element={
            <Sidebar>
              <Customers />
            </Sidebar>
          } />

          <Route path="/products" element={
            <Sidebar>
              <Products />
            </Sidebar>
          } />

          <Route path="/contracts" element={
            <Sidebar>
              <Contracts />
            </Sidebar>
          } />

          <Route path="/pricing" element={
            <Sidebar>
              <Pricing />
            </Sidebar>
          } />
          <Route path="/production" element={
            <Sidebar>
              <Production />
            </Sidebar>
          } />

          <Route path="/production-confirmation" element={
            <Sidebar>
              <ProductionConfirmation />
            </Sidebar>
          } />

          <Route path="/receivables" element={
            <Sidebar>
              <ReceivablesManagement />
            </Sidebar>
          } />

          <Route path="/test-quick-price" element={
            <Sidebar>
              <TestQuickPrice />
            </Sidebar>
          } />

          <Route path="/test-contract-select" element={
            <Sidebar>
              <TestContractSelect />
            </Sidebar>
          } />

          {/* Default route */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />

          {/* Catch all route */}
          <Route path="*" element={
            <Sidebar>
              <Navigate to="/dashboard" replace />
            </Sidebar>
          } />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
