const contractModel = require('../models/contractModel');
const {
  createResponse,
  createListResponse,
  processQueryParams,
  validateRequiredFields,
  sanitizeString
} = require('../utils/responseUtils');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * L<PERSON>y tất cả hợp đồng
 * @route GET /api/v1/contracts
 * @access Private
 */
const getAllContracts = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);

  const options = {
    page: pagination.page,
    limit: pagination.limit,
    search: filters.search,
    customer_id: filters.customer_id,
    status: filters.status,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await contractModel.getAllContracts(options);

  const response = createListResponse(
    result.contracts,
    result.pagination,
    req
  );

  res.status(200).json(response);
});

/**
 * L<PERSON>y hợp đồng theo ID
 * @route GET /api/v1/contracts/:id
 * @access Private
 */
const getContractById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['ID phải là một số nguyên']);
  }

  const contract = await contractModel.getContractById(parseInt(id));

  if (!contract) {
    throw new NotFoundError('Không tìm thấy hợp đồng', [`Không tìm thấy hợp đồng với ID ${id}`]);
  }

  res.status(200).json(createResponse(contract));
});

/**
 * Tạo hợp đồng mới
 * @route POST /api/v1/contracts
 * @access Private (Manager+)
 */
const createContract = asyncHandler(async (req, res) => {
  const {
    contract_number,
    customer_id,
    contract_name,
    start_date,
    end_date,
    status,
    notes
  } = req.body;

  // Validate required fields
  const requiredFields = ['contract_number', 'customer_id', 'contract_name', 'start_date'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_number format
  if (contract_number && !/^[A-Za-z0-9\/\-]{3,100}$/.test(contract_number)) {
    validationErrors.push({
      field: 'contract_number',
      message: 'Số hợp đồng phải từ 3-100 ký tự, chỉ chứa chữ, số, dấu / và -'
    });
  }

  // Validate customer_id
  if (customer_id && isNaN(parseInt(customer_id))) {
    validationErrors.push({
      field: 'customer_id',
      message: 'ID khách hàng phải là một số nguyên'
    });
  }

  // Validate status
  const allowedStatuses = ['active', 'paused', 'terminated', 'expired'];
  if (status && !allowedStatuses.includes(status)) {
    validationErrors.push({
      field: 'status',
      message: `Trạng thái phải là một trong: ${allowedStatuses.join(', ')}`
    });
  }

  // Validate dates
  if (start_date && isNaN(Date.parse(start_date))) {
    validationErrors.push({
      field: 'start_date',
      message: 'Ngày bắt đầu không hợp lệ'
    });
  }

  if (end_date && isNaN(Date.parse(end_date))) {
    validationErrors.push({
      field: 'end_date',
      message: 'Ngày kết thúc không hợp lệ'
    });
  }

  if (start_date && end_date && new Date(end_date) <= new Date(start_date)) {
    validationErrors.push({
      field: 'end_date',
      message: 'Ngày kết thúc phải sau ngày bắt đầu'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra số hợp đồng đã tồn tại
  const numberExists = await contractModel.checkContractNumberExists(contract_number);
  if (numberExists) {
    throw new ConflictError(
      'Số hợp đồng đã tồn tại',
      ['Số hợp đồng này đã được sử dụng']
    );
  }

  // Sanitize input data
  const contractData = {
    contract_number: sanitizeString(contract_number).toUpperCase(),
    customer_id: parseInt(customer_id),
    contract_name: sanitizeString(contract_name),
    start_date: start_date,
    end_date: end_date || null,
    status: status || 'active',
    notes: sanitizeString(notes)
  };

  // Tạm thời sử dụng user ID mặc định khi authentication bị tắt
  const userId = req.user ? req.user.id : 1; // Default to admin user
  const newContract = await contractModel.createContract(contractData, userId);

  res.status(201).json(createResponse(newContract, 'Tạo hợp đồng thành công'));
});

/**
 * Cập nhật hợp đồng
 * @route PUT /api/v1/contracts/:id
 * @access Private (Manager+)
 */
const updateContract = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    customer_id,
    contract_name,
    start_date,
    end_date,
    status,
    notes
  } = req.body;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Validate required fields
  const requiredFields = ['customer_id', 'contract_name', 'start_date'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate customer_id
  if (customer_id && isNaN(parseInt(customer_id))) {
    validationErrors.push({
      field: 'customer_id',
      message: 'ID khách hàng phải là một số nguyên'
    });
  }

  // Validate status
  const allowedStatuses = ['active', 'paused', 'terminated', 'expired'];
  if (status && !allowedStatuses.includes(status)) {
    validationErrors.push({
      field: 'status',
      message: `Trạng thái phải là một trong: ${allowedStatuses.join(', ')}`
    });
  }

  // Validate dates
  if (start_date && isNaN(Date.parse(start_date))) {
    validationErrors.push({
      field: 'start_date',
      message: 'Ngày bắt đầu không hợp lệ'
    });
  }

  if (end_date && isNaN(Date.parse(end_date))) {
    validationErrors.push({
      field: 'end_date',
      message: 'Ngày kết thúc không hợp lệ'
    });
  }

  if (start_date && end_date && new Date(end_date) <= new Date(start_date)) {
    validationErrors.push({
      field: 'end_date',
      message: 'Ngày kết thúc phải sau ngày bắt đầu'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra hợp đồng tồn tại
  const existingContract = await contractModel.getContractById(parseInt(id));
  if (!existingContract) {
    throw new NotFoundError('Không tìm thấy hợp đồng', [`Không tìm thấy hợp đồng với ID ${id}`]);
  }

  // Sanitize input data
  const contractData = {
    customer_id: parseInt(customer_id),
    contract_name: sanitizeString(contract_name),
    start_date: start_date,
    end_date: end_date || null,
    status: status || 'active',
    notes: sanitizeString(notes)
  };

  const updatedContract = await contractModel.updateContract(parseInt(id), contractData);

  if (!updatedContract) {
    throw new NotFoundError('Không thể cập nhật hợp đồng', ['Hợp đồng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(updatedContract, 'Cập nhật hợp đồng thành công'));
});

/**
 * Xóa hợp đồng (hard delete)
 * @route DELETE /api/v1/contracts/:id
 * @access Private (Admin only)
 */
const deleteContract = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Kiểm tra hợp đồng tồn tại
  const existingContract = await contractModel.getContractById(parseInt(id));
  if (!existingContract) {
    throw new NotFoundError('Không tìm thấy hợp đồng', [`Không tìm thấy hợp đồng với ID ${id}`]);
  }

  const deleted = await contractModel.deleteContract(parseInt(id));

  if (!deleted) {
    throw new NotFoundError('Không thể xóa hợp đồng', ['Hợp đồng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(
    { message: 'Xóa hợp đồng thành công', id: parseInt(id) }
  ));
});

/**
 * Lấy danh sách trạng thái hợp đồng
 * @route GET /api/v1/contracts/statuses
 * @access Private
 */
const getContractStatuses = asyncHandler(async (req, res) => {
  const statuses = await contractModel.getContractStatuses();

  res.status(200).json(createResponse(statuses));
});

/**
 * Lấy hợp đồng theo khách hàng
 * @route GET /api/v1/contracts/customer/:customerId
 * @access Private
 */
const getContractsByCustomer = asyncHandler(async (req, res) => {
  const { customerId } = req.params;

  // Validate customerId
  if (!customerId || isNaN(parseInt(customerId))) {
    throw new ValidationError('ID khách hàng không hợp lệ', ['ID phải là một số nguyên']);
  }

  const contracts = await contractModel.getContractsByCustomer(parseInt(customerId));

  res.status(200).json(createResponse(contracts));
});

/**
 * Tìm kiếm hợp đồng
 * @route GET /api/v1/contracts/search
 * @access Private
 */
const searchContracts = asyncHandler(async (req, res) => {
  const { q, limit = 10 } = req.query;

  if (!q || q.trim().length < 2) {
    throw new ValidationError('Từ khóa tìm kiếm phải có ít nhất 2 ký tự');
  }

  const options = {
    page: 1,
    limit: parseInt(limit),
    search: q.trim()
  };

  const result = await contractModel.getAllContracts(options);

  res.status(200).json(createResponse(result.contracts));
});

module.exports = {
  getAllContracts,
  getContractById,
  createContract,
  updateContract,
  deleteContract,
  getContractStatuses,
  getContractsByCustomer,
  searchContracts
};
