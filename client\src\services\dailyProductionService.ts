/**
 * Daily Production Service
 * API service for daily production management
 */

import { get, post, put, del, ApiResponse } from './api';
import {
  DailyProduction,
  DailyProductionCreateRequest,
  DailyProductionUpdateRequest,
  DailyProductionFilterOptions,
  BulkProductionCreateRequest,
  BulkProductionResult,
  ProductionReport,
  ProductionReportRequest,
  ProductionSummary,
  BulkStatusUpdateRequest,
  BulkStatusUpdateResult,
  ProductionConfirmationFilter,
  ProductionStatusStats,
  MonthlyProductionSummary,
  ProductionStatus
} from '../types/dailyProduction';

/**
 * Daily Production API Service
 */
export const dailyProductionService = {
  /**
   * <PERSON><PERSON>y tất cả sản lượng với filter và pagination
   */
  getAll: async (options?: DailyProductionFilterOptions): Promise<ApiResponse<DailyProduction[]> & {
    summary?: ProductionSummary;
  }> => {
    const params = new URLSearchParams();

    if (options?.page) params.append('page', options.page.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.production_date) params.append('production_date', options.production_date);
    if (options?.contract_id) params.append('contract_id', options.contract_id.toString());
    if (options?.product_id) params.append('product_id', options.product_id.toString());
    if (options?.date_from) params.append('date_from', options.date_from);
    if (options?.date_to) params.append('date_to', options.date_to);
    if (options?.search) params.append('search', options.search);
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const url = queryString ? `/daily-production?${queryString}` : '/daily-production';

    return get<DailyProduction[]>(url);
  },

  /**
   * Lấy sản lượng theo ID
   */
  getById: async (id: number): Promise<ApiResponse<DailyProduction>> => {
    return get<DailyProduction>(`/daily-production/${id}`);
  },

  /**
   * Tạo sản lượng mới
   */
  create: async (data: DailyProductionCreateRequest): Promise<ApiResponse<DailyProduction>> => {
    return post<DailyProduction>('/daily-production', data);
  },

  /**
   * Cập nhật sản lượng
   */
  update: async (id: number, data: DailyProductionUpdateRequest): Promise<ApiResponse<DailyProduction>> => {
    return put<DailyProduction>(`/daily-production/${id}`, data);
  },

  /**
   * Xóa sản lượng
   */
  delete: async (id: number): Promise<ApiResponse<{ message: string; id: number }>> => {
    return del<{ message: string; id: number }>(`/daily-production/${id}`);
  },

  /**
   * Tạo sản lượng hàng loạt
   */
  createBulk: async (data: BulkProductionCreateRequest): Promise<ApiResponse<BulkProductionResult>> => {
    return post<BulkProductionResult>('/daily-production/bulk', data);
  },

  /**
   * Lấy sản lượng theo ngày cụ thể
   */
  getByDate: async (productionDate: string, contractId?: number): Promise<ApiResponse<DailyProduction[]>> => {
    const params = new URLSearchParams({
      production_date: productionDate,
    });

    if (contractId) params.append('contract_id', contractId.toString());

    return get<DailyProduction[]>(`/daily-production/by-date?${params.toString()}`);
  },

  /**
   * Lấy báo cáo sản lượng
   */
  getReport: async (request: ProductionReportRequest): Promise<ApiResponse<ProductionReport>> => {
    const params = new URLSearchParams({
      date_from: request.date_from,
      date_to: request.date_to,
    });

    if (request.contract_id) params.append('contract_id', request.contract_id.toString());

    return get<ProductionReport>(`/daily-production/report?${params.toString()}`);
  },

  /**
   * Kiểm tra sản lượng đã tồn tại
   */
  checkExists: async (
    productionDate: string,
    contractId: number,
    productId: number,
    excludeId?: number
  ): Promise<ApiResponse<{ exists: boolean }>> => {
    const params = new URLSearchParams({
      production_date: productionDate,
      contract_id: contractId.toString(),
      product_id: productId.toString(),
    });

    if (excludeId) params.append('exclude_id', excludeId.toString());

    return get<{ exists: boolean }>(`/daily-production/check-exists?${params.toString()}`);
  },

  /**
   * Lấy thống kê sản lượng
   */
  getStatistics: async (dateFrom?: string, dateTo?: string): Promise<ApiResponse<{
    total_records: number;
    total_quantity: number;
    total_amount: number;
    avg_daily_production: number;
    top_products: Array<{
      product_id: number;
      product_name: string;
      total_quantity: number;
      total_amount: number;
    }>;
    top_contracts: Array<{
      contract_id: number;
      contract_number: string;
      total_quantity: number;
      total_amount: number;
    }>;
  }>> => {
    const params = new URLSearchParams();

    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    const queryString = params.toString();
    const url = queryString ? `/daily-production/statistics?${queryString}` : '/daily-production/statistics';

    return get<{
      total_records: number;
      total_quantity: number;
      total_amount: number;
      avg_daily_production: number;
      top_products: Array<{
        product_id: number;
        product_name: string;
        total_quantity: number;
        total_amount: number;
      }>;
      top_contracts: Array<{
        contract_id: number;
        contract_number: string;
        total_quantity: number;
        total_amount: number;
      }>;
    }>(url);
  },

  /**
   * Lấy sản lượng theo hợp đồng
   */
  getByContract: async (contractId: number, dateFrom?: string, dateTo?: string): Promise<ApiResponse<DailyProduction[]>> => {
    const params = new URLSearchParams({
      contract_id: contractId.toString(),
    });

    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    return get<DailyProduction[]>(`/daily-production/by-contract?${params.toString()}`);
  },

  /**
   * Lấy sản lượng theo sản phẩm
   */
  getByProduct: async (productId: number, dateFrom?: string, dateTo?: string): Promise<ApiResponse<DailyProduction[]>> => {
    const params = new URLSearchParams({
      product_id: productId.toString(),
    });

    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);

    return get<DailyProduction[]>(`/daily-production/by-product?${params.toString()}`);
  },

  /**
   * Validate dữ liệu sản lượng
   */
  validate: async (data: Partial<DailyProductionCreateRequest | DailyProductionUpdateRequest>): Promise<ApiResponse<{
    isValid: boolean;
    errors: Array<{ field: string; message: string }>;
  }>> => {
    return post<{
      isValid: boolean;
      errors: Array<{ field: string; message: string }>;
    }>('/daily-production/validate', data);
  },

  /**
   * Bulk delete nhiều sản lượng
   */
  bulkDelete: async (ids: number[]): Promise<ApiResponse<{
    deleted: number;
    failed: number;
    errors: Array<{ id: number; error: string }>;
  }>> => {
    return del<{
      deleted: number;
      failed: number;
      errors: Array<{ id: number; error: string }>;
    }>('/daily-production/bulk-delete', { ids });
  },

  /**
   * Bulk update status cho nhiều sản lượng
   */
  bulkUpdateStatus: async (data: BulkStatusUpdateRequest): Promise<ApiResponse<BulkStatusUpdateResult>> => {
    return put<BulkStatusUpdateResult>('/daily-production/bulk-status', data);
  },

  /**
   * Lấy sản lượng theo tháng hoặc giai đoạn để xác nhận
   */
  getForConfirmation: async (filter: ProductionConfirmationFilter): Promise<ApiResponse<DailyProduction[]>> => {
    const params = new URLSearchParams();

    // Nếu có year và month thì dùng chế độ tháng
    if (filter.year && filter.month) {
      params.append('year', filter.year.toString());
      params.append('month', filter.month.toString());
    }

    // Nếu có start_date và end_date thì dùng chế độ giai đoạn
    if (filter.start_date && filter.end_date) {
      params.append('start_date', filter.start_date);
      params.append('end_date', filter.end_date);
    }

    if (filter.contract_id) params.append('contract_id', filter.contract_id.toString());
    if (filter.status) params.append('status', filter.status);

    return get<DailyProduction[]>(`/daily-production/for-confirmation?${params.toString()}`);
  },

  /**
   * Lấy thống kê sản lượng theo trạng thái
   */
  getStatusStats: async (year?: number, month?: number, contractId?: number, startDate?: string, endDate?: string): Promise<ApiResponse<ProductionStatusStats[]>> => {
    const params = new URLSearchParams();

    if (year) params.append('year', year.toString());
    if (month) params.append('month', month.toString());
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    if (contractId) params.append('contract_id', contractId.toString());

    const queryString = params.toString();
    const url = queryString ? `/daily-production/status-stats?${queryString}` : '/daily-production/status-stats';

    return get<ProductionStatusStats[]>(url);
  },

  /**
   * Lấy sản lượng đã nhóm theo hợp đồng và ngày
   */
  getGrouped: async (options: DailyProductionFilterOptions & {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}): Promise<ApiResponse<{
    data: GroupedProduction[];
    pagination: {
      page: number;
      limit: number;
      totalCount: number;
      totalPages: number;
    };
  }>> => {
    const params = new URLSearchParams();

    if (options.date_from) params.append('date_from', options.date_from);
    if (options.date_to) params.append('date_to', options.date_to);
    if (options.contract_id) params.append('contract_id', options.contract_id.toString());
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortOrder) params.append('sortOrder', options.sortOrder);

    return get(`/daily-production/grouped?${params.toString()}`);
  },

  /**
   * Tìm kiếm sản lượng để tạo receivable
   */
  searchForReceivable: async (
    contractId: number,
    status?: ProductionStatus,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<DailyProduction[]>> => {
    const params = new URLSearchParams({
      contractId: contractId.toString(),
    });

    if (status) params.append('status', status);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    return get<DailyProduction[]>(`/daily-production/search?${params.toString()}`);
  },

  /**
   * Lấy tóm tắt sản lượng theo tháng
   */
  getMonthlyProductionSummary: async (
    year: number,
    month: number,
    contractId?: number
  ): Promise<ApiResponse<MonthlyProductionSummary>> => {
    const statusStats = await dailyProductionService.getStatusStats(year, month, contractId);

    if (!statusStats.success || !statusStats.data) {
      throw new Error('Không thể lấy thống kê sản lượng');
    }

    const totalRecords = statusStats.data.reduce((sum, stat) => sum + stat.count, 0);
    const totalAmount = statusStats.data.reduce((sum, stat) => sum + stat.total_amount, 0);

    return {
      success: true,
      data: {
        year,
        month,
        contract_id: contractId,
        stats: statusStats.data,
        total_records: totalRecords,
        total_amount: totalAmount,
      },
      message: 'Lấy tóm tắt sản lượng thành công'
    };
  },

  /**
   * Import sản lượng từ Excel
   */
  importFromExcel: async (data: Array<{
    contract_number: string;
    production_date: string;
    product_code: string;
    quantity: number;
    notes?: string;
  }>): Promise<ApiResponse<{
    total: number;
    success: number;
    failed: number;
    errors: Array<{
      row: number;
      data: any;
      error: string;
    }>;
  }>> => {
    return post('/daily-production/import', { data });
  },

  /**
   * Lấy báo cáo sản lượng theo tháng
   */
  getMonthlyReport: async (params: {
    year: number;
    month: number;
    contract_id: number;
    status?: string;
  }): Promise<ApiResponse<any>> => {
    const queryParams = new URLSearchParams({
      year: params.year.toString(),
      month: params.month.toString(),
      contract_id: params.contract_id.toString(),
    });

    if (params.status && params.status !== 'all') {
      queryParams.append('status', params.status);
    }

    return get(`/daily-production/monthly-report?${queryParams.toString()}`);
  },
};

export default dailyProductionService;
