const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');
const { requireAdmin } = require('../middleware/permission');

const router = express.Router();

/**
 * @route   POST /api/v1/auth/login
 * @desc    Đăng nhập
 * @access  Public
 */
router.post('/login', [
  body('email')
    .isEmail()
    .withMessage('Email không đúng định dạng')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('Mật khẩu không được để trống')
    .isLength({ min: 1 })
    .withMessage('Mật khẩu không được để trống')
], authController.login);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    Đăng xuất
 * @access  Private
 */
router.post('/logout', authenticateToken, authController.logout);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token không được để trống')
], authController.refreshToken);

/**
 * @route   GET /api/v1/auth/me
 * @desc    Lấy thông tin user hiện tại
 * @access  Private
 */
router.get('/me', authenticateToken, authController.getCurrentUser);

/**
 * @route   POST /api/v1/auth/register
 * @desc    Đăng ký user mới (chỉ admin)
 * @access  Private (Admin only)
 */
router.post('/register', [
  authenticateToken,
  requireAdmin,
  body('name')
    .notEmpty()
    .withMessage('Tên không được để trống')
    .isLength({ min: 1, max: 100 })
    .withMessage('Tên phải từ 1 đến 100 ký tự')
    .trim(),
  body('email')
    .isEmail()
    .withMessage('Email không đúng định dạng')
    .isLength({ max: 100 })
    .withMessage('Email không được quá 100 ký tự')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Mật khẩu phải có ít nhất 6 ký tự'),
  body('position')
    .optional({ nullable: true })
    .isLength({ max: 100 })
    .withMessage('Chức vụ không được quá 100 ký tự')
    .trim()
], authController.register);

module.exports = router;
