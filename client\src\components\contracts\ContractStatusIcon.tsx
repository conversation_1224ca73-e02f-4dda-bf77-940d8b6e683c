/**
 * Contract Status Icon Component
 * Hiển thị trạng thái hợp đồng bằng biểu tượng với tooltip
 */

import React from 'react';
import { Tooltip, Box } from '@mui/material';
import { 
  CheckCircle as ActiveIcon,
  Pause as PausedIcon,
  Cancel as TerminatedIcon,
  Schedule as ExpiredIcon
} from '@mui/icons-material';
import { ContractStatus } from '../../types/contract';

interface ContractStatusIconProps {
  status: ContractStatus;
  size?: 'small' | 'medium' | 'large';
  showTooltip?: boolean;
}

const ContractStatusIcon: React.FC<ContractStatusIconProps> = ({
  status,
  size = 'small',
  showTooltip = true
}) => {
  const getStatusConfig = (status: ContractStatus) => {
    const configs = {
      active: {
        icon: ActiveIcon,
        color: '#4caf50', // success green
        label: 'Đang hoạt động',
        tooltip: 'Hợp đồng đang được thực hiện'
      },
      paused: {
        icon: PausedIcon,
        color: '#ff9800', // warning orange
        label: 'Tạm dừng',
        tooltip: 'Hợp đồng đang tạm dừng'
      },
      terminated: {
        icon: TerminatedIcon,
        color: '#f44336', // error red
        label: 'Đã chấm dứt',
        tooltip: 'Hợp đồng đã được chấm dứt'
      },
      expired: {
        icon: ExpiredIcon,
        color: '#9e9e9e', // grey
        label: 'Đã hết hạn',
        tooltip: 'Hợp đồng đã hết hạn'
      }
    };

    return configs[status] || configs.active;
  };

  const config = getStatusConfig(status);
  const IconComponent = config.icon;

  const iconSize = {
    small: '18px',
    medium: '24px',
    large: '32px'
  }[size];

  const iconElement = (
    <Box
      component="span"
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: iconSize,
        height: iconSize,
      }}
    >
      <IconComponent
        sx={{
          fontSize: iconSize,
          color: config.color,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'scale(1.1)',
          }
        }}
      />
    </Box>
  );

  if (!showTooltip) {
    return iconElement;
  }

  return (
    <Tooltip 
      title={config.tooltip} 
      arrow 
      placement="top"
      enterDelay={500}
      leaveDelay={200}
    >
      {iconElement}
    </Tooltip>
  );
};

export default ContractStatusIcon;
