const express = require('express');
const { body, param, query } = require('express-validator');
const contractController = require('../controllers/contractController');
const { authenticateToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

const router = express.Router();

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// Middleware xác thực cho tất cả routes
// router.use(authenticateToken);

/**
 * @route   GET /api/v1/contracts/statuses
 * @desc    Lấy danh sách trạng thái hợp đồng có sẵn
 * @access  Private
 */
router.get('/statuses', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'view')
], contractController.getContractStatuses);

/**
 * @route   GET /api/v1/contracts/search
 * @desc    Tìm kiếm hợp đồng
 * @access  Private
 */
router.get('/search', [
  // NOTE: <PERSON><PERSON><PERSON> thời comment out permission check
  // checkPermission('contract', 'view'),
  query('q')
    .isLength({ min: 2 })
    .withMessage('Từ khóa tìm kiếm phải có ít nhất 2 ký tự'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit phải là số nguyên từ 1-100')
], contractController.searchContracts);

/**
 * @route   GET /api/v1/contracts/customer/:customerId
 * @desc    Lấy hợp đồng theo khách hàng
 * @access  Private
 */
router.get('/customer/:customerId', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'view'),
  param('customerId')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương')
], contractController.getContractsByCustomer);

/**
 * @route   GET /api/v1/contracts
 * @desc    Lấy tất cả hợp đồng với phân trang và filter
 * @access  Private
 */
router.get('/', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'view'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit phải là số nguyên từ 1-1000'),
  query('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID phải là số nguyên dương'),
  query('status')
    .optional()
    .isIn(['active', 'paused', 'terminated', 'expired'])
    .withMessage('Status phải là: active, paused, terminated, expired')
], contractController.getAllContracts);

/**
 * @route   GET /api/v1/contracts/:id
 * @desc    Lấy hợp đồng theo ID
 * @access  Private
 */
router.get('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'view'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương')
], contractController.getContractById);

/**
 * @route   POST /api/v1/contracts
 * @desc    Tạo hợp đồng mới
 * @access  Private (Manager+)
 */
router.post('/', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'create'),
  body('contract_number')
    .notEmpty()
    .withMessage('Số hợp đồng là bắt buộc')
    .isLength({ min: 3, max: 100 })
    .withMessage('Số hợp đồng phải từ 3-100 ký tự')
    .matches(/^[A-Za-z0-9\/\-]+$/)
    .withMessage('Số hợp đồng chỉ chứa chữ, số, dấu / và -'),
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương'),
  body('contract_name')
    .notEmpty()
    .withMessage('Tên hợp đồng là bắt buộc')
    .isLength({ min: 3, max: 200 })
    .withMessage('Tên hợp đồng phải từ 3-200 ký tự'),
  body('start_date')
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  body('status')
    .optional()
    .isIn(['active', 'paused', 'terminated', 'expired'])
    .withMessage('Trạng thái phải là: active, paused, terminated, expired'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Ghi chú không được vượt quá 1000 ký tự')
], contractController.createContract);

/**
 * @route   PUT /api/v1/contracts/:id
 * @desc    Cập nhật hợp đồng
 * @access  Private (Manager+)
 */
router.put('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'update'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('customer_id')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương'),
  body('contract_name')
    .notEmpty()
    .withMessage('Tên hợp đồng là bắt buộc')
    .isLength({ min: 3, max: 200 })
    .withMessage('Tên hợp đồng phải từ 3-200 ký tự'),
  body('start_date')
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  body('end_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  body('status')
    .optional()
    .isIn(['active', 'paused', 'terminated', 'expired'])
    .withMessage('Trạng thái phải là: active, paused, terminated, expired'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Ghi chú không được vượt quá 1000 ký tự')
], contractController.updateContract);

/**
 * @route   DELETE /api/v1/contracts/:id
 * @desc    Xóa hợp đồng
 * @access  Private (Admin only)
 */
router.delete('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('contract', 'delete'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương')
], contractController.deleteContract);

module.exports = router;
