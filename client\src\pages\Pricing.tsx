/**
 * Pricing Page
 * Trang quản lý đơn gi<PERSON> sản phẩm trong hợp đồng
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Button,
  Paper,
  Snackbar
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';
import { useSearchParams } from 'react-router-dom';

// Components
import PricingTable from '../components/pricing/PricingTable';
import PricingDialog from '../components/pricing/PricingDialog';
import PriceHistory from '../components/pricing/PriceHistory';
import QuickPriceForm from '../components/pricing/QuickPriceForm';
import ConfirmDialog from '../components/common/ConfirmDialog';

// Types
import { ContractPrice, ContractPriceFormData, ContractPriceFilterOptions, PriceHistoryItem } from '../types/contractPrice';
import { Contract } from '../types/contract';
import { Product } from '../types/product';

// Services
import { contractPriceService } from '../services/contractPriceService';
import { contractService } from '../services/contractService';
import { productService } from '../services/productService';

const Pricing: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [prices, setPrices] = useState<ContractPrice[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Pagination & Filtering
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ContractPriceFilterOptions>({});

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPrice, setSelectedPrice] = useState<ContractPrice | null>(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dialogError, setDialogError] = useState<string | null>(null);

  // History dialog
  const [historyOpen, setHistoryOpen] = useState(false);
  const [historyPrices, setHistoryPrices] = useState<PriceHistoryItem[]>([]);
  const [historyLoading, setHistoryLoading] = useState(false);

  // Delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Quick Price Form
  const [quickPriceOpen, setQuickPriceOpen] = useState(false);

  // Load data
  const loadPrices = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const options: ContractPriceFilterOptions = {
        page: page + 1,
        limit: rowsPerPage,
        ...filters,
      };

      const response = await contractPriceService.getAll(options);

      if (response.success) {
        setPrices(response.data);
        setTotalCount(response.meta?.pagination?.total || 0);
      } else {
        throw new Error('Không thể tải danh sách đơn giá');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi tải dữ liệu');
      setPrices([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, searchQuery, filters]);

  const loadContracts = useCallback(async () => {
    try {
      const response = await contractService.getAll({ limit: 1000 });
      if (response.success) {
        setContracts(response.data);
      }
    } catch (err) {
      console.error('Error loading contracts:', err);
    }
  }, []);

  const loadProducts = useCallback(async () => {
    try {
      const response = await productService.getAll({ limit: 1000 });
      if (response.success) {
        setProducts(response.data);
      }
    } catch (err) {
      console.error('Error loading products:', err);
    }
  }, []);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadPrices();
  }, [loadPrices]);

  useEffect(() => {
    loadContracts();
    loadProducts();
  }, [loadContracts, loadProducts]);

  // Event handlers
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  }, []);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(0);
  }, []);

  const handleFilter = useCallback((newFilters: ContractPriceFilterOptions) => {
    setFilters(newFilters);
    setPage(0);
  }, []);

  const handleAdd = useCallback(() => {
    setSelectedPrice(null);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleEdit = useCallback((price: ContractPrice) => {
    setSelectedPrice(price);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleDelete = useCallback((price: ContractPrice) => {
    setSelectedPrice(price);
    setDeleteDialogOpen(true);
  }, []);

  const handleViewHistory = useCallback(async (price: ContractPrice) => {
    setHistoryLoading(true);
    setHistoryOpen(true);
    setSelectedPrice(price);

    try {
      const response = await contractPriceService.getHistory(price.contract_id, price.product_id);
      if (response.success) {
        setHistoryPrices(response.data);
      } else {
        setHistoryPrices([]);
      }
    } catch (err) {
      console.error('Error loading price history:', err);
      setHistoryPrices([]);
    } finally {
      setHistoryLoading(false);
    }
  }, []);

  const handleDialogClose = useCallback(() => {
    setDialogOpen(false);
    setSelectedPrice(null);
    setDialogError(null);
  }, []);

  const handleDialogSubmit = useCallback(async (data: ContractPriceFormData) => {
    setDialogLoading(true);
    setDialogError(null);

    try {
      let response;
      if (selectedPrice) {
        response = await contractPriceService.update(selectedPrice.id, data);
      } else {
        response = await contractPriceService.create(data);
      }

      if (response.success) {
        setSuccessMessage(
          selectedPrice ? 'Đơn giá đã được cập nhật thành công' : 'Đơn giá đã được thêm thành công'
        );
        handleDialogClose();
        loadPrices();
      } else {
        throw new Error('Có lỗi xảy ra');
      }
    } catch (err: any) {
      setDialogError(err.message || 'Có lỗi xảy ra khi lưu đơn giá');
    } finally {
      setDialogLoading(false);
    }
  }, [selectedPrice, loadPrices, handleDialogClose]);

  const handleDeleteConfirm = useCallback(async () => {
    if (!selectedPrice) return;

    try {
      const response = await contractPriceService.delete(selectedPrice.id);
      if (response.success) {
        setSuccessMessage('Đơn giá đã được xóa thành công');
        setDeleteDialogOpen(false);
        setSelectedPrice(null);
        loadPrices();
      } else {
        throw new Error('Có lỗi xảy ra');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi xóa đơn giá');
      setDeleteDialogOpen(false);
    }
  }, [selectedPrice, loadPrices]);

  const handleExport = useCallback(async () => {
    try {
      const blob = await contractPriceService.exportToExcel(filters);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `don-gia-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError('Có lỗi xảy ra khi xuất Excel');
    }
  }, [filters]);

  // Quick Price Form handlers
  const handleQuickPriceSubmit = useCallback(async (data: any) => {
    try {
      // Create multiple prices
      for (const item of data.items) {
        const priceData = {
          contract_id: data.contract_id,
          product_id: item.product_id,
          price: item.price,
          effective_date: data.effective_date,
          expiry_date: data.expiry_date || null,
          notes: `Quick price update - ${new Date().toISOString()}`
        };
        await contractPriceService.create(priceData);
      }

      setSuccessMessage('Cập nhật giá nhanh thành công');
      setQuickPriceOpen(false);
      loadPrices();
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi cập nhật giá');
    }
  }, [loadPrices]);

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Quản lý Đơn giá
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Quản lý đơn giá sản phẩm trong các hợp đồng
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setQuickPriceOpen(true)}
          sx={{ height: 'fit-content' }}
        >
          Cập nhật giá nhanh
        </Button>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Pricing Table */}
      <PricingTable
        prices={prices}
        loading={loading}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        searchQuery={searchQuery}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSearch={handleSearch}
        onFilter={handleFilter}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onViewHistory={handleViewHistory}
        onExport={handleExport}
        onRefresh={loadPrices}
      />

      {/* Pricing Dialog */}
      <PricingDialog
        open={dialogOpen}
        price={selectedPrice}
        contracts={contracts}
        products={products}
        loading={dialogLoading}
        error={dialogError}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
      />

      {/* Price History Dialog */}
      <PriceHistory
        open={historyOpen}
        prices={historyPrices}
        productName={selectedPrice?.product_name}
        contractNumber={selectedPrice?.contract_number}
        loading={historyLoading}
        onClose={() => setHistoryOpen(false)}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa"
        message={`Bạn có chắc chắn muốn xóa đơn giá của sản phẩm "${selectedPrice?.product_name}" trong hợp đồng "${selectedPrice?.contract_number}"?`}
        onConfirm={handleDeleteConfirm}
        onCancel={() => setDeleteDialogOpen(false)}
      />

      {/* Quick Price Form */}
      <QuickPriceForm
        open={quickPriceOpen}
        onClose={() => setQuickPriceOpen(false)}
        onSubmit={handleQuickPriceSubmit}
        title="Cập nhật giá nhanh"
        subtitle="Thiết lập giá cho hợp đồng một cách nhanh chóng"
      />

      {/* Success Snackbar */}
      <Snackbar
        open={Boolean(successMessage)}
        autoHideDuration={6000}
        onClose={() => setSuccessMessage(null)}
        message={successMessage}
      />
    </Container>
  );
};

export default Pricing;
