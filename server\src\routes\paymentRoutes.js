const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');

/**
 * Payment Routes
 * Định nghĩa các route cho payments (Thanh toán)
 */

/**
 * @route   GET /api/payments
 * @desc    Lấy danh sách payments với filtering và pagination
 * @access  Private
 * @query   {number} page - Trang hiện tại (default: 1)
 * @query   {number} limit - Số lượng bản ghi trên mỗi trang (default: 10)
 * @query   {string} search - Từ khóa tìm kiếm
 * @query   {number} customerId - Lọ<PERSON> theo khách hàng
 * @query   {string} status - Lọc theo trạng thái (pending, confirmed, cancelled)
 * @query   {string} paymentMethod - Lọc theo phương thức thanh toán
 * @query   {string} sortBy - Cột để sắp xếp
 * @query   {string} sortOrder - Thứ tự sắp xếp (ASC/DESC)
 */
router.get('/', paymentController.getPayments);

/**
 * @route   GET /api/payments/:id
 * @desc    Lấy payment theo ID với thông tin allocation
 * @access  Private
 * @param   {number} id - ID của payment
 */
router.get('/:id', 
  paymentController.validateIdParam,
  paymentController.getPaymentById
);

/**
 * @route   POST /api/payments
 * @desc    Tạo payment mới với FIFO allocation
 * @access  Private
 * @body    {Object} paymentData - Dữ liệu payment
 * @body    {number} paymentData.customer_id - ID khách hàng
 * @body    {string} paymentData.payment_date - Ngày thanh toán
 * @body    {number} paymentData.amount - Số tiền thanh toán
 * @body    {string} paymentData.payment_method - Phương thức thanh toán
 * @body    {string} paymentData.reference_number - Số tham chiếu
 * @body    {string} paymentData.bank_account - Tài khoản ngân hàng
 * @body    {string} paymentData.description - Mô tả
 * @body    {string} paymentData.status - Trạng thái (default: confirmed)
 */
router.post('/', 
  paymentController.validatePaymentData,
  paymentController.createPayment
);

/**
 * @route   PUT /api/payments/:id
 * @desc    Cập nhật payment và re-allocate nếu cần
 * @access  Private
 * @param   {number} id - ID của payment
 * @body    {Object} paymentData - Dữ liệu cập nhật
 */
router.put('/:id', 
  paymentController.validateIdParam,
  paymentController.validatePaymentData,
  paymentController.updatePayment
);

/**
 * @route   DELETE /api/payments/:id
 * @desc    Xóa payment và các allocation liên quan
 * @access  Private
 * @param   {number} id - ID của payment
 */
router.delete('/:id', 
  paymentController.validateIdParam,
  paymentController.deletePayment
);

/**
 * @route   POST /api/payments/:id/confirm
 * @desc    Xác nhận payment và thực hiện FIFO allocation
 * @access  Private
 * @param   {number} id - ID của payment
 */
router.post('/:id/confirm', 
  paymentController.validateIdParam,
  paymentController.confirmPayment
);

/**
 * @route   POST /api/payments/allocation-preview
 * @desc    Lấy preview FIFO allocation trước khi tạo payment
 * @access  Private
 * @body    {Object} previewData - Dữ liệu preview
 * @body    {number} previewData.customerId - ID khách hàng
 * @body    {number} previewData.amount - Số tiền thanh toán
 * @body    {string} previewData.paymentDate - Ngày thanh toán
 */
router.post('/allocation-preview', 
  paymentController.validateAllocationPreviewData,
  paymentController.getPaymentAllocationPreview
);

module.exports = router;
