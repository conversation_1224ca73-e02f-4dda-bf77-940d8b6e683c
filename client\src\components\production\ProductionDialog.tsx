/**
 * ProductionDialog Component
 * Dialog thêm/sửa sản lượng đơn lẻ
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
  Box,
  InputAdornment
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { vi } from 'date-fns/locale';
import { MonetizationOn as MoneyIcon } from '@mui/icons-material';
import { convertDateToISOString, convertISOStringToDate } from '../../utils/vietnameseFormatters';

// Types
import { DailyProduction, DailyProductionFormData } from '../../types/dailyProduction';
import { Contract } from '../../types/contract';
import { Product } from '../../types/product';
import { Customer } from '../../types/customer';

// Services
import { contractService } from '../../services/contractService';
import { productService } from '../../services/productService';
import { customerService } from '../../services/customerService';
import { contractPriceService } from '../../services/contractPriceService';

interface ProductionDialogProps {
  open: boolean;
  production?: DailyProduction | null;
  onClose: () => void;
  onSubmit: (data: DailyProductionFormData) => Promise<void>;
  loading?: boolean;
  error?: string | null;
}

// Utility function - DEPRECATED: Use convertDateToISOString instead
const formatDateForStorage = (date: Date): string => {
  return convertDateToISOString(date);
};

const ProductionDialog: React.FC<ProductionDialogProps> = ({
  open,
  production,
  onClose,
  onSubmit,
  loading = false,
  error = null
}) => {
  const [formData, setFormData] = useState<DailyProductionFormData>({
    contract_id: 0,
    product_id: 0,
    production_date: formatDateForStorage(new Date()),
    quantity: 0,
    unit_price: 0,
    total_amount: 0,
    notes: '',
    status: 'draft'
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);
  const [currentPrice, setCurrentPrice] = useState<number>(0);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load customers
        const customersResponse = await customerService.getAll({ limit: 1000 });
        if (customersResponse.success) {
          setCustomers(customersResponse.data);
        }

        // Load products
        const productsResponse = await productService.getAll({ limit: 1000 });
        if (productsResponse.success) {
          setProducts(productsResponse.data);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    if (open) {
      loadData();
    }
  }, [open]);

  // Initialize form data
  useEffect(() => {
    if (open) {
      if (production) {
        // Edit mode
        setFormData({
          contract_id: production.contract_id || 0,
          product_id: production.product_id,
          production_date: production.production_date,
          quantity: production.quantity,
          unit_price: production.unit_price,
          total_amount: production.total_amount,
          notes: production.notes || '',
          status: production.status
        });
        
        if (production.customer_id) {
          setSelectedCustomerId(production.customer_id);
          loadContracts(production.customer_id);
        }
      } else {
        // Add mode
        setFormData({
          contract_id: 0,
          product_id: 0,
          production_date: formatDateForStorage(new Date()),
          quantity: 0,
          unit_price: 0,
          total_amount: 0,
          notes: '',
          status: 'draft'
        });
        setSelectedCustomerId(0);
      }
      setFormErrors({});
    }
  }, [open, production]);

  // Load contracts for customer
  const loadContracts = async (customerId: number) => {
    try {
      const response = await contractService.getAll({ 
        customer_id: customerId,
        limit: 1000 
      });
      if (response.success) {
        setContracts(response.data);
      }
    } catch (error) {
      console.error('Error loading contracts:', error);
    }
  };

  // Handle customer change
  const handleCustomerChange = async (customerId: number) => {
    setSelectedCustomerId(customerId);
    setFormData(prev => ({ ...prev, contract_id: 0 }));
    
    if (customerId) {
      await loadContracts(customerId);
    } else {
      setContracts([]);
    }
  };

  // Load current price for product and contract
  const loadCurrentPrice = async (contractId: number, productId: number) => {
    if (!contractId || !productId) return;

    try {
      const response = await contractPriceService.getCurrentPrice(
        contractId,
        productId,
        formData.production_date
      );
      
      if (response.success && response.data.price) {
        const price = response.data.price;
        setCurrentPrice(price);
        setFormData(prev => ({
          ...prev,
          unit_price: price,
          total_amount: prev.quantity * price
        }));
      }
    } catch (error) {
      console.error('Error loading current price:', error);
    }
  };

  // Handle input change
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Auto-calculate total amount when quantity or unit_price changes
      if (field === 'quantity' || field === 'unit_price') {
        newData.total_amount = newData.quantity * newData.unit_price;
      }
      
      return newData;
    });

    // Clear error when field is edited
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // Load price when contract or product changes
    if (field === 'contract_id' || field === 'product_id') {
      const contractId = field === 'contract_id' ? value : formData.contract_id;
      const productId = field === 'product_id' ? value : formData.product_id;
      
      if (contractId && productId) {
        loadCurrentPrice(contractId, productId);
      }
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.contract_id) {
      errors.contract_id = 'Hợp đồng là bắt buộc';
    }

    if (!formData.product_id) {
      errors.product_id = 'Sản phẩm là bắt buộc';
    }

    if (!formData.production_date) {
      errors.production_date = 'Ngày sản xuất là bắt buộc';
    }

    if (formData.quantity <= 0) {
      errors.quantity = 'Số lượng phải lớn hơn 0';
    }

    if (formData.unit_price <= 0) {
      errors.unit_price = 'Đơn giá phải lớn hơn 0';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle submit
  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  const isEditMode = !!production;
  const title = isEditMode ? 'Chỉnh sửa sản lượng' : 'Thêm sản lượng mới';

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: 500 }
        }}
      >
        <DialogTitle>
          <Typography variant="h6">{title}</Typography>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 1 }}>
            {error && (
              <Typography color="error" sx={{ mb: 2 }}>
                {error}
              </Typography>
            )}

            <Grid container spacing={3}>
              {/* Customer Selection */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required size="small">
                  <InputLabel>Khách hàng</InputLabel>
                  <Select
                    value={selectedCustomerId || ''}
                    onChange={(e) => handleCustomerChange(e.target.value as number)}
                    label="Khách hàng"
                    disabled={isEditMode}
                  >
                    <MenuItem value={0} disabled>Chọn khách hàng</MenuItem>
                    {customers.map((customer) => (
                      <MenuItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* Contract Selection */}
              <Grid item xs={12} md={6}>
                <FormControl 
                  fullWidth 
                  required 
                  error={!!formErrors.contract_id} 
                  size="small"
                  disabled={!selectedCustomerId}
                >
                  <InputLabel>Hợp đồng</InputLabel>
                  <Select
                    value={formData.contract_id || ''}
                    onChange={(e) => handleInputChange('contract_id', e.target.value as number)}
                    label="Hợp đồng"
                    disabled={isEditMode}
                  >
                    <MenuItem value={0} disabled>Chọn hợp đồng</MenuItem>
                    {contracts.map((contract) => (
                      <MenuItem key={contract.id} value={contract.id}>
                        {contract.contract_number} - {contract.contract_name}
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.contract_id && <FormHelperText>{formErrors.contract_id}</FormHelperText>}
                </FormControl>
              </Grid>

              {/* Product Selection */}
              <Grid item xs={12} md={6}>
                <FormControl 
                  fullWidth 
                  required 
                  error={!!formErrors.product_id} 
                  size="small"
                >
                  <InputLabel>Sản phẩm</InputLabel>
                  <Select
                    value={formData.product_id || ''}
                    onChange={(e) => handleInputChange('product_id', e.target.value as number)}
                    label="Sản phẩm"
                  >
                    <MenuItem value={0} disabled>Chọn sản phẩm</MenuItem>
                    {products.map((product) => (
                      <MenuItem key={product.id} value={product.id}>
                        {product.name} ({product.code})
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.product_id && <FormHelperText>{formErrors.product_id}</FormHelperText>}
                </FormControl>
              </Grid>

              {/* Production Date */}
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Ngày sản xuất"
                  value={convertISOStringToDate(formData.production_date)}
                  onChange={(date) => {
                    handleInputChange('production_date', convertDateToISOString(date));
                  }}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      required: true,
                      size: "small",
                      error: !!formErrors.production_date,
                      helperText: formErrors.production_date,
                      placeholder: 'dd/mm/yyyy'
                    }
                  }}
                />
              </Grid>

              {/* Quantity */}
              <Grid item xs={12} md={6}>
                <TextField
                  label="Số lượng"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 0)}
                  fullWidth
                  required
                  error={!!formErrors.quantity}
                  helperText={formErrors.quantity}
                  size="small"
                  InputProps={{
                    inputProps: { min: 0, step: 0.01 }
                  }}
                />
              </Grid>

              {/* Unit Price */}
              <Grid item xs={12} md={6}>
                <TextField
                  label="Đơn giá"
                  type="number"
                  value={formData.unit_price}
                  onChange={(e) => handleInputChange('unit_price', parseFloat(e.target.value) || 0)}
                  fullWidth
                  required
                  error={!!formErrors.unit_price}
                  helperText={formErrors.unit_price || (currentPrice > 0 ? `Giá hiện tại: ${currentPrice.toLocaleString('vi-VN')} VND` : '')}
                  size="small"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <MoneyIcon fontSize="small" />
                      </InputAdornment>
                    ),
                    inputProps: { min: 0 }
                  }}
                />
              </Grid>

              {/* Total Amount (Read-only) */}
              <Grid item xs={12} md={6}>
                <TextField
                  label="Thành tiền"
                  value={formData.total_amount.toLocaleString('vi-VN')}
                  fullWidth
                  size="small"
                  InputProps={{
                    readOnly: true,
                    startAdornment: (
                      <InputAdornment position="start">
                        <MoneyIcon fontSize="small" />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              {/* Status */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Trạng thái</InputLabel>
                  <Select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    label="Trạng thái"
                  >
                    <MenuItem value="draft">Nháp</MenuItem>
                    <MenuItem value="confirmed">Đã xác nhận</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Notes */}
              <Grid item xs={12}>
                <TextField
                  label="Ghi chú"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  fullWidth
                  multiline
                  rows={3}
                  size="small"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Đang xử lý...' : (isEditMode ? 'Cập nhật' : 'Thêm mới')}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ProductionDialog;
