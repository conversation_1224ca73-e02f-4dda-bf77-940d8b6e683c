/**
 * Payment Types
 * Định nghĩa types cho payments (Thanh toán)
 */

export interface Payment {
  id: number;
  customer_id: number;
  payment_date: string;
  amount: number;
  payment_method: string;
  reference_number?: string;
  bank_account?: string;
  description?: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  created_at: string;
  updated_at: string;
  
  // Joined fields
  customer_name: string;
  customer_tax_code?: string;
  created_by_name?: string;

  // Allocation info (for detailed view only)
  allocations?: PaymentAllocation[];
}

export interface PaymentAllocation {
  id: number;
  payment_id: number;
  receivable_id: number;
  allocated_amount: number;
  allocation_order: number;
  created_at: string;
  
  // Receivable info
  invoice_number: string;
  transaction_date: string;
  due_date: string;
  original_amount: number;
  receivable_description: string;
}

export interface CreatePaymentData {
  customer_id: number;
  payment_date: string;
  amount: number;
  payment_method: string;
  reference_number?: string;
  bank_account?: string;
  description?: string;
  status?: 'pending' | 'confirmed' | 'cancelled';
}

export interface UpdatePaymentData extends CreatePaymentData {}

export interface PaymentFilters {
  page?: number;
  limit?: number;
  search?: string;
  customerId?: number;
  status?: string;
  paymentMethod?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaymentsResponse {
  payments: Payment[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface AllocationPreviewData {
  customerId: number;
  amount: number;
  paymentDate: string;
}

export interface AllocationPreviewItem {
  receivable_id: number;
  invoice_number: string;
  transaction_date: string;
  due_date: string;
  original_amount: number;
  remaining_balance: number;
  allocated_amount: number;
  remaining_after_allocation: number;
}

export interface AllocationPreviewResult {
  allocations: AllocationPreviewItem[];
  total_allocated: number;
  unallocated_amount: number;
}

export const PAYMENT_METHODS = [
  'Tiền mặt',
  'Chuyển khoản',
  'Thẻ tín dụng',
  'Séc',
  'Khác'
] as const;

export type PaymentMethod = typeof PAYMENT_METHODS[number];

export const PAYMENT_STATUSES = [
  { value: 'pending', label: 'Chờ xác nhận', color: 'warning' },
  { value: 'confirmed', label: 'Đã xác nhận', color: 'success' },
  { value: 'cancelled', label: 'Đã hủy', color: 'error' }
] as const;
