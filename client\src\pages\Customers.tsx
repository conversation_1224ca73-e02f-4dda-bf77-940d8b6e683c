import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';

import { Customer, CustomerParams, CustomerDialogMode } from '../types/customer';
import { customerService } from '../services/customerService';
import { formatDate, formatErrorMessage } from '../utils/responseUtils';
import PermissionCheck from '../components/permission/PermissionCheck';
import CustomerDialog from '../components/customer/CustomerDialog';
import ConfirmDialog from '../components/common/ConfirmDialog';

/**
 * Customers Page Component
 * Main page for customer management
 */
const Customers: React.FC = () => {
  // State
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Dialog state
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<CustomerDialogMode>('create');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);

  // Confirm dialog state
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Notification state
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  /**
   * Load customers
   */
  const loadCustomers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params: CustomerParams = {
        page: page + 1, // API uses 1-based pagination
        limit: rowsPerPage,
        search: searchTerm,
        sort: 'created_at',
        order: 'DESC',
      };

      const result = await customerService.getAllCustomers(params);
      setCustomers(result.customers);
      setTotalCount(result.pagination?.total || 0);
    } catch (err) {
      setError(formatErrorMessage(err));
      setCustomers([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load customers on component mount and when dependencies change
   */
  useEffect(() => {
    loadCustomers();
  }, [page, rowsPerPage, searchTerm]);

  /**
   * Handle search
   */
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0); // Reset to first page when searching
  };

  /**
   * Handle page change
   */
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  /**
   * Handle rows per page change
   */
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  /**
   * Show notification
   */
  const showNotification = (message: string, severity: 'success' | 'error' | 'info' | 'warning' = 'success') => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  /**
   * Handle view customer
   */
  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setDialogMode('view');
    setDialogOpen(true);
  };

  /**
   * Handle edit customer
   */
  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setDialogMode('edit');
    setDialogOpen(true);
  };

  /**
   * Handle delete customer
   */
  const handleDeleteCustomer = (customer: Customer) => {
    setCustomerToDelete(customer);
    setConfirmDialogOpen(true);
  };

  /**
   * Handle add customer
   */
  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setDialogMode('create');
    setDialogOpen(true);
  };

  /**
   * Handle dialog close
   */
  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedCustomer(null);
  };

  /**
   * Handle dialog success
   */
  const handleDialogSuccess = (customer: Customer, action: 'create' | 'edit') => {
    if (action === 'create') {
      showNotification('Thêm khách hàng thành công!');
      // Add new customer to the list
      setCustomers(prev => [customer, ...prev]);
      setTotalCount(prev => prev + 1);
    } else {
      showNotification('Cập nhật khách hàng thành công!');
      // Update customer in the list
      setCustomers(prev => prev.map(c => c.id === customer.id ? customer : c));
    }
    loadCustomers(); // Reload to ensure data consistency
  };

  /**
   * Handle confirm delete
   */
  const handleConfirmDelete = async () => {
    if (!customerToDelete) return;

    try {
      setDeleteLoading(true);
      await customerService.deleteCustomer(customerToDelete.id);

      // Remove customer from list
      setCustomers(prev => prev.filter(c => c.id !== customerToDelete.id));
      setTotalCount(prev => prev - 1);

      showNotification('Xóa khách hàng thành công!');
      setConfirmDialogOpen(false);
      setCustomerToDelete(null);
    } catch (err: any) {
      showNotification(
        err.response?.data?.message || err.message || 'Có lỗi xảy ra khi xóa khách hàng',
        'error'
      );
    } finally {
      setDeleteLoading(false);
    }
  };

  /**
   * Handle cancel delete
   */
  const handleCancelDelete = () => {
    setConfirmDialogOpen(false);
    setCustomerToDelete(null);
  };

  return (
    <Box>
      {/* Page Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" component="h1" sx={{ fontWeight: 600, fontSize: '1.3rem' }}>
          Quản lý Khách hàng
        </Typography>
        <PermissionCheck resourceType="customer" action="create">
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleAddCustomer}
            sx={{ minWidth: 140 }}
          >
            Thêm khách hàng
          </Button>
        </PermissionCheck>
      </Box>

      {/* Search and Filters */}
      <Paper elevation={2} sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            placeholder="Tìm kiếm khách hàng..."
            value={searchTerm}
            onChange={handleSearch}
            size="small"
            sx={{ minWidth: 300 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
          />
          <Typography variant="body2" color="text.secondary">
            Tìm thấy {totalCount} khách hàng
          </Typography>
        </Box>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Customer Table */}
      <Paper elevation={2}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>ID</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Mã số thuế</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Tên khách hàng</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Tên viết tắt</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Người liên hệ</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Số điện thoại</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Email</TableCell>
                <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Ngày tạo</TableCell>
                <TableCell align="center" sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Thao tác</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                    <CircularProgress size={40} />
                  </TableCell>
                </TableRow>
              ) : customers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                    <Typography variant="body2" color="text.secondary">
                      {searchTerm ? 'Không tìm thấy khách hàng nào' : 'Chưa có khách hàng nào'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                customers.map((customer) => (
                  <TableRow key={customer.id} hover>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.id}</TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>
                      {customer.tax_code ? (
                        <Chip
                          label={customer.tax_code}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.75rem', height: 24 }}
                        />
                      ) : (
                        '-'
                      )}
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.8rem' }}>
                        {customer.name}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.short_name || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.contact_person || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.phone || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.email || '-'}</TableCell>
                    <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{formatDate(customer.created_at)}</TableCell>
                    <TableCell align="center" sx={{ py: 1.5 }}>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewCustomer(customer)}
                          title="Xem chi tiết"
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                        <PermissionCheck resourceType="customer" action="edit">
                          <IconButton
                            size="small"
                            onClick={() => handleEditCustomer(customer)}
                            title="Chỉnh sửa"
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </PermissionCheck>
                        <PermissionCheck resourceType="customer" action="delete">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteCustomer(customer)}
                            title="Xóa"
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </PermissionCheck>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={totalCount}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Số hàng mỗi trang:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} của ${count !== -1 ? count : `hơn ${to}`}`
          }
        />
      </Paper>

      {/* Customer Dialog */}
      <CustomerDialog
        open={dialogOpen}
        mode={dialogMode}
        customer={selectedCustomer}
        onClose={handleDialogClose}
        onSuccess={handleDialogSuccess}
      />

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={confirmDialogOpen}
        title="Xác nhận xóa khách hàng"
        message={`Bạn có chắc chắn muốn xóa khách hàng "${customerToDelete?.name}"? Hành động này không thể hoàn tác.`}
        type="warning"
        confirmText="Xóa"
        cancelText="Hủy"
        confirmColor="error"
        loading={deleteLoading}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
      />

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={() => setNotification(prev => ({ ...prev, open: false }))}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setNotification(prev => ({ ...prev, open: false }))}
          severity={notification.severity}
          sx={{ width: '100%', fontSize: '0.85rem' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Customers;
