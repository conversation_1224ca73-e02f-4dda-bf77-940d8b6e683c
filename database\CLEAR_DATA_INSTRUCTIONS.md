# Hướng dẫn xóa dữ liệu để chuẩn bị Production

## Tổng quan
Bộ script này giúp bạn xóa dữ liệu test/demo một cách an toàn để chuẩn bị nhập dữ liệu thực tế vào production.

## Các file script

### 1. `backup_before_clear.sql`
- **<PERSON><PERSON><PERSON> đích**: Tạo backup toàn bộ dữ liệu hiện tại
- **Chức năng**: Sao chép tất cả dữ liệu vào schema `backup_data`
- **Quan trọng**: PHẢI chạy trước khi xóa dữ liệu

### 2. `clear_data_for_production.sql`
- **<PERSON><PERSON><PERSON> đích**: Xóa dữ liệu test/demo
- **Chức năng**: 
  - Xó<PERSON> tất cả dữ liệu trừ tài khoản admin
  - <PERSON><PERSON><PERSON> nguyên cấu trúc bảng và ràng buộc
  - Reset sequence về 1
  - Không ảnh hưởng đến chức năng phần mềm

### 3. `restore_backup.sql`
- **<PERSON><PERSON><PERSON> đích**: Khôi phục dữ liệu nếu cần
- **Chức năng**: Phục hồi dữ liệu từ backup

## Quy trình thực hiện

### Bước 1: Backup dữ liệu
```bash
$env:PGPASSWORD = "110591"
psql -U postgres -d tinhtam-hp -f database/backup_before_clear.sql
```

### Bước 2: Xóa dữ liệu
```bash
$env:PGPASSWORD = "110591"
psql -U postgres -d tinhtam-hp -f database/clear_data_for_production.sql
```

### Bước 3: Kiểm tra kết quả
```bash
$env:PGPASSWORD = "110591"
psql -U postgres -d tinhtam-hp -c "
SELECT 
    schemaname,
    tablename,
    n_tup_ins as total_records
FROM pg_stat_user_tables 
WHERE schemaname = 'public'
ORDER BY tablename;
"
```

## Dữ liệu được giữ lại

### ✅ Được giữ lại:
- **Cấu trúc bảng**: Tất cả bảng, cột, ràng buộc
- **Indexes**: Tất cả chỉ mục
- **Views**: Tất cả view
- **Functions/Procedures**: Tất cả hàm và thủ tục
- **Triggers**: Tất cả trigger
- **Tài khoản admin**: Để đăng nhập hệ thống

### ❌ Được xóa:
- **Khách hàng**: Tất cả dữ liệu khách hàng test
- **Sản phẩm**: Tất cả sản phẩm demo
- **Hợp đồng**: Tất cả hợp đồng test
- **Giá hợp đồng**: Tất cả bảng giá test
- **Sản lượng**: Tất cả dữ liệu sản lượng test
- **Công nợ**: Tất cả dữ liệu công nợ test
- **Thanh toán**: Tất cả dữ liệu thanh toán test

## Khôi phục nếu cần

Nếu cần khôi phục lại dữ liệu cũ:
```bash
$env:PGPASSWORD = "110591"
psql -U postgres -d tinhtam-hp -f database/restore_backup.sql
```

## Lưu ý quan trọng

### ⚠️ Cảnh báo
- **Chỉ chạy trên database development/staging trước**
- **PHẢI backup trước khi xóa**
- **Kiểm tra kỹ trước khi chạy trên production**

### 🔒 Bảo mật
- Tài khoản admin được giữ lại với password hiện tại
- Có thể đăng nhập ngay sau khi xóa dữ liệu
- Email: `<EMAIL>`
- Password: `123456` (nên đổi sau khi production)

### 📊 Sau khi xóa dữ liệu
1. **ID mới bắt đầu từ 1**: Tất cả bảng có ID tự động tăng
2. **Chức năng hoạt động bình thường**: Không ảnh hưởng đến code
3. **Sẵn sàng nhập dữ liệu thực**: Có thể bắt đầu nhập dữ liệu production

## Kiểm tra sau khi thực hiện

### Kiểm tra số lượng bản ghi:
```sql
SELECT 
    'users' as table_name, COUNT(*) as records FROM users
UNION ALL
SELECT 
    'customers' as table_name, COUNT(*) as records FROM customers
UNION ALL
SELECT 
    'products' as table_name, COUNT(*) as records FROM products
UNION ALL
SELECT 
    'contracts' as table_name, COUNT(*) as records FROM contracts;
```

### Kết quả mong đợi:
- `users`: 1-2 bản ghi (admin + test user)
- `customers`: 0 bản ghi
- `products`: 0 bản ghi  
- `contracts`: 0 bản ghi
- Các bảng khác: 0 bản ghi

## Hỗ trợ

Nếu gặp lỗi trong quá trình thực hiện:
1. Kiểm tra log lỗi trong terminal
2. Sử dụng script khôi phục nếu cần
3. Liên hệ để được hỗ trợ

---
**Lưu ý**: Script này được thiết kế đặc biệt cho dự án quản lý giặt là và đã được test kỹ lưỡng.
