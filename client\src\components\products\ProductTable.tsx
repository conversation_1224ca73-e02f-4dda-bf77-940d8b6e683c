/**
 * Product Table Component
 * <PERSON>ển thị danh sách sản phẩm với các tính năng tìm kiếm, sắp xếp, phân trang
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Toolbar,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { visuallyHidden } from '@mui/utils';
import { Product, ProductFilterOptions, PRODUCT_UNIT_TYPE_OPTIONS, PRODUCT_STATUS_OPTIONS } from '../../types/product';

interface ProductTableProps {
  products: Product[];
  loading?: boolean;
  totalCount?: number;
  page?: number;
  rowsPerPage?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
  searchQuery?: string;
  filters?: ProductFilterOptions;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onSort?: (orderBy: string, order: 'asc' | 'desc') => void;
  onSearch?: (query: string) => void;
  onFilter?: (filters: ProductFilterOptions) => void;
  onAdd?: () => void;
  onEdit?: (product: Product) => void;
  onDelete?: (product: Product) => void;
  onView?: (product: Product) => void;
  onRefresh?: () => void;
  onExport?: () => void;
}

interface HeadCell {
  id: keyof Product;
  label: string;
  numeric: boolean;
  sortable: boolean;
  width?: number;
}

const headCells: HeadCell[] = [
  { id: 'code', label: 'Mã sản phẩm', numeric: false, sortable: true, width: 120 },
  { id: 'name', label: 'Tên sản phẩm', numeric: false, sortable: true, width: 200 },
  { id: 'description', label: 'Mô tả', numeric: false, sortable: false, width: 150 },
  { id: 'unit_type', label: 'Đơn vị tính', numeric: false, sortable: true, width: 100 },
  { id: 'is_active', label: 'Trạng thái', numeric: false, sortable: true, width: 100 },
  { id: 'created_at', label: 'Ngày tạo', numeric: false, sortable: true, width: 120 },
];

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  loading = false,
  totalCount = 0,
  page = 0,
  rowsPerPage = 10,
  orderBy = 'created_at',
  order = 'desc',
  searchQuery = '',
  filters = {},
  onPageChange,
  onRowsPerPageChange,
  onSort,
  onSearch,
  onFilter,
  onAdd,
  onEdit,
  onDelete,
  onView,
  onRefresh,
  onExport,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearchQuery !== searchQuery) {
        onSearch?.(localSearchQuery);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localSearchQuery, searchQuery, onSearch]);

  const handleRequestSort = useCallback((property: keyof Product) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    onSort?.(property as string, newOrder);
  }, [orderBy, order, onSort]);

  const handleChangePage = useCallback((_: unknown, newPage: number) => {
    onPageChange?.(newPage);
  }, [onPageChange]);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange?.(parseInt(event.target.value, 10));
  }, [onRowsPerPageChange]);

  const handleMenuClick = useCallback((event: React.MouseEvent<HTMLElement>, product: Product) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduct(product);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedProduct(null);
  }, []);

  const handleFilterClick = useCallback((event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  }, []);

  const handleFilterClose = useCallback(() => {
    setFilterAnchorEl(null);
  }, []);

  const handleFilterChange = useCallback((newFilters: Partial<ProductFilterOptions>) => {
    onFilter?.({ ...filters, ...newFilters });
    handleFilterClose();
  }, [filters, onFilter]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getUnitTypeLabel = (unitType: string) => {
    const option = PRODUCT_UNIT_TYPE_OPTIONS.find(opt => opt.value === unitType);
    return option?.label || unitType;
  };

  const renderStatusChip = (isActive: boolean) => {
    const status = PRODUCT_STATUS_OPTIONS.find(opt => opt.value === isActive);
    return (
      <Chip
        label={status?.label || 'Không xác định'}
        color={status?.color || 'default'}
        size="small"
        variant="outlined"
      />
    );
  };

  const renderSkeletonRows = () => {
    return Array.from(new Array(rowsPerPage)).map((_, index) => (
      <TableRow key={index}>
        {headCells.map((headCell) => (
          <TableCell key={headCell.id}>
            <Skeleton variant="text" width="80%" />
          </TableCell>
        ))}
        <TableCell>
          <Skeleton variant="circular" width={24} height={24} />
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <Paper sx={{ width: '100%', mb: 2 }}>
      <Toolbar
        sx={{
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
          minHeight: { xs: 56, sm: 64 },
        }}
      >
        <Typography
          sx={{ flex: '1 1 100%' }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          Danh sách sản phẩm
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            size="small"
            placeholder="Tìm kiếm sản phẩm..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ width: 250 }}
          />

          <Tooltip title="Bộ lọc">
            <IconButton onClick={handleFilterClick} size="small">
              <FilterIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Làm mới">
            <IconButton onClick={onRefresh} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xuất Excel">
            <IconButton onClick={onExport} size="small">
              <ExportIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Thêm sản phẩm">
            <IconButton onClick={onAdd} size="small" color="primary">
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>

      <TableContainer>
        <Table
          sx={{ minWidth: 750 }}
          aria-labelledby="tableTitle"
          size="small"
        >
          <TableHead>
            <TableRow>
              {headCells.map((headCell) => (
                <TableCell
                  key={headCell.id}
                  align={headCell.numeric ? 'right' : 'left'}
                  padding="normal"
                  sortDirection={orderBy === headCell.id ? order : false}
                  sx={{ width: headCell.width }}
                >
                  {headCell.sortable ? (
                    <TableSortLabel
                      active={orderBy === headCell.id}
                      direction={orderBy === headCell.id ? order : 'asc'}
                      onClick={() => handleRequestSort(headCell.id)}
                    >
                      {headCell.label}
                      {orderBy === headCell.id ? (
                        <Box component="span" sx={visuallyHidden}>
                          {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      ) : null}
                    </TableSortLabel>
                  ) : (
                    headCell.label
                  )}
                </TableCell>
              ))}
              <TableCell align="center" sx={{ width: 80 }}>
                Thao tác
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              renderSkeletonRows()
            ) : (
              products.map((product) => (
                <TableRow
                  hover
                  key={product.id}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => onView?.(product)}
                >
                  <TableCell>{product.code}</TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      {product.name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary" noWrap>
                      {product.description || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>{getUnitTypeLabel(product.unit_type)}</TableCell>
                  <TableCell>{renderStatusChip(product.is_active)}</TableCell>
                  <TableCell>{formatDate(product.created_at)}</TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuClick(e, product);
                      }}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Số dòng mỗi trang:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}–${to} của ${count !== -1 ? count : `hơn ${to}`}`
        }
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { onView?.(selectedProduct!); handleMenuClose(); }}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xem chi tiết</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { onEdit?.(selectedProduct!); handleMenuClose(); }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Chỉnh sửa</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => { onDelete?.(selectedProduct!); handleMenuClose(); }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xóa</ListItemText>
        </MenuItem>
      </Menu>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => handleFilterChange({ is_active: undefined })}>
          <ListItemText>Tất cả trạng thái</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange({ is_active: true })}>
          <ListItemText>Đang hoạt động</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleFilterChange({ is_active: false })}>
          <ListItemText>Ngừng hoạt động</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ProductTable;
