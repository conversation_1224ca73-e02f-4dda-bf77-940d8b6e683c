const { pool } = require('../db');

/**
 * Contract Price Model
 * Xử lý các thao tác CRUD cho bảng contract_prices
 */

/**
 * L<PERSON>y tất cả đơn giá với filter
 * @param {Object} options - T<PERSON><PERSON> chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {number} options.contract_id - Filter theo hợp đồng
 * @param {number} options.product_id - Filter theo sản phẩm
 * @param {boolean} options.is_active - Filter theo trạng thái
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllContractPrices = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    contract_id = '',
    product_id = '',
    is_active = true,
    sortBy = 'created_at',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'price', 'effective_date', 'expiry_date', 'is_active', 'created_at', 'updated_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    // Filter theo contract_id
    if (contract_id && !isNaN(parseInt(contract_id))) {
      whereClause += ` AND cp.contract_id = $${paramIndex}`;
      queryParams.push(parseInt(contract_id));
      paramIndex++;
    }

    // Filter theo product_id
    if (product_id && !isNaN(parseInt(product_id))) {
      whereClause += ` AND cp.product_id = $${paramIndex}`;
      queryParams.push(parseInt(product_id));
      paramIndex++;
    }

    // Filter theo is_active
    if (is_active !== undefined) {
      whereClause += ` AND cp.is_active = $${paramIndex}`;
      queryParams.push(is_active);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM contract_prices cp
      LEFT JOIN contracts c ON cp.contract_id = c.id
      LEFT JOIN products p ON cp.product_id = p.id
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT
        cp.id,
        cp.contract_id,
        cp.product_id,
        cp.price,
        cp.effective_date,
        cp.expiry_date,
        cp.is_active,
        cp.notes,
        cp.created_at,
        cp.updated_at,
        c.contract_number,
        c.contract_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type,
        u.name as created_by_name
      FROM contract_prices cp
      LEFT JOIN contracts c ON cp.contract_id = c.id
      LEFT JOIN products p ON cp.product_id = p.id
      LEFT JOIN users u ON cp.created_by = u.id
      ${whereClause}
      ORDER BY cp.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      prices: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllContractPrices:', error);
    throw error;
  }
};

/**
 * Lấy đơn giá theo ID
 * @param {number} id - ID của đơn giá
 * @returns {Object|null} Thông tin đơn giá hoặc null nếu không tìm thấy
 */
const getContractPriceById = async (id) => {
  try {
    const query = `
      SELECT
        cp.id,
        cp.contract_id,
        cp.product_id,
        cp.price,
        cp.effective_date,
        cp.expiry_date,
        cp.is_active,
        cp.notes,
        cp.created_at,
        cp.updated_at,
        c.contract_number,
        c.contract_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type,
        u.name as created_by_name
      FROM contract_prices cp
      LEFT JOIN contracts c ON cp.contract_id = c.id
      LEFT JOIN products p ON cp.product_id = p.id
      LEFT JOIN users u ON cp.created_by = u.id
      WHERE cp.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getContractPriceById:', error);
    throw error;
  }
};

/**
 * Tạo đơn giá mới
 * @param {Object} priceData - Dữ liệu đơn giá
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin đơn giá vừa tạo
 */
const createContractPrice = async (priceData, createdBy) => {
  const {
    contract_id,
    product_id,
    price,
    effective_date,
    expiry_date,
    notes
  } = priceData;

  try {
    const query = `
      INSERT INTO contract_prices (
        contract_id, product_id, price, effective_date, expiry_date, notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const values = [
      contract_id,
      product_id,
      price,
      effective_date,
      expiry_date || null,
      notes || null,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createContractPrice:', error);
    throw error;
  }
};

/**
 * Cập nhật đơn giá
 * @param {number} id - ID của đơn giá
 * @param {Object} priceData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin đơn giá sau khi cập nhật
 */
const updateContractPrice = async (id, priceData) => {
  const {
    price,
    effective_date,
    expiry_date,
    is_active,
    notes
  } = priceData;

  try {
    const query = `
      UPDATE contract_prices
      SET
        price = $1,
        effective_date = $2,
        expiry_date = $3,
        is_active = $4,
        notes = $5,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $6
      RETURNING *
    `;

    const values = [
      price,
      effective_date,
      expiry_date || null,
      is_active !== undefined ? is_active : true,
      notes || null,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateContractPrice:', error);
    throw error;
  }
};

/**
 * Xóa đơn giá (hard delete)
 * @param {number} id - ID của đơn giá
 * @returns {boolean} True nếu xóa thành công
 */
const deleteContractPrice = async (id) => {
  try {
    const query = `
      DELETE FROM contract_prices
      WHERE id = $1
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteContractPrice:', error);
    throw error;
  }
};

/**
 * Lấy đơn giá hiện tại cho sản phẩm trong hợp đồng
 * @param {number} contractId - ID hợp đồng
 * @param {number} productId - ID sản phẩm
 * @param {string} date - Ngày cần check giá (YYYY-MM-DD)
 * @returns {Object|null} Đơn giá hiện tại hoặc null
 */
const getCurrentPrice = async (contractId, productId, date = null) => {
  try {
    const checkDate = date || new Date().toISOString().split('T')[0];

    const query = `
      SELECT
        cp.id,
        cp.contract_id,
        cp.product_id,
        cp.price,
        cp.effective_date,
        cp.expiry_date,
        cp.is_active,
        cp.notes
      FROM contract_prices cp
      WHERE cp.contract_id = $1
        AND cp.product_id = $2
        AND cp.is_active = true
        AND cp.effective_date <= $3
        AND (cp.expiry_date IS NULL OR cp.expiry_date >= $3)
      ORDER BY cp.effective_date DESC
      LIMIT 1
    `;

    const result = await pool.query(query, [contractId, productId, checkDate]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getCurrentPrice:', error);
    throw error;
  }
};

/**
 * Kiểm tra xung đột đơn giá
 * @param {number} contractId - ID hợp đồng
 * @param {number} productId - ID sản phẩm
 * @param {string} effectiveDate - Ngày hiệu lực
 * @param {string} expiryDate - Ngày hết hạn (có thể null)
 * @param {number} excludeId - ID đơn giá cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu có xung đột
 */
const checkPriceConflict = async (contractId, productId, effectiveDate, expiryDate = null, excludeId = null) => {
  try {
    let query = `
      SELECT id FROM contract_prices
      WHERE contract_id = $1
        AND product_id = $2
        AND is_active = true
        AND (
          (effective_date <= $3 AND (expiry_date IS NULL OR expiry_date >= $3))
    `;

    const params = [contractId, productId, effectiveDate];
    let paramIndex = 4;

    if (expiryDate) {
      query += ` OR (effective_date <= $${paramIndex} AND (expiry_date IS NULL OR expiry_date >= $${paramIndex}))`;
      params.push(expiryDate);
      paramIndex++;
    }

    query += ')';

    if (excludeId) {
      query += ` AND id != $${paramIndex}`;
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkPriceConflict:', error);
    throw error;
  }
};

/**
 * Lấy lịch sử giá của sản phẩm trong hợp đồng
 * @param {number} contractId - ID hợp đồng
 * @param {number} productId - ID sản phẩm
 * @returns {Array} Danh sách lịch sử giá
 */
const getPriceHistory = async (contractId, productId) => {
  try {
    const query = `
      SELECT
        cp.id,
        cp.price,
        cp.effective_date,
        cp.expiry_date,
        cp.is_active,
        cp.notes,
        cp.created_at,
        u.name as created_by_name
      FROM contract_prices cp
      LEFT JOIN users u ON cp.created_by = u.id
      WHERE cp.contract_id = $1 AND cp.product_id = $2
      ORDER BY cp.effective_date DESC, cp.created_at DESC
    `;

    const result = await pool.query(query, [contractId, productId]);
    return result.rows;
  } catch (error) {
    console.error('Error in getPriceHistory:', error);
    throw error;
  }
};

/**
 * Vô hiệu hóa đơn giá cũ khi tạo đơn giá mới
 * @param {number} contractId - ID hợp đồng
 * @param {number} productId - ID sản phẩm
 * @param {string} effectiveDate - Ngày hiệu lực của giá mới
 * @returns {boolean} True nếu thành công
 */
const deactivateOldPrices = async (contractId, productId, effectiveDate) => {
  try {
    const query = `
      UPDATE contract_prices
      SET
        is_active = false,
        expiry_date = $3,
        updated_at = CURRENT_TIMESTAMP
      WHERE contract_id = $1
        AND product_id = $2
        AND is_active = true
        AND effective_date < $3
        AND (expiry_date IS NULL OR expiry_date >= $3)
    `;

    await pool.query(query, [contractId, productId, effectiveDate]);
    return true;
  } catch (error) {
    console.error('Error in deactivateOldPrices:', error);
    throw error;
  }
};

module.exports = {
  getAllContractPrices,
  getContractPriceById,
  createContractPrice,
  updateContractPrice,
  deleteContractPrice,
  getCurrentPrice,
  checkPriceConflict,
  getPriceHistory,
  deactivateOldPrices
};
