const dailyProductionModel = require('../models/dailyProductionModel');
const contractPriceModel = require('../models/contractPriceModel');
const contractModel = require('../models/contractModel');
const productModel = require('../models/productModel');
const {
  createResponse,
  createListResponse,
  processQueryParams,
  validateRequiredFields,
  sanitizeString
} = require('../utils/responseUtils');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * <PERSON><PERSON>y tất cả sản lượng
 * @route GET /api/v1/daily-production
 * @access Private
 */
const getAllDailyProduction = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);

  const options = {
    page: pagination.page,
    limit: pagination.limit,
    production_date: filters.production_date,
    contract_id: filters.contract_id,
    product_id: filters.product_id,
    date_from: filters.date_from,
    date_to: filters.date_to,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await dailyProductionModel.getAllDailyProduction(options);

  const response = createListResponse(
    result.productions,
    result.pagination,
    req
  );

  // Thêm summary vào response
  response.summary = result.summary;

  res.status(200).json(response);
});

/**
 * Lấy sản lượng theo ID
 * @route GET /api/v1/daily-production/:id
 * @access Private
 */
const getDailyProductionById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản lượng không hợp lệ', ['ID phải là một số nguyên']);
  }

  const production = await dailyProductionModel.getDailyProductionById(parseInt(id));

  if (!production) {
    throw new NotFoundError('Không tìm thấy sản lượng', [`Không tìm thấy sản lượng với ID ${id}`]);
  }

  res.status(200).json(createResponse(production));
});

/**
 * Tạo sản lượng mới
 * @route POST /api/v1/daily-production
 * @access Private
 */
const createDailyProduction = asyncHandler(async (req, res) => {
  const {
    production_date,
    contract_id,
    product_id,
    quantity,
    unit_price,
    notes,
    auto_get_price = true
  } = req.body;

  // Validate required fields
  const requiredFields = ['production_date', 'contract_id', 'product_id', 'quantity'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_id
  if (contract_id && isNaN(parseInt(contract_id))) {
    validationErrors.push({
      field: 'contract_id',
      message: 'ID hợp đồng phải là một số nguyên'
    });
  }

  // Validate product_id
  if (product_id && isNaN(parseInt(product_id))) {
    validationErrors.push({
      field: 'product_id',
      message: 'ID sản phẩm phải là một số nguyên'
    });
  }

  // Validate quantity
  if (quantity && (isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0)) {
    validationErrors.push({
      field: 'quantity',
      message: 'Số lượng phải là số dương'
    });
  }

  // Validate production_date
  if (production_date && isNaN(Date.parse(production_date))) {
    validationErrors.push({
      field: 'production_date',
      message: 'Ngày sản xuất không hợp lệ'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra sản lượng đã tồn tại
  const exists = await dailyProductionModel.checkProductionExists(
    production_date,
    parseInt(contract_id),
    parseInt(product_id)
  );

  if (exists) {
    throw new ConflictError(
      'Sản lượng đã tồn tại',
      ['Đã có sản lượng cho sản phẩm này trong ngày và hợp đồng này']
    );
  }

  // Tự động lấy đơn giá nếu không được cung cấp
  let finalUnitPrice = unit_price;
  if (auto_get_price && !unit_price) {
    const currentPrice = await contractPriceModel.getCurrentPrice(
      parseInt(contract_id),
      parseInt(product_id),
      production_date
    );

    if (!currentPrice) {
      throw new NotFoundError(
        'Không tìm thấy đơn giá',
        ['Không có đơn giá hiệu lực cho sản phẩm này trong hợp đồng']
      );
    }

    finalUnitPrice = currentPrice.price;
  }

  // Validate unit_price
  if (!finalUnitPrice || isNaN(parseFloat(finalUnitPrice)) || parseFloat(finalUnitPrice) <= 0) {
    throw new ValidationError(
      'Đơn giá không hợp lệ',
      ['Đơn giá phải là số dương hoặc được tự động lấy từ hợp đồng']
    );
  }

  // Sanitize input data
  const productionData = {
    production_date: production_date,
    contract_id: parseInt(contract_id),
    product_id: parseInt(product_id),
    quantity: parseFloat(quantity),
    unit_price: parseFloat(finalUnitPrice),
    notes: sanitizeString(notes)
  };

  // NOTE: Tạm thời sử dụng user ID giả vì authentication đã bị tắt
  const userId = req.user?.id || 1; // Default user ID = 1
  const newProduction = await dailyProductionModel.createDailyProduction(productionData, userId);

  res.status(201).json(createResponse(newProduction, 'Tạo sản lượng thành công'));
});

/**
 * Cập nhật sản lượng
 * @route PUT /api/v1/daily-production/:id
 * @access Private (Manager+)
 */
const updateDailyProduction = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    production_date,
    contract_id,
    product_id,
    quantity,
    unit_price,
    notes
  } = req.body;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản lượng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Validate required fields
  const requiredFields = ['production_date', 'contract_id', 'product_id', 'quantity', 'unit_price'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_id
  if (contract_id && isNaN(parseInt(contract_id))) {
    validationErrors.push({
      field: 'contract_id',
      message: 'ID hợp đồng phải là một số nguyên'
    });
  }

  // Validate product_id
  if (product_id && isNaN(parseInt(product_id))) {
    validationErrors.push({
      field: 'product_id',
      message: 'ID sản phẩm phải là một số nguyên'
    });
  }

  // Validate quantity
  if (quantity && (isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0)) {
    validationErrors.push({
      field: 'quantity',
      message: 'Số lượng phải là số dương'
    });
  }

  // Validate unit_price
  if (unit_price && (isNaN(parseFloat(unit_price)) || parseFloat(unit_price) <= 0)) {
    validationErrors.push({
      field: 'unit_price',
      message: 'Đơn giá phải là số dương'
    });
  }

  // Validate production_date
  if (production_date && isNaN(Date.parse(production_date))) {
    validationErrors.push({
      field: 'production_date',
      message: 'Ngày sản xuất không hợp lệ'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra sản lượng tồn tại
  const existingProduction = await dailyProductionModel.getDailyProductionById(parseInt(id));
  if (!existingProduction) {
    throw new NotFoundError('Không tìm thấy sản lượng', [`Không tìm thấy sản lượng với ID ${id}`]);
  }

  // Kiểm tra xung đột với sản lượng khác
  const hasConflict = await dailyProductionModel.checkProductionExists(
    production_date,
    parseInt(contract_id),
    parseInt(product_id),
    parseInt(id)
  );

  if (hasConflict) {
    throw new ConflictError(
      'Xung đột sản lượng',
      ['Đã có sản lượng khác cho sản phẩm này trong ngày và hợp đồng này']
    );
  }

  // Sanitize input data
  const productionData = {
    production_date: production_date,
    contract_id: parseInt(contract_id),
    product_id: parseInt(product_id),
    quantity: parseFloat(quantity),
    unit_price: parseFloat(unit_price),
    notes: sanitizeString(notes)
  };

  const updatedProduction = await dailyProductionModel.updateDailyProduction(parseInt(id), productionData);

  if (!updatedProduction) {
    throw new NotFoundError('Không thể cập nhật sản lượng', ['Sản lượng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(updatedProduction, 'Cập nhật sản lượng thành công'));
});

/**
 * Xóa sản lượng
 * @route DELETE /api/v1/daily-production/:id
 * @access Private (Admin only)
 */
const deleteDailyProduction = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản lượng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Kiểm tra sản lượng tồn tại
  const existingProduction = await dailyProductionModel.getDailyProductionById(parseInt(id));
  if (!existingProduction) {
    throw new NotFoundError('Không tìm thấy sản lượng', [`Không tìm thấy sản lượng với ID ${id}`]);
  }

  const deleted = await dailyProductionModel.deleteDailyProduction(parseInt(id));

  if (!deleted) {
    throw new NotFoundError('Không thể xóa sản lượng', ['Sản lượng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(
    { message: 'Xóa sản lượng thành công', id: parseInt(id) }
  ));
});

/**
 * Tạo sản lượng hàng loạt
 * @route POST /api/v1/daily-production/bulk
 * @access Private
 */
const createBulkProduction = asyncHandler(async (req, res) => {
  const {
    production_date,
    contract_id,
    items,
    auto_get_price = true
  } = req.body;

  // Validate required fields
  const requiredFields = ['production_date', 'contract_id', 'items'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_id
  if (contract_id && isNaN(parseInt(contract_id))) {
    validationErrors.push({
      field: 'contract_id',
      message: 'ID hợp đồng phải là một số nguyên'
    });
  }

  // Validate production_date
  if (production_date && isNaN(Date.parse(production_date))) {
    validationErrors.push({
      field: 'production_date',
      message: 'Ngày sản xuất không hợp lệ'
    });
  }

  // Validate items
  if (!Array.isArray(items) || items.length === 0) {
    validationErrors.push({
      field: 'items',
      message: 'Danh sách sản phẩm phải là mảng và không được rỗng'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Validate từng item và tự động lấy giá nếu cần
  const processedItems = [];
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    const { product_id, quantity, unit_price, notes } = item;

    // Validate item fields
    if (!product_id || isNaN(parseInt(product_id))) {
      throw new ValidationError(`Item ${i + 1}: ID sản phẩm không hợp lệ`);
    }

    if (!quantity || isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
      throw new ValidationError(`Item ${i + 1}: Số lượng phải là số dương`);
    }

    // Tự động lấy đơn giá nếu cần
    let finalUnitPrice = unit_price;
    if (auto_get_price && !unit_price) {
      const currentPrice = await contractPriceModel.getCurrentPrice(
        parseInt(contract_id),
        parseInt(product_id),
        production_date
      );

      if (!currentPrice) {
        throw new NotFoundError(
          `Item ${i + 1}: Không tìm thấy đơn giá`,
          [`Không có đơn giá hiệu lực cho sản phẩm ID ${product_id} trong hợp đồng`]
        );
      }

      finalUnitPrice = currentPrice.price;
    }

    if (!finalUnitPrice || isNaN(parseFloat(finalUnitPrice)) || parseFloat(finalUnitPrice) <= 0) {
      throw new ValidationError(`Item ${i + 1}: Đơn giá không hợp lệ`);
    }

    processedItems.push({
      product_id: parseInt(product_id),
      quantity: parseFloat(quantity),
      unit_price: parseFloat(finalUnitPrice),
      notes: sanitizeString(notes)
    });
  }

  // NOTE: Tạm thời sử dụng user ID giả vì authentication đã bị tắt
  const userId = req.user?.id || 1; // Default user ID = 1
  const result = await dailyProductionModel.createBulkProduction(
    production_date,
    parseInt(contract_id),
    processedItems,
    userId
  );

  res.status(201).json(createResponse(result, `Đã nhập thành công ${result.created_count} sản phẩm`));
});

/**
 * Lấy báo cáo sản lượng
 * @route GET /api/v1/daily-production/report
 * @access Private
 */
const getProductionReport = asyncHandler(async (req, res) => {
  const { date_from, date_to, contract_id } = req.query;

  // Validate required parameters
  if (!date_from || isNaN(Date.parse(date_from))) {
    throw new ValidationError('Ngày bắt đầu không hợp lệ', ['date_from phải có định dạng YYYY-MM-DD']);
  }

  if (!date_to || isNaN(Date.parse(date_to))) {
    throw new ValidationError('Ngày kết thúc không hợp lệ', ['date_to phải có định dạng YYYY-MM-DD']);
  }

  if (new Date(date_to) < new Date(date_from)) {
    throw new ValidationError('Khoảng thời gian không hợp lệ', ['Ngày kết thúc phải sau ngày bắt đầu']);
  }

  // Validate contract_id if provided
  if (contract_id && isNaN(parseInt(contract_id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là một số nguyên']);
  }

  const report = await dailyProductionModel.getProductionReport(
    date_from,
    date_to,
    contract_id ? parseInt(contract_id) : null
  );

  res.status(200).json(createResponse(report));
});

/**
 * Lấy sản lượng theo ngày
 * @route GET /api/v1/daily-production/by-date
 * @access Private
 */
const getProductionByDate = asyncHandler(async (req, res) => {
  const { production_date, contract_id } = req.query;

  // Validate required parameters
  if (!production_date || isNaN(Date.parse(production_date))) {
    throw new ValidationError('Ngày sản xuất không hợp lệ', ['production_date phải có định dạng YYYY-MM-DD']);
  }

  // Validate contract_id if provided
  if (contract_id && isNaN(parseInt(contract_id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là một số nguyên']);
  }

  const productions = await dailyProductionModel.getProductionByDate(
    production_date,
    contract_id ? parseInt(contract_id) : null
  );

  res.status(200).json(createResponse(productions));
});

/**
 * Tìm kiếm sản lượng để tạo công nợ
 * @route GET /api/v1/daily-production/search
 * @access Private
 */
const searchForReceivable = asyncHandler(async (req, res) => {
  const { contractId, status, startDate, endDate } = req.query;

  // Validate required parameters
  if (!contractId || isNaN(parseInt(contractId))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['contractId phải là một số nguyên']);
  }

  // Validate dates if provided
  if (startDate && isNaN(Date.parse(startDate))) {
    throw new ValidationError('Ngày bắt đầu không hợp lệ', ['startDate phải có định dạng YYYY-MM-DD']);
  }

  if (endDate && isNaN(Date.parse(endDate))) {
    throw new ValidationError('Ngày kết thúc không hợp lệ', ['endDate phải có định dạng YYYY-MM-DD']);
  }

  if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
    throw new ValidationError('Khoảng thời gian không hợp lệ', ['Ngày kết thúc phải sau ngày bắt đầu']);
  }

  // Search productions for receivable creation
  const productions = await dailyProductionModel.searchForReceivable({
    contractId: parseInt(contractId),
    status,
    startDate,
    endDate
  });

  res.status(200).json(createResponse(productions));
});

/**
 * Bulk update status cho nhiều sản lượng
 * @route PUT /api/v1/daily-production/bulk-status
 * @access Private
 */
const bulkUpdateStatus = asyncHandler(async (req, res) => {
  const { production_ids, new_status } = req.body;

  // Validate required fields
  if (!production_ids || !Array.isArray(production_ids) || production_ids.length === 0) {
    throw new ValidationError('Danh sách ID sản lượng không hợp lệ', ['production_ids phải là mảng không rỗng']);
  }

  if (!new_status) {
    throw new ValidationError('Trạng thái mới không được để trống', ['new_status là bắt buộc']);
  }

  // Validate status value
  const validStatuses = ['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'];
  if (!validStatuses.includes(new_status)) {
    throw new ValidationError('Trạng thái không hợp lệ', [`Trạng thái phải là một trong: ${validStatuses.join(', ')}`]);
  }

  // Validate production IDs
  const invalidIds = production_ids.filter(id => !id || isNaN(parseInt(id)));
  if (invalidIds.length > 0) {
    throw new ValidationError('ID sản lượng không hợp lệ', [`Các ID không hợp lệ: ${invalidIds.join(', ')}`]);
  }

  const userId = req.user?.id || 1; // Temporary fallback
  const result = await dailyProductionModel.bulkUpdateStatus(
    production_ids.map(id => parseInt(id)),
    new_status,
    userId
  );

  res.status(200).json(createResponse(result, 'Cập nhật trạng thái thành công'));
});

/**
 * Lấy sản lượng theo tháng hoặc giai đoạn để xác nhận
 * @route GET /api/v1/daily-production/for-confirmation
 * @access Private
 */
const getProductionForConfirmation = asyncHandler(async (req, res) => {
  const { year, month, start_date, end_date, contract_id, status } = req.query;

  // Validate: phải có (year + month) hoặc (start_date + end_date)
  const hasMonthFilter = year && month;
  const hasDateRangeFilter = start_date && end_date;

  if (!hasMonthFilter && !hasDateRangeFilter) {
    throw new ValidationError('Thiếu thông tin bắt buộc', ['Phải có (year + month) hoặc (start_date + end_date)']);
  }

  if (hasMonthFilter && hasDateRangeFilter) {
    throw new ValidationError('Tham số xung đột', ['Không thể sử dụng cả (year + month) và (start_date + end_date) cùng lúc']);
  }

  let yearNum = null, monthNum = null;

  // Validate year and month nếu có
  if (hasMonthFilter) {
    yearNum = parseInt(year);
    monthNum = parseInt(month);

    if (isNaN(yearNum) || yearNum < 2020 || yearNum > 2030) {
      throw new ValidationError('Năm không hợp lệ', ['Năm phải từ 2020 đến 2030']);
    }

    if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
      throw new ValidationError('Tháng không hợp lệ', ['Tháng phải từ 1 đến 12']);
    }
  }

  // Validate date range nếu có
  if (hasDateRangeFilter) {
    if (!start_date || isNaN(Date.parse(start_date))) {
      throw new ValidationError('Ngày bắt đầu không hợp lệ', ['start_date phải có định dạng YYYY-MM-DD']);
    }

    if (!end_date || isNaN(Date.parse(end_date))) {
      throw new ValidationError('Ngày kết thúc không hợp lệ', ['end_date phải có định dạng YYYY-MM-DD']);
    }

    if (new Date(end_date) < new Date(start_date)) {
      throw new ValidationError('Khoảng thời gian không hợp lệ', ['Ngày kết thúc phải sau ngày bắt đầu']);
    }
  }

  // Validate contract_id if provided
  let contractId = null;
  if (contract_id) {
    contractId = parseInt(contract_id);
    if (isNaN(contractId)) {
      throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là số nguyên']);
    }
  }

  // Validate status if provided
  if (status) {
    const validStatuses = ['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'];
    if (!validStatuses.includes(status)) {
      throw new ValidationError('Trạng thái không hợp lệ', [`Trạng thái phải là một trong: ${validStatuses.join(', ')}`]);
    }
  }

  const productions = await dailyProductionModel.getProductionForConfirmation(
    yearNum,
    monthNum,
    contractId,
    status,
    start_date,
    end_date
  );

  res.status(200).json(createResponse(productions));
});

/**
 * Lấy thống kê sản lượng theo trạng thái
 * @route GET /api/v1/daily-production/status-stats
 * @access Private
 */
const getProductionStatusStats = asyncHandler(async (req, res) => {
  const { year, month, start_date, end_date, contract_id } = req.query;

  // Validate year if provided
  let yearNum = null;
  if (year) {
    yearNum = parseInt(year);
    if (isNaN(yearNum) || yearNum < 2020 || yearNum > 2030) {
      throw new ValidationError('Năm không hợp lệ', ['Năm phải từ 2020 đến 2030']);
    }
  }

  // Validate month if provided
  let monthNum = null;
  if (month) {
    monthNum = parseInt(month);
    if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
      throw new ValidationError('Tháng không hợp lệ', ['Tháng phải từ 1 đến 12']);
    }
  }

  // Validate contract_id if provided
  let contractId = null;
  if (contract_id) {
    contractId = parseInt(contract_id);
    if (isNaN(contractId)) {
      throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là số nguyên']);
    }
  }

  // Validate date range nếu có
  if (start_date && !isNaN(Date.parse(start_date)) && end_date && !isNaN(Date.parse(end_date))) {
    if (new Date(end_date) < new Date(start_date)) {
      throw new ValidationError('Khoảng thời gian không hợp lệ', ['Ngày kết thúc phải sau ngày bắt đầu']);
    }
  }

  const stats = await dailyProductionModel.getProductionStatusStats(yearNum, monthNum, contractId, start_date, end_date);

  res.status(200).json(createResponse(stats));
});

/**
 * Lấy sản lượng đã nhóm theo hợp đồng và ngày
 * @route GET /api/v1/daily-production/grouped
 * @access Private
 */
const getGroupedProduction = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);

  const options = {
    page: pagination.page,
    limit: pagination.limit,
    date_from: filters.date_from,
    date_to: filters.date_to,
    contract_id: filters.contract_id,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await dailyProductionModel.getGroupedProduction(options);

  const response = createListResponse(
    result.productions,
    result.pagination,
    req
  );

  res.status(200).json(response);
});

/**
 * Import sản lượng từ Excel
 * @route POST /api/v1/daily-production/import
 * @access Private
 */
const importProduction = asyncHandler(async (req, res) => {
  const { data } = req.body;

  // Validate required fields
  if (!Array.isArray(data) || data.length === 0) {
    throw new ValidationError('Dữ liệu import không hợp lệ', ['data phải là mảng và không được rỗng']);
  }

  const results = {
    total: data.length,
    success: 0,
    failed: 0,
    errors: []
  };

  // Process each row
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    try {
      const {
        contract_number,
        production_date,
        product_code,
        quantity,
        notes
      } = row;

      // Validate required fields for each row
      if (!contract_number || !production_date || !product_code || !quantity) {
        throw new Error('Thiếu thông tin bắt buộc');
      }

      // Find contract by number
      const contracts = await contractModel.getAllContracts({
        page: 1,
        limit: 1,
        search: contract_number.trim()
      });

      if (!contracts.contracts || contracts.contracts.length === 0) {
        throw new Error(`Không tìm thấy hợp đồng: ${contract_number}`);
      }

      const contract = contracts.contracts.find(c =>
        c.contract_number === contract_number.trim()
      );

      if (!contract) {
        throw new Error(`Không tìm thấy hợp đồng: ${contract_number}`);
      }

      // Find product by code
      const products = await productModel.getAllProducts({
        page: 1,
        limit: 1,
        search: product_code.trim()
      });

      if (!products.products || products.products.length === 0) {
        throw new Error(`Không tìm thấy sản phẩm: ${product_code}`);
      }

      const product = products.products.find(p =>
        p.code === product_code.trim()
      );

      if (!product) {
        throw new Error(`Không tìm thấy sản phẩm: ${product_code}`);
      }

      // Convert Vietnamese date format (dd/mm/yyyy) to ISO format
      const convertVNDateToISO = (vnDate) => {
        const parts = vnDate.split('/');
        if (parts.length !== 3) return null;

        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);

        if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
        if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900) return null;

        const date = new Date(year, month - 1, day);
        if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
          return null;
        }

        // Use local date components to avoid timezone conversion issues
        const yearStr = date.getFullYear();
        const monthStr = String(date.getMonth() + 1).padStart(2, '0');
        const dayStr = String(date.getDate()).padStart(2, '0');
        return `${yearStr}-${monthStr}-${dayStr}`;
      };

      const isoDate = convertVNDateToISO(production_date);
      if (!isoDate) {
        throw new Error('Ngày sản xuất không đúng định dạng (dd/mm/yyyy)');
      }

      // Validate quantity
      if (isNaN(parseFloat(quantity)) || parseFloat(quantity) <= 0) {
        throw new Error('Số lượng phải là số dương');
      }

      // Get unit price from contract prices
      const contractPrices = await contractPriceModel.getAllContractPrices({
        contract_id: contract.id,
        product_id: product.id,
        is_active: true,
        limit: 1
      });

      if (!contractPrices.prices || contractPrices.prices.length === 0) {
        throw new Error(`Chưa thiết lập giá cho sản phẩm ${product_code} trong hợp đồng ${contract_number}`);
      }

      const unitPrice = contractPrices.prices[0].price;

      // Check if production already exists
      const exists = await dailyProductionModel.checkProductionExists(
        isoDate,
        contract.id,
        product.id
      );

      if (exists) {
        throw new Error('Sản lượng đã tồn tại cho sản phẩm này trong ngày');
      }

      // Create production
      const productionData = {
        production_date: isoDate,
        contract_id: contract.id,
        product_id: product.id,
        quantity: parseFloat(quantity),
        unit_price: unitPrice,
        notes: sanitizeString(notes || '')
      };

      const userId = req.user?.id || 1;
      await dailyProductionModel.createDailyProduction(productionData, userId);

      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push({
        row: i + 1,
        data: row,
        error: error.message
      });
    }
  }

  res.status(200).json(createResponse(results,
    `Import hoàn tất: ${results.success} thành công, ${results.failed} thất bại`
  ));
});

/**
 * Lấy báo cáo sản lượng theo tháng cho một hợp đồng
 * @route GET /api/v1/daily-production/monthly-report
 * @access Private
 */
const getMonthlyProductionReport = asyncHandler(async (req, res) => {
  const { year, month, contract_id, status } = req.query;

  // Validate required parameters
  if (!year || !month || !contract_id) {
    throw new ValidationError('Thiếu thông tin bắt buộc', ['year, month, và contract_id là bắt buộc']);
  }

  // Validate year and month
  const yearNum = parseInt(year);
  const monthNum = parseInt(month);
  const contractIdNum = parseInt(contract_id);

  if (yearNum < 2020 || yearNum > 2030) {
    throw new ValidationError('Năm không hợp lệ', ['Năm phải từ 2020 đến 2030']);
  }

  if (monthNum < 1 || monthNum > 12) {
    throw new ValidationError('Tháng không hợp lệ', ['Tháng phải từ 1 đến 12']);
  }

  try {
    // Lấy báo cáo sản lượng theo tháng
    const reportData = await dailyProductionModel.getMonthlyReport(yearNum, monthNum, contractIdNum, status);

    res.json({
      success: true,
      data: {
        year: yearNum,
        month: monthNum,
        contract_id: contractIdNum,
        status: status || 'all',
        report: reportData
      },
      message: 'Lấy báo cáo sản lượng thành công'
    });
  } catch (error) {
    console.error('Error in getMonthlyProductionReport:', error);
    throw error;
  }
});

module.exports = {
  getAllDailyProduction,
  getDailyProductionById,
  createDailyProduction,
  updateDailyProduction,
  deleteDailyProduction,
  createBulkProduction,
  getProductionReport,
  getProductionByDate,
  searchForReceivable,
  bulkUpdateStatus,
  getProductionForConfirmation,
  getProductionStatusStats,
  getGroupedProduction,
  importProduction,
  getMonthlyProductionReport
};
