/* Laundry Management System - Fresh & Clean Theme */
/* Custom CSS for enhanced styling beyond MUI theme */

:root {
  /* Primary Colors - Sky Blue (Cleanliness & Freshness) */
  --primary-main: #0ea5e9;
  --primary-light: #38bdf8;
  --primary-dark: #0284c7;
  
  /* Secondary Colors - Green (Eco-friendly) */
  --secondary-main: #22c55e;
  --secondary-light: #4ade80;
  --secondary-dark: #16a34a;
  
  /* Accent Colors - <PERSON>an (Fresh & Information) */
  --accent-main: #06b6d4;
  --accent-light: #22d3ee;
  --accent-dark: #0891b2;
  
  /* Background Colors - Clean & Minimal */
  --bg-default: #f8fafc;
  --bg-paper: #ffffff;
  --bg-surface: #f1f5f9;
  
  /* Text Colors - Professional & Readable */
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-disabled: #94a3b8;
  
  /* Status Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #06b6d4;
  
  /* Shadows - Clean & Subtle */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
}

/* Custom gradient backgrounds for special elements */
.laundry-gradient-primary {
  background: linear-gradient(135deg, var(--primary-main) 0%, var(--primary-light) 100%);
}

.laundry-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-main) 0%, var(--secondary-light) 100%);
}

.laundry-gradient-accent {
  background: linear-gradient(135deg, var(--accent-main) 0%, var(--accent-light) 100%);
}

/* Custom hover effects */
.laundry-hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.laundry-hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Custom sidebar styling */
.laundry-sidebar {
  background: linear-gradient(180deg, var(--bg-paper) 0%, var(--bg-surface) 100%);
  border-right: 1px solid var(--text-disabled);
}

.laundry-sidebar-item {
  border-radius: var(--radius-lg);
  margin: 2px 8px;
  transition: all 0.2s ease-in-out;
}

.laundry-sidebar-item:hover {
  background-color: rgba(14, 165, 233, 0.08);
  transform: translateX(2px);
}

.laundry-sidebar-item.active {
  background-color: rgba(14, 165, 233, 0.12);
  border-left: 3px solid var(--primary-main);
}

/* Custom form styling */
.laundry-form-container {
  background: var(--bg-paper);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 24px;
}

.laundry-form-section {
  border-bottom: 1px solid var(--text-disabled);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.laundry-form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Custom button enhancements */
.laundry-btn-primary {
  background: var(--primary-main);
  color: white;
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.laundry-btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.laundry-btn-secondary {
  background: var(--secondary-main);
  color: white;
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
}

.laundry-btn-secondary:hover {
  background: var(--secondary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Custom status indicators */
.laundry-status-clean {
  background-color: var(--success);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.laundry-status-processing {
  background-color: var(--info);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.laundry-status-pending {
  background-color: var(--warning);
  color: white;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Custom table styling */
.laundry-table-header {
  background: linear-gradient(90deg, var(--bg-surface) 0%, var(--bg-paper) 100%);
  border-bottom: 2px solid var(--primary-main);
}

.laundry-table-row:hover {
  background-color: rgba(14, 165, 233, 0.04);
  transition: background-color 0.2s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .laundry-form-container {
    padding: 16px;
  }
  
  .laundry-sidebar-item {
    margin: 1px 4px;
  }
}

/* Accessibility enhancements */
.laundry-focus-visible:focus-visible {
  outline: 2px solid var(--primary-main);
  outline-offset: 2px;
}

/* Loading states */
.laundry-loading {
  background: linear-gradient(90deg, var(--bg-surface) 25%, var(--bg-paper) 50%, var(--bg-surface) 75%);
  background-size: 200% 100%;
  animation: laundry-shimmer 1.5s infinite;
}

@keyframes laundry-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
