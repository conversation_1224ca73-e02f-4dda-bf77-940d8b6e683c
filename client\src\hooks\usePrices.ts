/**
 * usePrices Hook
 * Hook quản lý pricing logic cho contracts
 */

import { useState, useEffect, useCallback } from 'react';
import { contractPriceService } from '../services/contractPriceService';
import { ContractPrice, ContractPriceFilterOptions, PriceHistoryItem } from '../types/contractPrice';

interface UsePricesOptions {
  contractId?: number;
  productId?: number;
  autoLoad?: boolean;
}

interface UsePricesReturn {
  // Data
  prices: ContractPrice[];
  currentPrice: number | null;
  priceHistory: PriceHistoryItem[];
  
  // State
  loading: boolean;
  error: string | null;
  
  // Actions
  loadPrices: (options?: ContractPriceFilterOptions) => Promise<void>;
  loadCurrentPrice: (contractId: number, productId: number, date?: string) => Promise<number | null>;
  loadPriceHistory: (contractId: number, productId: number) => Promise<void>;
  createPrice: (data: any) => Promise<boolean>;
  updatePrice: (id: number, data: any) => Promise<boolean>;
  deletePrice: (id: number) => Promise<boolean>;
  setActivePrice: (contractId: number, productId: number, priceId: number) => Promise<boolean>;
  
  // Utilities
  validatePrice: (price: number, contractId: number, productId: number) => Promise<string[]>;
  calculatePriceChange: (oldPrice: number, newPrice: number) => { amount: number; percentage: number };
  formatPrice: (price: number) => string;
}

export const usePrices = (options: UsePricesOptions = {}): UsePricesReturn => {
  const { contractId, productId, autoLoad = true } = options;

  // State
  const [prices, setPrices] = useState<ContractPrice[]>([]);
  const [currentPrice, setCurrentPrice] = useState<number | null>(null);
  const [priceHistory, setPriceHistory] = useState<PriceHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load prices
  const loadPrices = useCallback(async (filterOptions: ContractPriceFilterOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const options = {
        ...filterOptions,
        contract_id: contractId,
        product_id: productId,
      };

      const response = await contractPriceService.getAll(options);
      
      if (response.success) {
        setPrices(response.data);
      } else {
        throw new Error('Không thể tải danh sách giá');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error loading prices:', err);
    } finally {
      setLoading(false);
    }
  }, [contractId, productId]);

  // Load current price
  const loadCurrentPrice = useCallback(async (
    contractId: number, 
    productId: number, 
    date?: string
  ): Promise<number | null> => {
    try {
      const response = await contractPriceService.getCurrentPrice(contractId, productId, date);
      
      if (response.success && response.data.price) {
        const price = response.data.price;
        setCurrentPrice(price);
        return price;
      }
      
      setCurrentPrice(null);
      return null;
    } catch (err) {
      console.error('Error loading current price:', err);
      setCurrentPrice(null);
      return null;
    }
  }, []);

  // Load price history
  const loadPriceHistory = useCallback(async (contractId: number, productId: number) => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.getHistory(contractId, productId);
      
      if (response.success) {
        setPriceHistory(response.data);
      } else {
        throw new Error('Không thể tải lịch sử giá');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error loading price history:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create price
  const createPrice = useCallback(async (data: any): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.create(data);
      
      if (response.success) {
        // Reload prices after creation
        await loadPrices();
        return true;
      } else {
        throw new Error('Không thể tạo giá mới');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error creating price:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPrices]);

  // Update price
  const updatePrice = useCallback(async (id: number, data: any): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.update(id, data);
      
      if (response.success) {
        // Reload prices after update
        await loadPrices();
        return true;
      } else {
        throw new Error('Không thể cập nhật giá');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error updating price:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPrices]);

  // Delete price
  const deletePrice = useCallback(async (id: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.delete(id);
      
      if (response.success) {
        // Reload prices after deletion
        await loadPrices();
        return true;
      } else {
        throw new Error('Không thể xóa giá');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error deleting price:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPrices]);

  // Set active price
  const setActivePrice = useCallback(async (
    contractId: number, 
    productId: number, 
    priceId: number
  ): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.setActivePrice(contractId, productId, priceId);
      
      if (response.success) {
        // Reload prices after setting active
        await loadPrices();
        return true;
      } else {
        throw new Error('Không thể thiết lập giá hiệu lực');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error setting active price:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadPrices]);

  // Validate price
  const validatePrice = useCallback(async (
    price: number, 
    contractId: number, 
    productId: number
  ): Promise<string[]> => {
    const errors: string[] = [];

    // Basic validation
    if (price <= 0) {
      errors.push('Giá phải lớn hơn 0');
    }

    if (price > 1000000000) {
      errors.push('Giá không được vượt quá 1 tỷ VND');
    }

    // Business logic validation
    try {
      // Check if price is reasonable compared to current price
      const current = await loadCurrentPrice(contractId, productId);
      if (current && Math.abs(price - current) / current > 0.5) {
        errors.push('Giá mới chênh lệch quá 50% so với giá hiện tại');
      }
    } catch (err) {
      console.error('Error validating price:', err);
    }

    return errors;
  }, [loadCurrentPrice]);

  // Calculate price change
  const calculatePriceChange = useCallback((oldPrice: number, newPrice: number) => {
    const amount = newPrice - oldPrice;
    const percentage = oldPrice > 0 ? (amount / oldPrice) * 100 : 0;
    
    return { amount, percentage };
  }, []);

  // Format price
  const formatPrice = useCallback((price: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(price);
  }, []);

  // Auto load on mount
  useEffect(() => {
    if (autoLoad && contractId) {
      loadPrices();
    }
  }, [autoLoad, contractId, loadPrices]);

  // Auto load current price when contract and product are available
  useEffect(() => {
    if (autoLoad && contractId && productId) {
      loadCurrentPrice(contractId, productId);
    }
  }, [autoLoad, contractId, productId, loadCurrentPrice]);

  return {
    // Data
    prices,
    currentPrice,
    priceHistory,
    
    // State
    loading,
    error,
    
    // Actions
    loadPrices,
    loadCurrentPrice,
    loadPriceHistory,
    createPrice,
    updatePrice,
    deletePrice,
    setActivePrice,
    
    // Utilities
    validatePrice,
    calculatePriceChange,
    formatPrice,
  };
};

export default usePrices;
