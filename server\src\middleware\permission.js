/**
 * Permission middleware
 * Kiểm tra quyền truy cập của người dùng
 */

/**
 * Middleware kiểm tra quyền truy cập
 * @param {string} resource - Tài nguyên cần kiểm tra (customer, user, etc.)
 * @param {string} action - Hành động cần kiểm tra (view, edit, delete, etc.)
 * @returns {Function} Express middleware function
 */
const checkPermission = (resource, action) => {
  return (req, res, next) => {
    try {
      // Kiểm tra xem user đã được xác thực chưa
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Chưa xác thực',
            details: ['Vui lòng đăng nhập để truy cập tài nguyên này']
          }
        });
      }

      // Tạm thời cho phép tất cả user đã đăng nhập truy cập mọi tài nguyên
      // Trong tương lai có thể mở rộng với hệ thống phân quyền phức tạp hơn
      const hasPermission = checkUserPermission(req.user, resource, action);

      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Không có quyền truy cập',
            details: [`Bạn không có quyền ${action} trên tài nguyên ${resource}`]
          }
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Lỗi kiểm tra quyền',
          details: ['Có lỗi xảy ra trong quá trình kiểm tra quyền truy cập']
        }
      });
    }
  };
};

/**
 * Kiểm tra quyền của user đối với tài nguyên và hành động cụ thể
 * @param {Object} user - Thông tin user
 * @param {string} resource - Tài nguyên
 * @param {string} action - Hành động
 * @returns {boolean} True nếu có quyền
 */
const checkUserPermission = (user, resource, action) => {
  // Tạm thời cho phép tất cả user đã đăng nhập
  // Có thể mở rộng logic phân quyền ở đây
  
  // Ví dụ logic phân quyền đơn giản:
  if (!user.is_active) {
    return false;
  }

  // Admin có tất cả quyền
  if (user.position && user.position.toLowerCase().includes('admin')) {
    return true;
  }

  // Các quyền cơ bản cho user thường
  const basicPermissions = {
    customer: ['view', 'edit', 'create'],
    user: ['view'] // User thường chỉ có thể xem thông tin user
  };

  if (basicPermissions[resource] && basicPermissions[resource].includes(action)) {
    return true;
  }

  // Mặc định không có quyền
  return false;
};

/**
 * Middleware kiểm tra quyền admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requireAdmin = (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Chưa xác thực',
          details: ['Vui lòng đăng nhập để truy cập tài nguyên này']
        }
      });
    }

    const isAdmin = req.user.position && 
                   req.user.position.toLowerCase().includes('admin');

    if (!isAdmin) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Yêu cầu quyền admin',
          details: ['Chỉ admin mới có thể truy cập tài nguyên này']
        }
      });
    }

    next();
  } catch (error) {
    console.error('Admin check error:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Lỗi kiểm tra quyền admin',
        details: ['Có lỗi xảy ra trong quá trình kiểm tra quyền admin']
      }
    });
  }
};

/**
 * Middleware kiểm tra quyền sở hữu tài nguyên
 * @param {string} resourceIdParam - Tên parameter chứa ID tài nguyên
 * @param {Function} getResourceOwner - Function để lấy owner của tài nguyên
 * @returns {Function} Express middleware function
 */
const requireOwnership = (resourceIdParam, getResourceOwner) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Chưa xác thực',
            details: ['Vui lòng đăng nhập để truy cập tài nguyên này']
          }
        });
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'BAD_REQUEST',
            message: 'Thiếu ID tài nguyên',
            details: ['ID tài nguyên không được cung cấp']
          }
        });
      }

      // Admin có thể truy cập tất cả tài nguyên
      const isAdmin = req.user.position && 
                     req.user.position.toLowerCase().includes('admin');
      
      if (isAdmin) {
        return next();
      }

      // Kiểm tra quyền sở hữu
      const ownerId = await getResourceOwner(resourceId);
      
      if (ownerId !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'Không có quyền truy cập',
            details: ['Bạn chỉ có thể truy cập tài nguyên của chính mình']
          }
        });
      }

      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Lỗi kiểm tra quyền sở hữu',
          details: ['Có lỗi xảy ra trong quá trình kiểm tra quyền sở hữu']
        }
      });
    }
  };
};

module.exports = {
  checkPermission,
  checkUserPermission,
  requireAdmin,
  requireOwnership
};
