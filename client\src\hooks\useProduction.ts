/**
 * useProduction Hook
 * Hook quản lý production logic cho contracts
 */

import { useState, useEffect, useCallback } from 'react';
import { dailyProductionService } from '../services/dailyProductionService';
import { contractPriceService } from '../services/contractPriceService';
import { DailyProduction, DailyProductionFilterOptions } from '../types/dailyProduction';

interface UseProductionOptions {
  contractId?: number;
  productId?: number;
  autoLoad?: boolean;
}

interface ProductionSummary {
  totalQuantity: number;
  totalAmount: number;
  averagePrice: number;
  productionDays: number;
  lastProductionDate: string | null;
  monthlyTotal: number;
  monthlyGrowth: number;
}

interface UseProductionReturn {
  // Data
  productions: DailyProduction[];
  summary: ProductionSummary;
  
  // State
  loading: boolean;
  error: string | null;
  
  // Actions
  loadProductions: (options?: DailyProductionFilterOptions) => Promise<void>;
  loadSummary: (contractId?: number, startDate?: string, endDate?: string) => Promise<void>;
  createProduction: (data: any) => Promise<boolean>;
  updateProduction: (id: number, data: any) => Promise<boolean>;
  deleteProduction: (id: number) => Promise<boolean>;
  confirmProduction: (id: number) => Promise<boolean>;
  
  // Utilities
  calculateAmount: (quantity: number, contractId: number, productId: number, date?: string) => Promise<number>;
  validateProduction: (data: any) => Promise<string[]>;
  formatQuantity: (quantity: number, unit?: string) => string;
  formatAmount: (amount: number) => string;
  getProductionStatus: (production: DailyProduction) => { label: string; color: string };
}

export const useProduction = (options: UseProductionOptions = {}): UseProductionReturn => {
  const { contractId, productId, autoLoad = true } = options;

  // State
  const [productions, setProductions] = useState<DailyProduction[]>([]);
  const [summary, setSummary] = useState<ProductionSummary>({
    totalQuantity: 0,
    totalAmount: 0,
    averagePrice: 0,
    productionDays: 0,
    lastProductionDate: null,
    monthlyTotal: 0,
    monthlyGrowth: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load productions
  const loadProductions = useCallback(async (filterOptions: DailyProductionFilterOptions = {}) => {
    setLoading(true);
    setError(null);

    try {
      const options = {
        ...filterOptions,
        contract_id: contractId,
        product_id: productId,
      };

      const response = await dailyProductionService.getAll(options);
      
      if (response.success) {
        setProductions(response.data);
      } else {
        throw new Error('Không thể tải danh sách sản lượng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error loading productions:', err);
    } finally {
      setLoading(false);
    }
  }, [contractId, productId]);

  // Load summary
  const loadSummary = useCallback(async (
    summaryContractId?: number, 
    startDate?: string, 
    endDate?: string
  ) => {
    try {
      const response = await dailyProductionService.getSummary({
        contract_id: summaryContractId || contractId,
        start_date: startDate,
        end_date: endDate
      });
      
      if (response.success) {
        setSummary(response.data);
      }
    } catch (err) {
      console.error('Error loading production summary:', err);
    }
  }, [contractId]);

  // Create production
  const createProduction = useCallback(async (data: any): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await dailyProductionService.create(data);
      
      if (response.success) {
        // Reload productions after creation
        await loadProductions();
        await loadSummary();
        return true;
      } else {
        throw new Error('Không thể tạo sản lượng mới');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error creating production:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadProductions, loadSummary]);

  // Update production
  const updateProduction = useCallback(async (id: number, data: any): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await dailyProductionService.update(id, data);
      
      if (response.success) {
        // Reload productions after update
        await loadProductions();
        await loadSummary();
        return true;
      } else {
        throw new Error('Không thể cập nhật sản lượng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error updating production:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadProductions, loadSummary]);

  // Delete production
  const deleteProduction = useCallback(async (id: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await dailyProductionService.delete(id);
      
      if (response.success) {
        // Reload productions after deletion
        await loadProductions();
        await loadSummary();
        return true;
      } else {
        throw new Error('Không thể xóa sản lượng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error deleting production:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadProductions, loadSummary]);

  // Confirm production
  const confirmProduction = useCallback(async (id: number): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const response = await dailyProductionService.updateStatus(id, 'confirmed');
      
      if (response.success) {
        // Reload productions after confirmation
        await loadProductions();
        await loadSummary();
        return true;
      } else {
        throw new Error('Không thể xác nhận sản lượng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error confirming production:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [loadProductions, loadSummary]);

  // Calculate amount based on current price
  const calculateAmount = useCallback(async (
    quantity: number, 
    contractId: number, 
    productId: number, 
    date?: string
  ): Promise<number> => {
    try {
      const response = await contractPriceService.getCurrentPrice(contractId, productId, date);
      
      if (response.success && response.data.price) {
        return quantity * response.data.price;
      }
      
      return 0;
    } catch (err) {
      console.error('Error calculating amount:', err);
      return 0;
    }
  }, []);

  // Validate production data
  const validateProduction = useCallback(async (data: any): Promise<string[]> => {
    const errors: string[] = [];

    // Basic validation
    if (!data.contract_id) {
      errors.push('Hợp đồng là bắt buộc');
    }

    if (!data.product_id) {
      errors.push('Sản phẩm là bắt buộc');
    }

    if (!data.production_date) {
      errors.push('Ngày sản xuất là bắt buộc');
    }

    if (data.quantity <= 0) {
      errors.push('Số lượng phải lớn hơn 0');
    }

    if (data.unit_price <= 0) {
      errors.push('Đơn giá phải lớn hơn 0');
    }

    // Date validation
    if (data.production_date) {
      const productionDate = new Date(data.production_date);
      const today = new Date();
      
      if (productionDate > today) {
        errors.push('Ngày sản xuất không thể là ngày tương lai');
      }

      // Check if production date is too old (more than 1 year)
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
      
      if (productionDate < oneYearAgo) {
        errors.push('Ngày sản xuất không thể quá 1 năm');
      }
    }

    // Business logic validation
    if (data.quantity > 10000) {
      errors.push('Số lượng sản xuất có vẻ quá lớn, vui lòng kiểm tra lại');
    }

    return errors;
  }, []);

  // Format quantity
  const formatQuantity = useCallback((quantity: number, unit: string = 'đơn vị'): string => {
    return `${quantity.toLocaleString('vi-VN')} ${unit}`;
  }, []);

  // Format amount
  const formatAmount = useCallback((amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(amount);
  }, []);

  // Get production status
  const getProductionStatus = useCallback((production: DailyProduction) => {
    const statusConfig = {
      'Mới tạo': { label: 'Mới tạo', color: 'default' },
      'Đã xác nhận': { label: 'Đã xác nhận', color: 'success' },
      'Đã ghi nhận công nợ': { label: 'Đã ghi nhận công nợ', color: 'warning' },
      // Backward compatibility
      draft: { label: 'Mới tạo', color: 'default' },
      confirmed: { label: 'Đã xác nhận', color: 'success' },
      invoiced: { label: 'Đã ghi nhận công nợ', color: 'warning' },
      cancelled: { label: 'Đã hủy', color: 'error' }
    };

    return statusConfig[production.status as keyof typeof statusConfig] || statusConfig['Mới tạo'];
  }, []);

  // Auto load on mount
  useEffect(() => {
    if (autoLoad && contractId) {
      loadProductions();
      loadSummary();
    }
  }, [autoLoad, contractId, loadProductions, loadSummary]);

  return {
    // Data
    productions,
    summary,
    
    // State
    loading,
    error,
    
    // Actions
    loadProductions,
    loadSummary,
    createProduction,
    updateProduction,
    deleteProduction,
    confirmProduction,
    
    // Utilities
    calculateAmount,
    validateProduction,
    formatQuantity,
    formatAmount,
    getProductionStatus,
  };
};

export default useProduction;
