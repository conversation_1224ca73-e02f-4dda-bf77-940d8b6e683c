/**
 * Production Confirmation Page
 * Trang xác nhận sản lượng theo tháng
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  Chip,
  CircularProgress,
  Card,
  CardContent,
  Autocomplete,
  TextField
} from '@mui/material';
import {
  CheckCircleOutlined as ConfirmIcon,
  FilterListOutlined as FilterIcon,
  AssessmentOutlined as StatsIcon,
  Description as ReportIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import { formatCurrencyVN, formatQuantityVN, formatDateVN, convertDateToISOString, convertISOStringToDate } from '../utils/vietnameseFormatters';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

import {
  DailyProduction,
  ProductionStatus,
  ProductionConfirmationFilter,
  ProductionStatusStats,
  BulkStatusUpdateResult
} from '../types/dailyProduction';
import { Contract } from '../types/contract';
import { dailyProductionService } from '../services/dailyProductionService';
import { contractService } from '../services/contractService';
import ProductionStatusChip from '../components/production/ProductionStatusChip';
import BulkStatusUpdateDialog from '../components/production/BulkStatusUpdateDialog';
import ProductionReportDialog from '../components/reports/ProductionReportDialog';

const ProductionConfirmation: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [productions, setProductions] = useState<DailyProduction[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [statusStats, setStatusStats] = useState<ProductionStatusStats[]>([]);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [bulkDialogOpen, setBulkDialogOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);

  // Filter mode: 'month' hoặc 'range'
  const [filterMode, setFilterMode] = useState<'month' | 'range'>('month');

  // Filter state
  const [filter, setFilter] = useState<ProductionConfirmationFilter>({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    status: 'Mới tạo'
  });

  // Date range filter state
  const [dateRange, setDateRange] = useState({
    start_date: '',
    end_date: ''
  });

  // Load contracts
  useEffect(() => {
    const loadContracts = async () => {
      try {
        const response = await contractService.getAll({
          status: 'active',
          limit: 1000 // Lấy tất cả hợp đồng
        });
        if (response.success && response.data) {
          setContracts(response.data);
        }
      } catch (error) {
        console.error('Error loading contracts:', error);
      }
    };
    loadContracts();
  }, []);

  // Load productions and stats
  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      // Tạo filter object dựa trên chế độ lọc
      const finalFilter = filterMode === 'month'
        ? filter
        : {
            ...filter,
            start_date: dateRange.start_date,
            end_date: dateRange.end_date,
            year: undefined,
            month: undefined
          };

      // Load productions
      const productionsResponse = await dailyProductionService.getForConfirmation(finalFilter);

      if (productionsResponse.success && productionsResponse.data) {
        setProductions(productionsResponse.data);
      } else {
        setProductions([]);
      }

      // Load status stats
      const statsResponse = filterMode === 'month'
        ? await dailyProductionService.getStatusStats(
            filter.year,
            filter.month,
            filter.contract_id
          )
        : await dailyProductionService.getStatusStats(
            undefined,
            undefined,
            filter.contract_id,
            dateRange.start_date,
            dateRange.end_date
          );

      if (statsResponse.success && statsResponse.data) {
        setStatusStats(statsResponse.data);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  }, [filter, filterMode, dateRange]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle filter changes
  const handleFilterChange = (field: keyof ProductionConfirmationFilter, value: any) => {
    setFilter(prev => ({ ...prev, [field]: value }));
    setSelectedIds([]); // Clear selection when filter changes
  };

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const selectableIds = productions
        .filter(p => p.status === 'Mới tạo')
        .map(p => p.id);
      setSelectedIds(selectableIds);
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  // Handle bulk status update
  const handleBulkUpdate = async (newStatus: ProductionStatus): Promise<BulkStatusUpdateResult> => {
    const result = await dailyProductionService.bulkUpdateStatus({
      production_ids: selectedIds,
      new_status: newStatus
    });

    if (result.success) {
      // Reload data after successful update
      await loadData();
      setSelectedIds([]);
    }

    return result.data || {
      success: false,
      updated_count: 0,
      updated_records: [],
      errors: ['Có lỗi xảy ra khi cập nhật'],
      skipped_count: selectedIds.length
    };
  };

  const selectedProductions = productions.filter(p => selectedIds.includes(p.id));
  const selectableCount = productions.filter(p => p.status === 'Mới tạo').length;
  const isAllSelected = selectableCount > 0 && selectedIds.length === selectableCount;
  const isIndeterminate = selectedIds.length > 0 && selectedIds.length < selectableCount;

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
          Xác nhận Sản lượng Cuối tháng
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Xác nhận sản lượng theo tháng để chuẩn bị tạo công nợ phải thu
        </Typography>
      </Box>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box display="flex" alignItems="center" gap={1} mb={2}>
          <FilterIcon fontSize="small" />
          <Typography variant="h6" sx={{ fontSize: '1rem' }}>
            Bộ lọc
          </Typography>
        </Box>

        {/* Filter Mode Selector */}
        <Box mb={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Chế độ lọc</InputLabel>
            <Select
              value={filterMode}
              label="Chế độ lọc"
              onChange={(e) => setFilterMode(e.target.value as 'month' | 'range')}
            >
              <MenuItem value="month">Lọc theo tháng/năm</MenuItem>
              <MenuItem value="range">Lọc theo giai đoạn</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <Grid container spacing={2}>
          {/* Chế độ lọc theo tháng/năm */}
          {filterMode === 'month' && (
            <>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Năm</InputLabel>
                  <Select
                    value={filter.year}
                    label="Năm"
                    onChange={(e) => handleFilterChange('year', e.target.value)}
                  >
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                      <MenuItem key={year} value={year}>{year}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Tháng</InputLabel>
                  <Select
                    value={filter.month}
                    label="Tháng"
                    onChange={(e) => handleFilterChange('month', e.target.value)}
                  >
                    {Array.from({ length: 12 }, (_, i) => i + 1).map(month => (
                      <MenuItem key={month} value={month}>
                        Tháng {month}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </>
          )}

          {/* Chế độ lọc theo giai đoạn */}
          {filterMode === 'range' && (
            <>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Từ ngày"
                  value={convertISOStringToDate(dateRange.start_date)}
                  onChange={(newValue) => {
                    const dateStr = convertDateToISOString(newValue);
                    setDateRange(prev => ({ ...prev, start_date: dateStr }));
                  }}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      placeholder: 'dd/mm/yyyy'
                    }
                  }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Đến ngày"
                  value={convertISOStringToDate(dateRange.end_date)}
                  onChange={(newValue) => {
                    const dateStr = convertDateToISOString(newValue);
                    setDateRange(prev => ({ ...prev, end_date: dateStr }));
                  }}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      placeholder: 'dd/mm/yyyy'
                    }
                  }}
                />
              </Grid>
            </>
          )}

          <Grid item xs={12} sm={6} md={3}>
            <Autocomplete
              size="small"
              options={contracts}
              getOptionLabel={(option) => `${option.contract_number} - ${option.customer_name}`}
              value={contracts.find(c => c.id === filter.contract_id) || null}
              onChange={(_, newValue) => {
                handleFilterChange('contract_id', newValue?.id || undefined);
              }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Hợp đồng"
                  placeholder="Tìm kiếm hợp đồng..."
                />
              )}
              isOptionEqualToValue={(option, value) => option.id === value.id}
              noOptionsText="Không tìm thấy hợp đồng"
              clearText="Xóa"
              openText="Mở"
              closeText="Đóng"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Trạng thái</InputLabel>
              <Select
                value={filter.status || ''}
                label="Trạng thái"
                onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
              >
                <MenuItem value="">Tất cả trạng thái</MenuItem>
                <MenuItem value="Mới tạo">Mới tạo</MenuItem>
                <MenuItem value="Đã xác nhận">Đã xác nhận</MenuItem>
                <MenuItem value="Đã ghi nhận công nợ">Đã ghi nhận công nợ</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        {/* Report Button */}
        <Box mt={2} display="flex" justifyContent="flex-end">
          <Button
            variant="outlined"
            startIcon={<ReportIcon />}
            onClick={() => setReportDialogOpen(true)}
            disabled={!filter.contract_id}
            sx={{ fontSize: '0.85rem' }}
          >
            Xem báo cáo
          </Button>
        </Box>
      </Paper>

      {/* Status Statistics */}
      {statusStats.length > 0 && (
        <Grid container spacing={2} sx={{ mb: 3 }}>
          {statusStats.map(stat => (
            <Grid item xs={12} sm={6} md={4} key={stat.status}>
              <Card>
                <CardContent sx={{ p: 2 }}>
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                      <ProductionStatusChip status={stat.status} showTooltip={false} />
                      <Typography variant="h6" sx={{ mt: 1, fontSize: '1.25rem' }}>
                        {formatQuantityVN(stat.count)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {formatCurrencyVN(stat.total_amount, true)}
                      </Typography>
                    </Box>
                    <StatsIcon color="action" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Actions */}
      {selectedIds.length > 0 && (
        <Box mb={2}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Đã chọn {selectedIds.length} bản ghi sản lượng
          </Alert>
          <Button
            variant="contained"
            startIcon={<ConfirmIcon />}
            onClick={() => setBulkDialogOpen(true)}
            sx={{ fontSize: '0.85rem' }}
          >
            Xác nhận sản lượng đã chọn
          </Button>
        </Box>
      )}

      {/* Productions Table */}
      <Paper>
        <TableContainer>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={isAllSelected}
                    indeterminate={isIndeterminate}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    disabled={selectableCount === 0}
                  />
                </TableCell>
                <TableCell>Ngày SX</TableCell>
                <TableCell>Hợp đồng</TableCell>
                <TableCell>Khách hàng</TableCell>
                <TableCell>Sản phẩm</TableCell>
                <TableCell align="right">Số lượng</TableCell>
                <TableCell align="right">Đơn giá</TableCell>
                <TableCell align="right">Thành tiền</TableCell>
                <TableCell>Trạng thái</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                    <CircularProgress size={24} />
                  </TableCell>
                </TableRow>
              ) : productions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                    <Typography variant="body2" color="text.secondary">
                      Không có dữ liệu sản lượng
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                productions.map((production) => (
                  <TableRow key={production.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedIds.includes(production.id)}
                        onChange={(e) => handleSelectOne(production.id, e.target.checked)}
                        disabled={production.status !== 'Mới tạo'}
                      />
                    </TableCell>
                    <TableCell>
                      {formatDateVN(production.production_date)}
                    </TableCell>
                    <TableCell>{production.contract_number}</TableCell>
                    <TableCell>{production.customer_name}</TableCell>
                    <TableCell>{production.product_name}</TableCell>
                    <TableCell align="right">
                      {formatQuantityVN(production.quantity)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrencyVN(production.unit_price)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrencyVN(production.total_amount)}
                    </TableCell>
                    <TableCell>
                      <ProductionStatusChip status={production.status} />
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Bulk Status Update Dialog */}
      <BulkStatusUpdateDialog
        open={bulkDialogOpen}
        onClose={() => setBulkDialogOpen(false)}
        selectedProductions={selectedProductions}
        onUpdate={handleBulkUpdate}
      />

      {/* Production Report Dialog */}
      {filter.contract_id && (
        <ProductionReportDialog
          open={reportDialogOpen}
          onClose={() => setReportDialogOpen(false)}
          year={filterMode === 'month' ? filter.year : undefined}
          month={filterMode === 'month' ? filter.month : undefined}
          startDate={filterMode === 'range' ? dateRange.start_date : undefined}
          endDate={filterMode === 'range' ? dateRange.end_date : undefined}
          contractId={filter.contract_id}
          status={filter.status}
          contractName={contracts.find(c => c.id === filter.contract_id)?.contract_number}
          customerName={contracts.find(c => c.id === filter.contract_id)?.customer_name}
        />
      )}
      </Container>
    </LocalizationProvider>
  );
};

export default ProductionConfirmation;
