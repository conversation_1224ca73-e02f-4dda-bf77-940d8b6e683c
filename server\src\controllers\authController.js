const bcrypt = require('bcrypt');
const { pool } = require('../db');
const { 
  generateToken, 
  generateRefreshToken,
  verifyRefreshToken 
} = require('../middleware/auth');
const { 
  createResponse,
  validateRequiredFields,
  validateEmail,
  sanitizeString
} = require('../utils/responseUtils');
const { 
  asyncHand<PERSON>,
  ValidationError,
  UnauthorizedError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * Đăng nhập
 * @route POST /api/v1/auth/login
 * @access Public
 */
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;

  // Validate required fields
  const requiredFields = ['email', 'password'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate email format
  if (email && !validateEmail(email)) {
    validationErrors.push({
      field: 'email',
      message: '<PERSON><PERSON> không đúng định dạng'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      '<PERSON><PERSON> liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Tìm user trong database
  const userQuery = `
    SELECT id, name, email, password, position, is_active, last_login
    FROM users 
    WHERE email = $1
  `;
  
  const userResult = await pool.query(userQuery, [email.toLowerCase()]);
  
  if (userResult.rows.length === 0) {
    throw new UnauthorizedError(
      'Thông tin đăng nhập không chính xác',
      ['Email hoặc mật khẩu không đúng']
    );
  }

  const user = userResult.rows[0];

  // Kiểm tra tài khoản có bị vô hiệu hóa
  if (!user.is_active) {
    throw new UnauthorizedError(
      'Tài khoản đã bị vô hiệu hóa',
      ['Vui lòng liên hệ quản trị viên để kích hoạt lại tài khoản']
    );
  }

  // Kiểm tra mật khẩu
  const isPasswordValid = await bcrypt.compare(password, user.password);
  
  if (!isPasswordValid) {
    throw new UnauthorizedError(
      'Thông tin đăng nhập không chính xác',
      ['Email hoặc mật khẩu không đúng']
    );
  }

  // Cập nhật last_login
  await pool.query(
    'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
    [user.id]
  );

  // Tạo tokens
  const token = generateToken(user);
  const refreshToken = generateRefreshToken(user);

  // Loại bỏ password khỏi response
  const { password: _, ...userWithoutPassword } = user;

  res.status(200).json(createResponse({
    user: userWithoutPassword,
    token,
    refreshToken,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  }));
});

/**
 * Đăng xuất
 * @route POST /api/v1/auth/logout
 * @access Private
 */
const logout = asyncHandler(async (req, res) => {
  // Trong implementation đơn giản, chỉ cần trả về success
  // Trong production có thể implement blacklist token
  
  res.status(200).json(createResponse({
    message: 'Đăng xuất thành công'
  }));
});

/**
 * Refresh token
 * @route POST /api/v1/auth/refresh
 * @access Public
 */
const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new ValidationError(
      'Refresh token không được cung cấp',
      ['Vui lòng cung cấp refresh token']
    );
  }

  try {
    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);
    
    // Lấy thông tin user
    const userQuery = `
      SELECT id, name, email, position, is_active, last_login
      FROM users 
      WHERE id = $1 AND is_active = true
    `;
    
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length === 0) {
      throw new UnauthorizedError(
        'Refresh token không hợp lệ',
        ['User không tồn tại hoặc đã bị vô hiệu hóa']
      );
    }

    const user = userResult.rows[0];

    // Tạo token mới
    const newToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);

    res.status(200).json(createResponse({
      token: newToken,
      refreshToken: newRefreshToken,
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    }));
  } catch (error) {
    throw new UnauthorizedError(
      'Refresh token không hợp lệ',
      ['Refresh token đã hết hạn hoặc không đúng định dạng']
    );
  }
});

/**
 * Lấy thông tin user hiện tại
 * @route GET /api/v1/auth/me
 * @access Private
 */
const getCurrentUser = asyncHandler(async (req, res) => {
  // req.user đã được set bởi authenticateToken middleware
  const { password: _, ...userWithoutPassword } = req.user;
  
  res.status(200).json(createResponse(userWithoutPassword));
});

/**
 * Đăng ký user mới (chỉ admin)
 * @route POST /api/v1/auth/register
 * @access Private (Admin only)
 */
const register = asyncHandler(async (req, res) => {
  const { name, email, password, position } = req.body;

  // Validate required fields
  const requiredFields = ['name', 'email', 'password'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate email format
  if (email && !validateEmail(email)) {
    validationErrors.push({
      field: 'email',
      message: 'Email không đúng định dạng'
    });
  }

  // Validate password strength
  if (password && password.length < 6) {
    validationErrors.push({
      field: 'password',
      message: 'Mật khẩu phải có ít nhất 6 ký tự'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra email đã tồn tại
  const existingUserQuery = 'SELECT id FROM users WHERE email = $1';
  const existingUserResult = await pool.query(existingUserQuery, [email.toLowerCase()]);
  
  if (existingUserResult.rows.length > 0) {
    throw new ConflictError(
      'Email đã được sử dụng',
      ['Email này đã được đăng ký bởi user khác']
    );
  }

  // Hash password
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  // Tạo user mới
  const insertUserQuery = `
    INSERT INTO users (name, email, password, position, is_active)
    VALUES ($1, $2, $3, $4, true)
    RETURNING id, name, email, position, is_active, created_at
  `;

  const newUserResult = await pool.query(insertUserQuery, [
    sanitizeString(name),
    email.toLowerCase(),
    hashedPassword,
    sanitizeString(position) || null
  ]);

  const newUser = newUserResult.rows[0];

  res.status(201).json(createResponse(newUser));
});

module.exports = {
  login,
  logout,
  refreshToken,
  getCurrentUser,
  register
};
