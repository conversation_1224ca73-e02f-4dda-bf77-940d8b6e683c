import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * Loading Spinner Props
 */
interface LoadingSpinnerProps {
  size?: number;
  message?: string;
  fullScreen?: boolean;
}

/**
 * Loading Spinner Component
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 40,
  message = 'Đang tải...',
  fullScreen = false,
}) => {
  const containerSx = fullScreen
    ? {
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        zIndex: 9999,
      }
    : {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        p: 3,
      };

  return (
    <Box sx={containerSx}>
      <CircularProgress size={size} thickness={4} />
      {message && (
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ mt: 2 }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );
};

export default LoadingSpinner;
