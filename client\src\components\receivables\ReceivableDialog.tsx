import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  MenuItem,
  Typography,
  Box,
  Alert
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { vi } from 'date-fns/locale';
import { convertDateToISOString } from '../../utils/vietnameseFormatters';

// Services
import receivableService from '../../services/receivableService';

// Types
import { Receivable } from '../../types/receivable';
import { Customer } from '../../types/customer';
import { Contract } from '../../types/contract';

interface ReceivableDialogProps {
  open: boolean;
  receivable: Receivable | null;
  customers: Customer[];
  contracts: Contract[];
  onClose: () => void;
  onSave: (receivable: Receivable) => void;
  onError: (error: string) => void;
}

interface FormData {
  customer_id: number | '';
  contract_id: number | '';
  invoice_number: string;
  transaction_date: Date;
  due_date: Date;
  original_amount: string;
  description: string;
}

const ReceivableDialog: React.FC<ReceivableDialogProps> = ({
  open,
  receivable,
  customers,
  contracts,
  onClose,
  onSave,
  onError
}) => {
  const [formData, setFormData] = useState<FormData>({
    customer_id: '',
    contract_id: '',
    invoice_number: '',
    transaction_date: new Date(),
    due_date: new Date(),
    original_amount: '',
    description: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [filteredContracts, setFilteredContracts] = useState<Contract[]>([]);

  const isEdit = !!receivable;

  useEffect(() => {
    if (receivable) {
      setFormData({
        customer_id: receivable.customer_id || '',
        contract_id: receivable.contract_id || '',
        invoice_number: receivable.invoice_number || '',
        transaction_date: receivable.transaction_date ? new Date(receivable.transaction_date) : new Date(),
        due_date: receivable.due_date ? new Date(receivable.due_date) : new Date(),
        original_amount: receivable.original_amount?.toString() || '',
        description: receivable.description || ''
      });
    } else {
      // Reset form for new receivable
      setFormData({
        customer_id: '',
        contract_id: '',
        invoice_number: '',
        transaction_date: new Date(),
        due_date: new Date(),
        original_amount: '',
        description: ''
      });
    }
    setErrors({});
  }, [receivable, open]);

  // Filter contracts by selected customer
  useEffect(() => {
    if (formData.customer_id) {
      const customerContracts = contracts.filter(
        contract => contract.customer_id === formData.customer_id
      );
      setFilteredContracts(customerContracts);
      
      // Reset contract selection if current contract doesn't belong to selected customer
      if (formData.contract_id && !customerContracts.find(c => c.id === formData.contract_id)) {
        setFormData(prev => ({ ...prev, contract_id: '' }));
      }
    } else {
      setFilteredContracts([]);
      setFormData(prev => ({ ...prev, contract_id: '' }));
    }
  }, [formData.customer_id, contracts]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.customer_id) {
      newErrors.customer_id = 'Vui lòng chọn khách hàng';
    }

    if (!formData.invoice_number.trim()) {
      newErrors.invoice_number = 'Vui lòng nhập số hóa đơn';
    }

    if (!formData.original_amount.trim()) {
      newErrors.original_amount = 'Vui lòng nhập số tiền';
    } else {
      const amount = parseFloat(formData.original_amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.original_amount = 'Số tiền phải là số dương';
      }
    }

    if (formData.due_date < formData.transaction_date) {
      newErrors.due_date = 'Ngày đến hạn không thể trước ngày giao dịch';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const receivableData = {
        customer_id: formData.customer_id as number,
        contract_id: formData.contract_id || null,
        invoice_number: formData.invoice_number.trim(),
        transaction_date: convertDateToISOString(formData.transaction_date),
        due_date: convertDateToISOString(formData.due_date),
        original_amount: parseFloat(formData.original_amount),
        description: formData.description.trim()
      };

      let savedReceivable: Receivable;

      if (isEdit && receivable) {
        savedReceivable = await receivableService.updateReceivable(receivable.id, receivableData);
      } else {
        savedReceivable = await receivableService.createReceivable(receivableData);
      }

      onSave(savedReceivable);
      onClose();
    } catch (error: any) {
      onError(error.message || 'Không thể lưu công nợ');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: '500px' }
        }}
      >
        <DialogTitle>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {isEdit ? 'Cập nhật Công nợ' : 'Thêm Công nợ mới'}
          </Typography>
        </DialogTitle>

        <DialogContent dividers>
          <Grid container spacing={3}>
            {/* Customer Selection */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Khách hàng *"
                value={formData.customer_id}
                onChange={(e) => handleInputChange('customer_id', parseInt(e.target.value) || '')}
                error={!!errors.customer_id}
                helperText={errors.customer_id}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              >
                <MenuItem value="">Chọn khách hàng</MenuItem>
                {Array.isArray(customers) && customers.map((customer) => (
                  <MenuItem key={customer.id} value={customer.id}>
                    {customer.name} {customer.tax_code && `(${customer.tax_code})`}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Contract Selection */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Hợp đồng"
                value={formData.contract_id}
                onChange={(e) => handleInputChange('contract_id', parseInt(e.target.value) || '')}
                disabled={!formData.customer_id}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              >
                <MenuItem value="">Không chọn hợp đồng</MenuItem>
                {filteredContracts.map((contract) => (
                  <MenuItem key={contract.id} value={contract.id}>
                    {contract.contract_number} - {contract.name}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>

            {/* Invoice Number */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Số hóa đơn *"
                value={formData.invoice_number}
                onChange={(e) => handleInputChange('invoice_number', e.target.value)}
                error={!!errors.invoice_number}
                helperText={errors.invoice_number}
                placeholder="VD: INV-2024-001"
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Original Amount */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Số tiền *"
                type="number"
                value={formData.original_amount}
                onChange={(e) => handleInputChange('original_amount', e.target.value)}
                error={!!errors.original_amount}
                helperText={errors.original_amount}
                placeholder="0"
                InputProps={{
                  endAdornment: <Typography variant="body2" color="text.secondary">VNĐ</Typography>
                }}
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Transaction Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Ngày giao dịch *"
                value={formData.transaction_date}
                onChange={(date) => handleInputChange('transaction_date', date || new Date())}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.transaction_date,
                    helperText: errors.transaction_date,
                    sx: { '& .MuiInputBase-input': { fontSize: '0.85rem' } }
                  }
                }}
              />
            </Grid>

            {/* Due Date */}
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Ngày đến hạn *"
                value={formData.due_date}
                onChange={(date) => handleInputChange('due_date', date || new Date())}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.due_date,
                    helperText: errors.due_date,
                    sx: { '& .MuiInputBase-input': { fontSize: '0.85rem' } }
                  }
                }}
              />
            </Grid>

            {/* Description */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Mô tả"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Mô tả chi tiết về công nợ..."
                sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
              />
            </Grid>

            {/* Info Alert */}
            {!isEdit && (
              <Grid item xs={12}>
                <Alert severity="info" sx={{ fontSize: '0.85rem' }}>
                  Công nợ mới sẽ được tạo với trạng thái "Hoạt động" và số dư bằng số tiền gốc.
                </Alert>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button 
            onClick={handleClose}
            disabled={loading}
            sx={{ fontSize: '0.85rem' }}
          >
            Hủy
          </Button>
          <Button 
            variant="contained"
            onClick={handleSave}
            disabled={loading}
            sx={{ fontSize: '0.85rem' }}
          >
            {loading ? 'Đang lưu...' : (isEdit ? 'Cập nhật' : 'Tạo mới')}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ReceivableDialog;
