const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const dailyProductionController = require('../controllers/dailyProductionController');
const { authenticateToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

// Validation error handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Dữ liệu không hợp lệ',
      errors: errors.array()
    });
  }
  next();
};

const router = express.Router();

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// Middleware xác thực cho tất cả routes
// router.use(authenticateToken);

/**
 * @route   GET /api/v1/daily-production/grouped
 * @desc    Lấy sản lượng đã nhóm theo hợp đồng và ngày
 * @access  Private
 */
router.get('/grouped', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Trang phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Giới hạn phải từ 1 đến 100'),
  handleValidationErrors
], dailyProductionController.getGroupedProduction);

/**
 * @route   GET /api/v1/daily-production/report
 * @desc    Lấy báo cáo sản lượng theo khoảng thời gian
 * @access  Private
 */
router.get('/report', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  query('date_from')
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('date_to')
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  handleValidationErrors
], dailyProductionController.getProductionReport);

/**
 * @route   GET /api/v1/daily-production/by-date
 * @desc    Lấy sản lượng theo ngày cụ thể
 * @access  Private
 */
router.get('/by-date', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  query('production_date')
    .isISO8601()
    .withMessage('Ngày sản xuất phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  handleValidationErrors
], dailyProductionController.getProductionByDate);

/**
 * @route   GET /api/v1/daily-production/search
 * @desc    Tìm kiếm sản lượng để tạo công nợ
 * @access  Private
 */
router.get('/search', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  query('contractId')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('status')
    .optional()
    .isIn(['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'])
    .withMessage('Trạng thái không hợp lệ'),
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  handleValidationErrors
], dailyProductionController.searchForReceivable);

/**
 * @route   POST /api/v1/daily-production/import
 * @desc    Import sản lượng từ Excel
 * @access  Private
 */
router.post('/import', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'create'),
  body('data')
    .isArray({ min: 1 })
    .withMessage('Dữ liệu import phải là mảng và không được rỗng'),
  body('data.*.contract_number')
    .notEmpty()
    .withMessage('Số hợp đồng là bắt buộc'),
  body('data.*.production_date')
    .notEmpty()
    .withMessage('Ngày sản xuất là bắt buộc'),
  body('data.*.product_code')
    .notEmpty()
    .withMessage('Mã sản phẩm là bắt buộc'),
  body('data.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Số lượng phải là số dương'),
  body('data.*.notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Ghi chú không được vượt quá 500 ký tự'),
  handleValidationErrors
], dailyProductionController.importProduction);

/**
 * @route   POST /api/v1/daily-production/bulk
 * @desc    Tạo sản lượng hàng loạt
 * @access  Private
 */
router.post('/bulk', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'create'),
  body('production_date')
    .isISO8601()
    .withMessage('Ngày sản xuất phải có định dạng YYYY-MM-DD'),
  body('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Danh sách sản phẩm phải là mảng và không được rỗng'),
  body('items.*.product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Số lượng phải là số dương'),
  body('items.*.unit_price')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('items.*.notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Ghi chú không được vượt quá 500 ký tự'),
  body('auto_get_price')
    .optional()
    .isBoolean()
    .withMessage('auto_get_price phải là boolean'),
  handleValidationErrors
], dailyProductionController.createBulkProduction);

/**
 * @route   GET /api/v1/daily-production
 * @desc    Lấy tất cả sản lượng với filter
 * @access  Private
 */
router.get('/', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit phải là số nguyên từ 1-100'),
  query('production_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày sản xuất phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Contract ID phải là số nguyên dương'),
  query('product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID phải là số nguyên dương'),
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  handleValidationErrors
], dailyProductionController.getAllDailyProduction);

/**
 * @route   PUT /api/v1/daily-production/bulk-status
 * @desc    Cập nhật trạng thái hàng loạt cho sản lượng
 * @access  Private
 */
router.put('/bulk-status', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'update'),
  body('production_ids')
    .isArray({ min: 1 })
    .withMessage('Danh sách ID sản lượng phải là mảng không rỗng'),
  body('production_ids.*')
    .isInt({ min: 1 })
    .withMessage('ID sản lượng phải là số nguyên dương'),
  body('new_status')
    .isIn(['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'])
    .withMessage('Trạng thái mới không hợp lệ'),
  handleValidationErrors
], dailyProductionController.bulkUpdateStatus);

/**
 * @route   GET /api/v1/daily-production/for-confirmation
 * @desc    Lấy sản lượng theo tháng hoặc giai đoạn để xác nhận
 * @access  Private
 */
router.get('/for-confirmation', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'read'),
  query('year')
    .optional()
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Năm phải từ 2020 đến 2030'),
  query('month')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('Tháng phải từ 1 đến 12'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('status')
    .optional()
    .isIn(['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'])
    .withMessage('Trạng thái không hợp lệ'),
  handleValidationErrors
], dailyProductionController.getProductionForConfirmation);

/**
 * @route   GET /api/v1/daily-production/status-stats
 * @desc    Lấy thống kê sản lượng theo trạng thái
 * @access  Private
 */
router.get('/status-stats', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'read'),
  query('year')
    .optional()
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Năm phải từ 2020 đến 2030'),
  query('month')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('Tháng phải từ 1 đến 12'),
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày bắt đầu phải có định dạng YYYY-MM-DD'),
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày kết thúc phải có định dạng YYYY-MM-DD'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  handleValidationErrors
], dailyProductionController.getProductionStatusStats);

/**
 * @route   GET /api/v1/daily-production/monthly-report
 * @desc    Lấy báo cáo sản lượng theo tháng
 * @access  Private
 */
router.get('/monthly-report', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'read'),
  query('year')
    .isInt({ min: 2020, max: 2030 })
    .withMessage('Năm phải từ 2020 đến 2030'),
  query('month')
    .isInt({ min: 1, max: 12 })
    .withMessage('Tháng phải từ 1 đến 12'),
  query('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('status')
    .optional()
    .isIn(['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ', 'all'])
    .withMessage('Trạng thái không hợp lệ'),
  handleValidationErrors
], dailyProductionController.getMonthlyProductionReport);

/**
 * @route   GET /api/v1/daily-production/:id
 * @desc    Lấy sản lượng theo ID
 * @access  Private
 */
router.get('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'view'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản lượng phải là số nguyên dương'),
  handleValidationErrors
], dailyProductionController.getDailyProductionById);

/**
 * @route   POST /api/v1/daily-production
 * @desc    Tạo sản lượng mới
 * @access  Private
 */
router.post('/', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'create'),
  body('production_date')
    .isISO8601()
    .withMessage('Ngày sản xuất phải có định dạng YYYY-MM-DD'),
  body('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Số lượng phải là số dương'),
  body('unit_price')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Ghi chú không được vượt quá 500 ký tự'),
  body('auto_get_price')
    .optional()
    .isBoolean()
    .withMessage('auto_get_price phải là boolean'),
  handleValidationErrors
], dailyProductionController.createDailyProduction);

/**
 * @route   PUT /api/v1/daily-production/:id
 * @desc    Cập nhật sản lượng
 * @access  Private (Manager+)
 */
router.put('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'update'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản lượng phải là số nguyên dương'),
  body('production_date')
    .isISO8601()
    .withMessage('Ngày sản xuất phải có định dạng YYYY-MM-DD'),
  body('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Số lượng phải là số dương'),
  body('unit_price')
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Ghi chú không được vượt quá 500 ký tự'),
  handleValidationErrors
], dailyProductionController.updateDailyProduction);

/**
 * @route   DELETE /api/v1/daily-production/:id
 * @desc    Xóa sản lượng
 * @access  Private (Admin only)
 */
router.delete('/:id', [
  // NOTE: Tạm thời comment out permission check
  // checkPermission('daily_production', 'delete'),
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID sản lượng phải là số nguyên dương'),
  handleValidationErrors
], dailyProductionController.deleteDailyProduction);

module.exports = router;
