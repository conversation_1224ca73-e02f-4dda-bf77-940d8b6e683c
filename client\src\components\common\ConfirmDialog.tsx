import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  IconButton,
} from '@mui/material';
import {
  Close as CloseIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
} from '@mui/icons-material';

/**
 * Confirm Dialog Props Interface
 */
export interface ConfirmDialogProps {
  open: boolean;
  title: string;
  message: string;
  type?: 'warning' | 'error' | 'info' | 'success';
  confirmText?: string;
  cancelText?: string;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  loading?: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * Get icon by dialog type
 */
const getIconByType = (type: string) => {
  switch (type) {
    case 'warning':
      return <WarningIcon sx={{ fontSize: 48, color: 'warning.main' }} />;
    case 'error':
      return <ErrorIcon sx={{ fontSize: 48, color: 'error.main' }} />;
    case 'success':
      return <SuccessIcon sx={{ fontSize: 48, color: 'success.main' }} />;
    case 'info':
    default:
      return <InfoIcon sx={{ fontSize: 48, color: 'info.main' }} />;
  }
};

/**
 * Confirm Dialog Component
 * Reusable confirmation dialog with different types and customizable actions
 */
const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  title,
  message,
  type = 'info',
  confirmText = 'Xác nhận',
  cancelText = 'Hủy',
  confirmColor = 'primary',
  loading = false,
  onConfirm,
  onCancel,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: 200,
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 1,
          fontSize: '1rem',
          fontWeight: 600,
        }}
      >
        {title}
        <IconButton
          onClick={onCancel}
          size="small"
          disabled={loading}
          sx={{ ml: 1 }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>

      {/* Dialog Content */}
      <DialogContent sx={{ pt: 1, pb: 2 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: 2,
            minHeight: 80,
          }}
        >
          {/* Icon */}
          <Box sx={{ flexShrink: 0, mt: 0.5 }}>
            {getIconByType(type)}
          </Box>

          {/* Message */}
          <Box sx={{ flex: 1, pt: 1 }}>
            <Typography
              variant="body1"
              sx={{
                fontSize: '0.9rem',
                lineHeight: 1.5,
                color: 'text.primary',
              }}
            >
              {message}
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      {/* Dialog Actions */}
      <DialogActions
        sx={{
          px: 3,
          pb: 2,
          gap: 1,
          justifyContent: 'flex-end',
        }}
      >
        <Button
          onClick={onCancel}
          disabled={loading}
          variant="outlined"
          size="medium"
          sx={{
            minWidth: 80,
            fontSize: '0.85rem',
            textTransform: 'none',
          }}
        >
          {cancelText}
        </Button>
        <Button
          onClick={onConfirm}
          disabled={loading}
          variant="contained"
          color={confirmColor}
          size="medium"
          sx={{
            minWidth: 80,
            fontSize: '0.85rem',
            textTransform: 'none',
          }}
        >
          {loading ? 'Đang xử lý...' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDialog;
