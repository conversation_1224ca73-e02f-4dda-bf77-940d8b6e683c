/**
 * Test Page for QuickPriceForm
 * Trang test để kiểm tra QuickPriceForm component
 */

import React, { useState } from 'react';
import {
  Container,
  Typography,
  Button,
  Box,
  Alert
} from '@mui/material';
import { Add as AddIcon } from '@mui/icons-material';

// Components
import QuickPriceForm from '../components/pricing/QuickPriceForm';

// Services
import { contractPriceService } from '../services/contractPriceService';

const TestQuickPrice: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleSubmit = async (data: any) => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('QuickPriceForm data:', data);
      
      // Create multiple prices
      for (const item of data.items) {
        const priceData = {
          contract_id: data.contract_id,
          product_id: item.product_id,
          price: item.price,
          effective_date: data.effective_date,
          expiry_date: data.expiry_date || null,
          notes: `Quick price update test - ${new Date().toISOString()}`
        };
        
        console.log('Creating price:', priceData);
        const response = await contractPriceService.create(priceData);
        console.log('Price created:', response);
      }
      
      setSuccess('Cập nhật giá nhanh thành công!');
      setOpen(false);
    } catch (err: any) {
      console.error('Error creating prices:', err);
      setError(err.message || 'Có lỗi xảy ra khi cập nhật giá');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Test QuickPriceForm
        </Typography>
        <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
          Trang test để kiểm tra tính năng cập nhật giá nhanh
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpen(true)}
          size="large"
        >
          Mở QuickPriceForm
        </Button>
      </Box>

      <QuickPriceForm
        open={open}
        onClose={() => setOpen(false)}
        onSubmit={handleSubmit}
        loading={loading}
        error={error}
        title="Test - Cập nhật giá nhanh"
        subtitle="Đây là trang test để kiểm tra QuickPriceForm"
      />
    </Container>
  );
};

export default TestQuickPrice;
