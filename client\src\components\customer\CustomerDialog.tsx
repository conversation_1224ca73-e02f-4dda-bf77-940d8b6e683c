import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Box,
  Alert,
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { Customer, CustomerFormData, CustomerDialogMode } from '../../types/customer';
import { customerService } from '../../services/customerService';
import CustomerForm from './CustomerForm';
import CustomerDetail from './CustomerDetail';

/**
 * Customer Dialog Props Interface
 */
export interface CustomerDialogProps {
  open: boolean;
  mode: CustomerDialogMode;
  customer?: Customer | null;
  onClose: () => void;
  onSuccess: (customer: Customer, action: 'create' | 'edit') => void;
}

/**
 * Customer Dialog Component
 * Modal dialog for customer create, edit, and view operations
 */
const CustomerDialog: React.FC<CustomerDialogProps> = ({
  open,
  mode,
  customer,
  onClose,
  onSuccess,
}) => {
  // State
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFormValid, setIsFormValid] = useState(false);

  /**
   * Get dialog title based on mode
   */
  const getDialogTitle = (): string => {
    switch (mode) {
      case 'create':
        return 'Thêm khách hàng mới';
      case 'edit':
        return 'Chỉnh sửa khách hàng';
      case 'view':
        return 'Thông tin khách hàng';
      default:
        return 'Khách hàng';
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (formData: CustomerFormData) => {
    try {
      setLoading(true);
      setError(null);

      let result: Customer;

      if (mode === 'create') {
        result = await customerService.createCustomer(formData);
        onSuccess(result, 'create');
      } else if (mode === 'edit' && customer) {
        result = await customerService.updateCustomer(customer.id, formData);
        onSuccess(result, 'edit');
      }

      onClose();
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle dialog close
   */
  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  /**
   * Handle validation change
   */
  const handleValidationChange = (isValid: boolean) => {
    setIsFormValid(isValid);
  };

  /**
   * Check if dialog should show actions
   */
  const shouldShowActions = mode !== 'view';

  /**
   * Check if submit button should be disabled
   */
  const isSubmitDisabled = loading || (mode !== 'view' && !isFormValid);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: mode === 'view' ? 400 : 500,
          maxHeight: '90vh',
        },
      }}
    >
      {/* Dialog Header */}
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
          fontSize: '1.1rem',
          fontWeight: 600,
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        {getDialogTitle()}
        <IconButton
          onClick={handleClose}
          size="small"
          disabled={loading}
          sx={{ ml: 1 }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>

      {/* Dialog Content */}
      <DialogContent
        sx={{
          pt: 3,
          pb: shouldShowActions ? 2 : 3,
          position: 'relative',
          minHeight: 300,
        }}
      >
        {/* Error Alert */}
        {error && (
          <Alert
            severity="error"
            onClose={() => setError(null)}
            sx={{ mb: 3, fontSize: '0.85rem' }}
          >
            {error}
          </Alert>
        )}

        {/* Content based on mode */}
        {mode === 'view' ? (
          <CustomerDetail customer={customer} />
        ) : (
          <CustomerForm
            customer={customer}
            mode={mode}
            loading={loading}
            onSubmit={handleSubmit}
            onValidationChange={handleValidationChange}
          />
        )}
      </DialogContent>

      {/* Dialog Actions */}
      {shouldShowActions && (
        <DialogActions
          sx={{
            px: 3,
            pb: 3,
            pt: 1,
            gap: 1,
            justifyContent: 'flex-end',
            borderTop: '1px solid',
            borderColor: 'divider',
          }}
        >
          <Button
            onClick={handleClose}
            disabled={loading}
            variant="outlined"
            size="medium"
            sx={{
              minWidth: 100,
              fontSize: '0.85rem',
              textTransform: 'none',
            }}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            disabled={isSubmitDisabled}
            variant="contained"
            size="medium"
            onClick={() => {
              // Trigger form submission
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            sx={{
              minWidth: 100,
              fontSize: '0.85rem',
              textTransform: 'none',
            }}
          >
            {loading
              ? 'Đang xử lý...'
              : mode === 'create'
              ? 'Thêm khách hàng'
              : 'Cập nhật'}
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default CustomerDialog;
