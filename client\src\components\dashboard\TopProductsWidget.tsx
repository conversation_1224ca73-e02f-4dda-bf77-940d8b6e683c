import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  LinearProgress,
  useTheme
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';
import { TopProduct } from '../../services/dashboardService';
import { formatCurrencyToMillions } from '../../utils/formatters';

interface TopProductsWidgetProps {
  data: TopProduct[] | null;
  title?: string;
  period?: string;
  loading?: boolean;
}

const TopProductsWidget: React.FC<TopProductsWidgetProps> = ({
  data,
  title = 'Top 5 sản phẩm',
  period = 'hôm nay',
  loading = false
}) => {
  const theme = useTheme();

  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toLocaleString('vi-VN');
  };

  // Tính phần trăm cho progress bar
  const getProgressPercentage = (value: string, maxValue: string): number => {
    const val = parseFloat(value);
    const max = parseFloat(maxValue);
    if (isNaN(val) || isNaN(max) || max === 0) return 0;
    return (val / max) * 100;
  };

  const maxQuantity = data && data.length > 0 ? data[0].total_quantity : '0';

  const getUnitLabel = (unitType: string): string => {
    const unitMap: { [key: string]: string } = {
      'piece': 'cái',
      'kg': 'kg',
      'set': 'bộ',
      'meter': 'm',
      'liter': 'lít'
    };
    return unitMap[unitType] || unitType;
  };

  return (
    <Card
      elevation={2}
      className="laundry-hover-lift"
      sx={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: '1px solid rgba(34, 197, 94, 0.1)',
        borderRadius: 2,
        fontSize: '0.85rem'
      }}
    >
      <CardContent sx={{ p: 2.5 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <TrendingUpIcon 
            sx={{ 
              color: theme.palette.success.main, 
              mr: 1.5,
              fontSize: '1.5rem'
            }} 
          />
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                color: theme.palette.success.main,
                fontSize: '1.1rem'
              }}
            >
              {title}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '0.75rem' }}
            >
              Theo sản lượng {period}
            </Typography>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              Đang tải dữ liệu...
            </Typography>
          </Box>
        ) : (
          <>
            {data && data.length > 0 ? (
              <List sx={{ p: 0 }}>
                {data.map((product, index) => (
                  <ListItem
                    key={product.id}
                    sx={{
                      px: 0,
                      py: 2,
                      borderBottom: index < data.length - 1 ? '1px solid' : 'none',
                      borderColor: 'divider',
                      '&:hover': {
                        backgroundColor: '#f8fafc'
                      }
                    }}
                  >
                    {/* Ranking Badge - More prominent with color coding */}
                    <Box
                      sx={{
                        minWidth: 36,
                        height: 36,
                        borderRadius: '50%',
                        backgroundColor: index === 0 ? '#fef3c7' : index === 1 ? '#e5e7eb' : index === 2 ? '#fed7aa' : '#f3f4f6',
                        color: index === 0 ? '#92400e' : index === 1 ? '#374151' : index === 2 ? '#9a3412' : '#6b7280',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.875rem',
                        fontWeight: 700,
                        mr: 2,
                        border: index < 3 ? '2px solid' : '1px solid',
                        borderColor: index === 0 ? '#f59e0b' : index === 1 ? '#9ca3af' : index === 2 ? '#f97316' : '#d1d5db'
                      }}
                    >
                      #{index + 1}
                    </Box>
                    
                    <Box sx={{ flex: 1 }}>
                      {/* Product Name & Code - Simplified */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 600,
                            fontSize: '0.9rem',
                            color: '#1e293b',
                            lineHeight: 1.2
                          }}
                        >
                          {product.product_name}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '0.75rem',
                            color: '#64748b',
                            fontWeight: 500
                          }}
                        >
                          {product.product_code}
                        </Typography>
                      </Box>

                      {/* Progress Bar - More prominent */}
                      <Box sx={{ mb: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={getProgressPercentage(product.total_quantity, maxQuantity)}
                          sx={{
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: '#f1f5f9',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: index === 0 ? '#059669' : index === 1 ? '#0891b2' : index === 2 ? '#d97706' : '#64748b',
                              borderRadius: 4
                            }
                          }}
                        />
                      </Box>

                      {/* Key Metrics - Streamlined */}
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '0.8rem',
                            fontWeight: 600,
                            color: '#374151'
                          }}
                        >
                          {formatNumber(product.total_quantity)} {getUnitLabel(product.unit_type)}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontSize: '0.8rem',
                            fontWeight: 600,
                            color: '#059669'
                          }}
                        >
                          {formatCurrencyToMillions(product.total_value)}
                        </Typography>
                      </Box>
                    </Box>
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 4,
                  color: 'text.secondary'
                }}
              >
                <InventoryIcon sx={{ fontSize: '3rem', opacity: 0.3, mb: 1 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  Chưa có dữ liệu sản phẩm {period}
                </Typography>
                <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                  Dữ liệu sẽ hiển thị khi có sản lượng được ghi nhận
                </Typography>
              </Box>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default TopProductsWidget;
