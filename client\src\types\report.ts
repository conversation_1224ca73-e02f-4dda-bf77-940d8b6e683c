/**
 * Report Types
 * <PERSON><PERSON><PERSON> nghĩa types cho reports và aging analysis
 */

export interface AgingAnalysisItem {
  customer_id: number;
  customer_name: string;
  tax_code?: string;
  short_name?: string;
  total_invoices: number;
  active_invoices: number;
  overdue_invoices: number;
  paid_invoices: number;
  total_invoiced: number;
  total_payments: number;
  total_outstanding: number;
  current_amount: number;
  days_1_30: number;
  days_31_60: number;
  days_61_90: number;
  over_90_days: number;
  overdue_percentage: number;
  max_days_overdue: number;
  oldest_overdue_date?: string;
  
  // Percentage breakdowns
  current_percentage: number;
  days_1_30_percentage: number;
  days_31_60_percentage: number;
  days_61_90_percentage: number;
  over_90_days_percentage: number;
}

export interface AgingAnalysisFilters {
  customerId?: number;
  asOfDate?: string;
  includeZeroBalance?: boolean;
}

export interface CustomerDebtSummary {
  summary: {
    total_receivables: number;
    total_payments: number;
    total_outstanding: number;
    overdue_amount: number;
    current_amount: number;
  };
  receivables: Array<{
    id: number;
    invoice_number: string;
    transaction_date: string;
    due_date: string;
    description: string;
    original_amount: number;
    total_paid: number;
    remaining_balance: number;
    balance_status: string;
    days_overdue: number;
    current_amount: number;
    days_1_30: number;
    days_31_60: number;
    days_61_90: number;
    over_90_days: number;
    contract_number: string;
    contract_name: string;
  }>;
  recent_payments: Array<{
    id: number;
    payment_date: string;
    amount: number;
    payment_method: string;
    reference_number?: string;
    description?: string;
    status: string;
  }>;
}

export interface OverallDebtSummary {
  total_customers: number;
  total_invoices: number;
  total_invoiced: number;
  total_payments: number;
  total_outstanding: number;
  total_current: number;
  total_1_30: number;
  total_31_60: number;
  total_61_90: number;
  total_over_90: number;
  customers_with_debt: number;
  customers_with_overdue: number;
  avg_overdue_percentage: number;
  max_days_overdue_system: number;
  
  // Percentage breakdowns
  current_percentage: string;
  days_1_30_percentage: string;
  days_31_60_percentage: string;
  days_61_90_percentage: string;
  over_90_days_percentage: string;
}

export interface ContractDebtSummary {
  contract_id: number;
  contract_number: string;
  contract_name: string;
  customer_id: number;
  customer_name: string;
  tax_code?: string;
  start_date: string;
  end_date?: string;
  contract_status: string;
  total_invoices: number;
  total_invoiced: number;
  total_payments: number;
  total_outstanding: number;
  current_amount: number;
  days_1_30: number;
  days_31_60: number;
  days_61_90: number;
  over_90_days: number;
  max_days_overdue: number;
  overdue_percentage: number;
}

export interface PaymentHistoryDetail {
  payment_id: number;
  customer_id: number;
  customer_name: string;
  tax_code?: string;
  payment_date: string;
  payment_amount: number;
  payment_method: string;
  reference_number?: string;
  bank_account?: string;
  payment_description?: string;
  payment_status: string;
  allocation_id?: number;
  receivable_id?: number;
  invoice_number?: string;
  invoice_date?: string;
  invoice_due_date?: string;
  invoice_amount?: number;
  allocated_amount?: number;
  allocation_order?: number;
  payment_created_at: string;
}

export interface PaymentHistoryFilters {
  customerId?: number;
  paymentId?: number;
  startDate?: string;
  endDate?: string;
  limit?: number;
}

export interface DashboardData {
  overall_summary: OverallDebtSummary;
  top_debtors: AgingAnalysisItem[];
  aging_chart: {
    current: number;
    days_1_30: number;
    days_31_60: number;
    days_61_90: number;
    over_90_days: number;
  };
  summary_stats: {
    total_customers_with_debt: number;
    total_customers_with_overdue: number;
    avg_overdue_percentage: string;
    max_days_overdue: number;
  };
}

export interface ChartDataPoint {
  name: string;
  value: number;
  percentage: number;
  color: string;
}
