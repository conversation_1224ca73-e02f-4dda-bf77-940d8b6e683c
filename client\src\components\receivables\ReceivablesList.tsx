import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  FileDownload as ExportIcon,
  Refresh as RefreshIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';

// Services
import receivableService from '../../services/receivableService';
import customerService from '../../services/customerService';
import contractService from '../../services/contractService';

// Components
import LoadingSpinner from '../common/LoadingSpinner';
import ConfirmDialog from '../common/ConfirmDialog';
import ReceivableDialog from './ReceivableDialog';
import ProductionSelectionDialog from './ProductionSelectionDialog';

// Types
import { Receivable, ReceivableFilters } from '../../types/receivable';
import { Customer } from '../../types/customer';
import { Contract } from '../../types/contract';

interface ReceivablesListProps {
  refreshTrigger: number;
  onReceivableCreated: (receivable: Receivable) => void;
  onReceivableUpdated: (receivable: Receivable) => void;
  onReceivableDeleted: (invoiceNumber: string) => void;
  onError: (error: string) => void;
}

const ReceivablesList: React.FC<ReceivablesListProps> = ({
  refreshTrigger,
  onReceivableCreated,
  onReceivableUpdated,
  onReceivableDeleted,
  onError
}) => {
  const [receivables, setReceivables] = useState<Receivable[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 10,
    total: 0
  });

  // Filters
  const [filters, setFilters] = useState<ReceivableFilters>({
    page: 1,
    limit: 10,
    search: '',
    customerId: undefined,
    contractId: undefined,
    status: '',
    sortBy: 'transaction_date',
    sortOrder: 'DESC'
  });

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedReceivable, setSelectedReceivable] = useState<Receivable | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [receivableToDelete, setReceivableToDelete] = useState<Receivable | null>(null);
  const [productionDialogOpen, setProductionDialogOpen] = useState(false);

  // Load data
  const loadReceivables = async () => {
    try {
      setLoading(true);
      const response = await receivableService.getReceivables(filters);
      setReceivables(response.receivables);
      setPagination({
        page: response.pagination.page - 1, // DataGrid uses 0-based indexing
        pageSize: response.pagination.limit,
        total: response.pagination.total
      });
    } catch (error: any) {
      onError(error.message || 'Không thể tải danh sách công nợ');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const response = await customerService.getCustomers({ limit: 1000 });
      // Ensure we're setting an array, handle both response formats
      const customersArray = response.customers || response.data || [];
      setCustomers(Array.isArray(customersArray) ? customersArray : []);
    } catch (error: any) {
      console.error('Error loading customers:', error);
      setCustomers([]); // Set empty array on error to prevent map error
    }
  };

  const loadContracts = async () => {
    try {
      const response = await contractService.getContracts({ limit: 1000 });
      // Ensure we're setting an array, handle both response formats
      const contractsArray = response.contracts || response.data || [];
      setContracts(Array.isArray(contractsArray) ? contractsArray : []);
    } catch (error: any) {
      console.error('Error loading contracts:', error);
      setContracts([]); // Set empty array on error to prevent map error
    }
  };

  useEffect(() => {
    loadReceivables();
  }, [filters, refreshTrigger]);

  useEffect(() => {
    loadCustomers();
    loadContracts();
  }, []);

  // Handlers
  const handleFilterChange = (field: keyof ReceivableFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage + 1 // Convert to 1-based indexing
    }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setFilters(prev => ({
      ...prev,
      limit: newPageSize,
      page: 1
    }));
  };

  const handleCreate = () => {
    setSelectedReceivable(null);
    setDialogOpen(true);
  };

  const handleCreateFromProduction = () => {
    setProductionDialogOpen(true);
  };

  const handleEdit = (receivable: Receivable) => {
    setSelectedReceivable(receivable);
    setDialogOpen(true);
  };

  const handleView = (receivable: Receivable) => {
    setSelectedReceivable(receivable);
    setDialogOpen(true);
  };

  const handleDelete = (receivable: Receivable) => {
    setReceivableToDelete(receivable);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!receivableToDelete) return;

    try {
      await receivableService.deleteReceivable(receivableToDelete.id);
      onReceivableDeleted(receivableToDelete.invoice_number);
      setDeleteDialogOpen(false);
      setReceivableToDelete(null);
    } catch (error: any) {
      onError(error.message || 'Không thể xóa công nợ');
    }
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedReceivable(null);
  };

  const handleReceivableSaved = (receivable: Receivable) => {
    if (selectedReceivable) {
      onReceivableUpdated(receivable);
    } else {
      onReceivableCreated(receivable);
    }
    handleDialogClose();
  };

  const handleRefresh = () => {
    loadReceivables();
  };

  const handleCreateFromProductionData = async (productionData: any) => {
    try {
      // Auto-populate receivable form data from production
      const { contract, selectedProductions, totalAmount, dateRange } = productionData;

      console.log('Production data received:', productionData);
      console.log('Contract data:', contract);
      console.log('Selected productions:', selectedProductions);

      // Get customer_id from first production record if not in contract
      const customerId = contract.customer_id || selectedProductions[0]?.customer_id;
      if (!customerId) {
        throw new Error('Không tìm thấy thông tin khách hàng');
      }

      // Generate invoice number
      const contractCode = contract.contract_number || contract.contract_code || 'UNKNOWN';
      const invoiceNumber = `INV-${contractCode}-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;

      // Calculate transaction and due dates
      const transactionDate = dateRange.endDate || new Date();
      const dueDate = new Date(transactionDate);
      dueDate.setDate(dueDate.getDate() + (contract.payment_terms || 30));

      // Create description from production data
      const startDateStr = dateRange.startDate ? dateRange.startDate.toLocaleDateString('vi-VN') : '';
      const endDateStr = dateRange.endDate ? dateRange.endDate.toLocaleDateString('vi-VN') : '';
      const dateRangeStr = startDateStr && endDateStr ? ` từ ${startDateStr} đến ${endDateStr}` : '';
      const description = `Sản lượng hợp đồng ${contractCode}${dateRangeStr} (${selectedProductions.length} bản ghi)`;

      // Prepare receivable data
      const receivableData = {
        customer_id: customerId,
        contract_id: contract.id,
        invoice_number: invoiceNumber,
        transaction_date: transactionDate.toISOString().split('T')[0],
        due_date: dueDate.toISOString().split('T')[0],
        original_amount: totalAmount,
        description: description,
        production_ids: selectedProductions.map((p: any) => p.production_id)
      };

      console.log('Receivable data to send:', receivableData);

      // Call API to create receivable from production
      const response = await receivableService.createFromProduction(receivableData);

      onReceivableCreated(response);
      setProductionDialogOpen(false);

    } catch (error: any) {
      console.error('Error creating receivable from production:', error);
      onError(error.message || 'Không thể tạo công nợ từ sản lượng');
    }
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: 'invoice_number',
      headerName: 'Số hóa đơn',
      width: 130,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {params.value}
        </Typography>
      )
    },
    {
      field: 'customer_name',
      headerName: 'Khách hàng',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">{params.value}</Typography>
          {params.row.customer_tax_code && (
            <Typography variant="caption" color="text.secondary">
              MST: {params.row.customer_tax_code}
            </Typography>
          )}
        </Box>
      )
    },
    {
      field: 'contract_number',
      headerName: 'Hợp đồng',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">{params.value}</Typography>
      )
    },
    {
      field: 'transaction_date',
      headerName: 'Ngày GD',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString('vi-VN')}
        </Typography>
      )
    },
    {
      field: 'due_date',
      headerName: 'Ngày đến hạn',
      width: 110,
      renderCell: (params) => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString('vi-VN')}
        </Typography>
      )
    },
    {
      field: 'original_amount',
      headerName: 'Số tiền gốc',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {new Intl.NumberFormat('vi-VN').format(params.value)}
        </Typography>
      )
    },
    {
      field: 'remaining_balance',
      headerName: 'Còn lại',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography 
          variant="body2" 
          sx={{ 
            fontWeight: 500,
            color: params.value > 0 ? 'error.main' : 'success.main'
          }}
        >
          {new Intl.NumberFormat('vi-VN').format(params.value)}
        </Typography>
      )
    },
    {
      field: 'balance_status',
      headerName: 'Trạng thái',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={receivableService.getStatusLabel(params.value)}
          color={receivableService.getStatusColor(params.value)}
          size="small"
          sx={{ fontSize: '0.75rem' }}
        />
      )
    },
    {
      field: 'days_overdue',
      headerName: 'Quá hạn',
      width: 80,
      align: 'center',
      renderCell: (params) => (
        params.value > 0 ? (
          <Chip
            label={`${params.value} ngày`}
            color="error"
            size="small"
            sx={{ fontSize: '0.7rem' }}
          />
        ) : null
      )
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Thao tác',
      width: 120,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<ViewIcon />}
          label="Xem"
          onClick={() => handleView(params.row)}
        />,
        <GridActionsCellItem
          icon={<EditIcon />}
          label="Sửa"
          onClick={() => handleEdit(params.row)}
        />,
        <GridActionsCellItem
          icon={<DeleteIcon />}
          label="Xóa"
          onClick={() => handleDelete(params.row)}
        />
      ]
    }
  ];

  return (
    <Box>
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              label="Tìm kiếm"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Số hóa đơn, khách hàng..."
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              select
              label="Khách hàng"
              value={filters.customerId || ''}
              onChange={(e) => handleFilterChange('customerId', e.target.value ? parseInt(e.target.value) : undefined)}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="">Tất cả</MenuItem>
              {Array.isArray(customers) && customers.map((customer) => (
                <MenuItem key={customer.id} value={customer.id}>
                  {customer.name}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} sm={2}>
            <TextField
              fullWidth
              size="small"
              select
              label="Trạng thái"
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="">Tất cả</MenuItem>
              <MenuItem value="active">Hoạt động</MenuItem>
              <MenuItem value="overdue">Quá hạn</MenuItem>
              <MenuItem value="paid">Đã thanh toán</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreate}
                sx={{ fontSize: '0.85rem', height: 40 }}
              >
                Thêm công nợ
              </Button>
              <Button
                variant="outlined"
                startIcon={<LinkIcon />}
                onClick={handleCreateFromProduction}
                sx={{ fontSize: '0.85rem', height: 40 }}
              >
                Từ sản lượng
              </Button>
              <Tooltip title="Làm mới">
                <IconButton onClick={handleRefresh} sx={{ height: 40, width: 40 }}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={receivables}
          columns={columns}
          loading={loading}
          pagination
          paginationMode="server"
          paginationModel={{
            page: pagination.page,
            pageSize: pagination.pageSize
          }}
          rowCount={pagination.total}
          onPaginationModelChange={(model) => {
            if (model.page !== pagination.page) {
              handlePageChange(model.page);
            }
            if (model.pageSize !== pagination.pageSize) {
              handlePageSizeChange(model.pageSize);
            }
          }}
          pageSizeOptions={[10, 25, 50, 100]}
          disableRowSelectionOnClick
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.85rem'
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontSize: '0.85rem',
              fontWeight: 600
            }
          }}
        />
      </Paper>

      {/* Dialogs */}
      <ReceivableDialog
        open={dialogOpen}
        receivable={selectedReceivable}
        customers={customers}
        contracts={contracts}
        onClose={handleDialogClose}
        onSave={handleReceivableSaved}
        onError={onError}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa"
        message={`Bạn có chắc chắn muốn xóa công nợ "${receivableToDelete?.invoice_number}"?`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
      />

      <ProductionSelectionDialog
        open={productionDialogOpen}
        onClose={() => setProductionDialogOpen(false)}
        onCreateReceivable={handleCreateFromProductionData}
        onError={onError}
      />
    </Box>
  );
};

export default ReceivablesList;
