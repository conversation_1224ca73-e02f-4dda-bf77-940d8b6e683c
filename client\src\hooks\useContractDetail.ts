/**
 * useContractDetail Hook
 * Hook tổng hợp logic cho contract detail page
 */

import { useState, useEffect, useCallback } from 'react';
import { contractService } from '../services/contractService';
import { Contract, ContractDetail } from '../types/contract';
import { usePrices } from './usePrices';
import { useProduction } from './useProduction';

interface UseContractDetailOptions {
  contractId?: number;
  autoLoad?: boolean;
}

interface ContractStats {
  totalPrices: number;
  activePrices: number;
  totalProductions: number;
  totalRevenue: number;
  lastPriceUpdate: string | null;
  lastProduction: string | null;
  averageMonthlyRevenue: number;
  productionGrowth: number;
}

interface UseContractDetailReturn {
  // Data
  contract: Contract | null;
  contractDetail: ContractDetail | null;
  stats: ContractStats;
  
  // State
  loading: boolean;
  error: string | null;
  
  // Pricing
  prices: ReturnType<typeof usePrices>;
  
  // Production
  production: ReturnType<typeof useProduction>;
  
  // Actions
  loadContract: (id: number) => Promise<void>;
  loadContractDetail: (id: number) => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Quick Actions
  quickPriceUpdate: (data: any) => Promise<boolean>;
  quickProductionInput: (data: any) => Promise<boolean>;
  
  // Utilities
  getContractStatus: () => { label: string; color: string; canEdit: boolean };
  calculateContractValue: () => number;
  getContractProgress: () => { completed: number; total: number; percentage: number };
  isContractActive: () => boolean;
  canPerformActions: () => boolean;
}

export const useContractDetail = (options: UseContractDetailOptions = {}): UseContractDetailReturn => {
  const { contractId, autoLoad = true } = options;

  // State
  const [contract, setContract] = useState<Contract | null>(null);
  const [contractDetail, setContractDetail] = useState<ContractDetail | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<ContractStats>({
    totalPrices: 0,
    activePrices: 0,
    totalProductions: 0,
    totalRevenue: 0,
    lastPriceUpdate: null,
    lastProduction: null,
    averageMonthlyRevenue: 0,
    productionGrowth: 0
  });

  // Initialize pricing and production hooks
  const prices = usePrices({ 
    contractId: contractId, 
    autoLoad: false // We'll control loading manually
  });

  const production = useProduction({ 
    contractId: contractId, 
    autoLoad: false // We'll control loading manually
  });

  // Load contract basic info
  const loadContract = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractService.getById(id);
      
      if (response.success) {
        setContract(response.data);
      } else {
        throw new Error('Không thể tải thông tin hợp đồng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error loading contract:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Load contract detail with related data
  const loadContractDetail = useCallback(async (id: number) => {
    setLoading(true);
    setError(null);

    try {
      const response = await contractService.getDetail(id);
      
      if (response.success) {
        setContractDetail(response.data);
        
        // Calculate stats from detail data
        const detail = response.data;
        setStats({
          totalPrices: detail.prices?.length || 0,
          activePrices: detail.prices?.filter(p => p.is_active).length || 0,
          totalProductions: detail.productions?.length || 0,
          totalRevenue: detail.productions?.reduce((sum, p) => sum + p.total_amount, 0) || 0,
          lastPriceUpdate: detail.prices?.[0]?.created_at || null,
          lastProduction: detail.productions?.[0]?.production_date || null,
          averageMonthlyRevenue: detail.average_monthly_revenue || 0,
          productionGrowth: detail.production_growth || 0
        });
      } else {
        throw new Error('Không thể tải chi tiết hợp đồng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Có lỗi xảy ra';
      setError(errorMessage);
      console.error('Error loading contract detail:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh all data
  const refreshAll = useCallback(async () => {
    if (!contractId) return;

    await Promise.all([
      loadContract(contractId),
      loadContractDetail(contractId),
      prices.loadPrices(),
      production.loadProductions(),
      production.loadSummary()
    ]);
  }, [contractId, loadContract, loadContractDetail, prices, production]);

  // Quick price update
  const quickPriceUpdate = useCallback(async (data: any): Promise<boolean> => {
    if (!contractId) return false;

    try {
      // Create multiple price entries
      const promises = data.items.map((item: any) =>
        prices.createPrice({
          contract_id: contractId,
          product_id: item.product_id,
          price: item.price,
          effective_date: data.effective_date,
          expiry_date: data.expiry_date || null,
          is_active: true
        })
      );

      const results = await Promise.all(promises);
      const success = results.every(result => result);

      if (success) {
        // Refresh contract detail to update stats
        await loadContractDetail(contractId);
      }

      return success;
    } catch (err) {
      console.error('Error in quick price update:', err);
      return false;
    }
  }, [contractId, prices, loadContractDetail]);

  // Quick production input
  const quickProductionInput = useCallback(async (data: any): Promise<boolean> => {
    if (!contractId) return false;

    try {
      // Create multiple production entries
      const promises = data.items.map((item: any) =>
        production.createProduction({
          contract_id: contractId,
          product_id: item.product_id,
          production_date: data.production_date,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_amount: item.total_amount,
          notes: item.notes || data.notes,
          status: data.status
        })
      );

      const results = await Promise.all(promises);
      const success = results.every(result => result);

      if (success) {
        // Refresh contract detail to update stats
        await loadContractDetail(contractId);
      }

      return success;
    } catch (err) {
      console.error('Error in quick production input:', err);
      return false;
    }
  }, [contractId, production, loadContractDetail]);

  // Get contract status
  const getContractStatus = useCallback(() => {
    if (!contract) {
      return { label: 'Không xác định', color: 'default', canEdit: false };
    }

    const statusConfig = {
      draft: { label: 'Nháp', color: 'default', canEdit: true },
      active: { label: 'Đang thực hiện', color: 'success', canEdit: true },
      completed: { label: 'Hoàn thành', color: 'info', canEdit: false },
      cancelled: { label: 'Đã hủy', color: 'error', canEdit: false },
      suspended: { label: 'Tạm dừng', color: 'warning', canEdit: true }
    };

    return statusConfig[contract.status as keyof typeof statusConfig] || statusConfig.draft;
  }, [contract]);

  // Calculate contract value
  const calculateContractValue = useCallback((): number => {
    if (!contractDetail?.prices) return 0;

    // Sum up all active prices
    return contractDetail.prices
      .filter(price => price.is_active)
      .reduce((sum, price) => sum + price.price, 0);
  }, [contractDetail]);

  // Get contract progress
  const getContractProgress = useCallback(() => {
    const totalTasks = 4; // Basic setup tasks
    let completed = 0;

    if (contract) completed++;
    if (stats.totalPrices > 0) completed++;
    if (stats.totalProductions > 0) completed++;
    if (contract?.status === 'active') completed++;

    return {
      completed,
      total: totalTasks,
      percentage: (completed / totalTasks) * 100
    };
  }, [contract, stats]);

  // Check if contract is active
  const isContractActive = useCallback((): boolean => {
    return contract?.status === 'active';
  }, [contract]);

  // Check if can perform actions
  const canPerformActions = useCallback((): boolean => {
    const status = getContractStatus();
    return status.canEdit && isContractActive();
  }, [getContractStatus, isContractActive]);

  // Auto load on mount
  useEffect(() => {
    if (autoLoad && contractId) {
      refreshAll();
    }
  }, [autoLoad, contractId, refreshAll]);

  return {
    // Data
    contract,
    contractDetail,
    stats,
    
    // State
    loading: loading || prices.loading || production.loading,
    error: error || prices.error || production.error,
    
    // Pricing
    prices,
    
    // Production
    production,
    
    // Actions
    loadContract,
    loadContractDetail,
    refreshAll,
    
    // Quick Actions
    quickPriceUpdate,
    quickProductionInput,
    
    // Utilities
    getContractStatus,
    calculateContractValue,
    getContractProgress,
    isContractActive,
    canPerformActions,
  };
};

export default useContractDetail;
