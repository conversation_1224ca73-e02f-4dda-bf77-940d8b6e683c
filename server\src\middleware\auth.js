const jwt = require('jsonwebtoken');
const { pool } = require('../db');

/**
 * Middleware xác thực JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Token không được cung cấp',
          details: ['Vui lòng cung cấp token xác thực trong header Authorization']
        }
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Kiểm tra user còn tồn tại và active
    const userQuery = `
      SELECT id, name, email, position, is_active, last_login
      FROM users 
      WHERE id = $1 AND is_active = true
    `;
    
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length === 0) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Token không hợp lệ',
          details: ['Người dùng không tồn tại hoặc đã bị vô hiệu hóa']
        }
      });
    }

    // Thêm thông tin user vào request
    req.user = userResult.rows[0];
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Token không hợp lệ',
          details: ['Token đã bị thay đổi hoặc không đúng định dạng']
        }
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        error: {
          code: 'TOKEN_EXPIRED',
          message: 'Token đã hết hạn',
          details: ['Vui lòng đăng nhập lại để lấy token mới']
        }
      });
    }

    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Lỗi xác thực',
        details: ['Có lỗi xảy ra trong quá trình xác thực']
      }
    });
  }
};

/**
 * Middleware tùy chọn xác thực (không bắt buộc phải có token)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    const userQuery = `
      SELECT id, name, email, position, is_active, last_login
      FROM users 
      WHERE id = $1 AND is_active = true
    `;
    
    const userResult = await pool.query(userQuery, [decoded.userId]);
    
    if (userResult.rows.length > 0) {
      req.user = userResult.rows[0];
    } else {
      req.user = null;
    }
    
    next();
  } catch (error) {
    // Nếu có lỗi, vẫn cho phép request tiếp tục nhưng không có user
    req.user = null;
    next();
  }
};

/**
 * Tạo JWT token
 * @param {Object} user - Thông tin user
 * @returns {string} JWT token
 */
const generateToken = (user) => {
  const payload = {
    userId: user.id,
    email: user.email,
    name: user.name
  };

  const options = {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    issuer: 'customer-management-app'
  };

  return jwt.sign(payload, process.env.JWT_SECRET, options);
};

/**
 * Tạo refresh token
 * @param {Object} user - Thông tin user
 * @returns {string} Refresh token
 */
const generateRefreshToken = (user) => {
  const payload = {
    userId: user.id,
    type: 'refresh'
  };

  const options = {
    expiresIn: '7d', // Refresh token có thời hạn dài hơn
    issuer: 'customer-management-app'
  };

  return jwt.sign(payload, process.env.JWT_SECRET, options);
};

/**
 * Verify refresh token
 * @param {string} token - Refresh token
 * @returns {Object} Decoded token payload
 */
const verifyRefreshToken = (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid token type');
    }
    
    return decoded;
  } catch (error) {
    throw error;
  }
};

module.exports = {
  authenticateToken,
  optionalAuth,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken
};
