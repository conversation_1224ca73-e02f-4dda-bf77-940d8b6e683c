const productModel = require('../models/productModel');
const {
  createResponse,
  createListResponse,
  processQueryParams,
  validateRequiredFields,
  sanitizeString
} = require('../utils/responseUtils');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * <PERSON><PERSON>y tất cả sản phẩm
 * @route GET /api/v1/products
 * @access Private
 */
const getAllProducts = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);

  const options = {
    page: pagination.page,
    limit: pagination.limit,
    search: filters.search,
    unit_type: filters.unit_type,
    is_active: filters.is_active !== undefined ? filters.is_active === 'true' : true,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await productModel.getAllProducts(options);

  const response = createListResponse(
    result.products,
    result.pagination,
    req
  );

  res.status(200).json(response);
});

/**
 * <PERSON><PERSON><PERSON> sản phẩm theo ID
 * @route GET /api/v1/products/:id
 * @access Private
 */
const getProductById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản phẩm không hợp lệ', ['ID phải là một số nguyên']);
  }

  const product = await productModel.getProductById(parseInt(id));

  if (!product) {
    throw new NotFoundError('Không tìm thấy sản phẩm', [`Không tìm thấy sản phẩm với ID ${id}`]);
  }

  res.status(200).json(createResponse(product));
});

/**
 * Tạo sản phẩm mới
 * @route POST /api/v1/products
 * @access Private (Manager+)
 */
const createProduct = asyncHandler(async (req, res) => {
  const {
    name,
    description,
    unit_type
  } = req.body;

  // Validate required fields (code không còn required vì sẽ tự động generate)
  const requiredFields = ['name', 'unit_type'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate unit_type
  const allowedUnitTypes = ['piece', 'kg', 'set', 'meter', 'liter', 'box'];
  if (unit_type && !allowedUnitTypes.includes(unit_type)) {
    validationErrors.push({
      field: 'unit_type',
      message: `Loại đơn vị phải là một trong: ${allowedUnitTypes.join(', ')}`
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Tự động generate mã sản phẩm
  const autoGeneratedCode = await productModel.getNextProductCode();

  // Sanitize input data
  const productData = {
    code: autoGeneratedCode,
    name: sanitizeString(name),
    description: sanitizeString(description),
    unit_type: sanitizeString(unit_type)
  };

  // Tạm thời sử dụng user ID mặc định khi authentication bị tắt
  const userId = req.user ? req.user.id : 1; // Default to admin user
  const newProduct = await productModel.createProduct(productData, userId);

  res.status(201).json(createResponse(newProduct, 'Tạo sản phẩm thành công'));
});

/**
 * Cập nhật sản phẩm
 * @route PUT /api/v1/products/:id
 * @access Private (Manager+)
 */
const updateProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    name,
    description,
    unit_type,
    is_active
  } = req.body;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản phẩm không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Validate required fields
  const requiredFields = ['name', 'unit_type'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate unit_type
  const allowedUnitTypes = ['piece', 'kg', 'set', 'meter', 'liter', 'box'];
  if (unit_type && !allowedUnitTypes.includes(unit_type)) {
    validationErrors.push({
      field: 'unit_type',
      message: `Loại đơn vị phải là một trong: ${allowedUnitTypes.join(', ')}`
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra sản phẩm tồn tại
  const existingProduct = await productModel.getProductById(parseInt(id));
  if (!existingProduct) {
    throw new NotFoundError('Không tìm thấy sản phẩm', [`Không tìm thấy sản phẩm với ID ${id}`]);
  }

  // Sanitize input data
  const productData = {
    name: sanitizeString(name),
    description: sanitizeString(description),
    unit_type: sanitizeString(unit_type),
    is_active: is_active !== undefined ? is_active : true
  };

  const updatedProduct = await productModel.updateProduct(parseInt(id), productData);

  if (!updatedProduct) {
    throw new NotFoundError('Không thể cập nhật sản phẩm', ['Sản phẩm có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(updatedProduct, 'Cập nhật sản phẩm thành công'));
});

/**
 * Xóa sản phẩm (soft delete)
 * @route DELETE /api/v1/products/:id
 * @access Private (Admin only)
 */
const deleteProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID sản phẩm không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Kiểm tra sản phẩm tồn tại
  const existingProduct = await productModel.getProductById(parseInt(id));
  if (!existingProduct) {
    throw new NotFoundError('Không tìm thấy sản phẩm', [`Không tìm thấy sản phẩm với ID ${id}`]);
  }

  const deleted = await productModel.deleteProduct(parseInt(id));

  if (!deleted) {
    throw new NotFoundError('Không thể xóa sản phẩm', ['Sản phẩm có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(
    { message: 'Xóa sản phẩm thành công', id: parseInt(id) }
  ));
});

/**
 * Lấy danh sách unit types
 * @route GET /api/v1/products/unit-types
 * @access Private
 */
const getUnitTypes = asyncHandler(async (req, res) => {
  const unitTypes = await productModel.getUnitTypes();

  res.status(200).json(createResponse(unitTypes));
});

/**
 * Tìm kiếm sản phẩm
 * @route GET /api/v1/products/search
 * @access Private
 */
const searchProducts = asyncHandler(async (req, res) => {
  const { q, limit = 10 } = req.query;

  if (!q || q.trim().length < 2) {
    throw new ValidationError('Từ khóa tìm kiếm phải có ít nhất 2 ký tự');
  }

  const options = {
    page: 1,
    limit: parseInt(limit),
    search: q.trim(),
    is_active: true
  };

  const result = await productModel.getAllProducts(options);

  res.status(200).json(createResponse(result.products));
});

/**
 * Lấy mã sản phẩm tiếp theo
 * @route GET /api/v1/products/next-code
 * @access Private
 */
const getNextProductCode = asyncHandler(async (req, res) => {
  const nextCode = await productModel.getNextProductCode();

  res.status(200).json(createResponse({
    nextCode,
    preview: `Mã sản phẩm tiếp theo sẽ là: ${nextCode}`
  }));
});

/**
 * Kiểm tra tên sản phẩm đã tồn tại
 * @route GET /api/v1/products/check-name
 * @access Private
 */
const checkProductName = asyncHandler(async (req, res) => {
  const { name, excludeId } = req.query;

  if (!name || name.trim().length < 2) {
    throw new ValidationError('Tên sản phẩm phải có ít nhất 2 ký tự');
  }

  const result = await productModel.checkNameExists(
    name.trim(),
    excludeId ? parseInt(excludeId) : null
  );

  res.status(200).json(createResponse(result));
});

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getUnitTypes,
  searchProducts,
  getNextProductCode,
  checkProductName
};
