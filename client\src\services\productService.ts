/**
 * Product Service
 * API service for product management
 */

import { get, post, put, del, patch } from './api';
import {
  Product,
  ProductFilterOptions,
} from '../types/product';

/**
 * Product API Service
 */
export const productService = {
  /**
   * L<PERSON>y tất cả sản phẩm với filter và pagination
   */
  getAll: async (options?: ProductFilterOptions) => {
    const params = new URLSearchParams();

    if (options?.page) params.append('page', options.page.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.search) params.append('search', options.search);
    if (options?.unit_type) params.append('unit_type', options.unit_type);
    if (options?.is_active !== undefined) params.append('is_active', options.is_active.toString());
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const url = queryString ? `/products?${queryString}` : '/products';

    return get<Product[]>(url);
  },

  /**
   * Lấy sản phẩm theo ID
   */
  getById: async (id: number) => {
    return get<Product>(`/products/${id}`);
  },

  /**
   * Tạo sản phẩm mới
   */
  create: async (data: any) => {
    return post<Product>('/products', data);
  },

  /**
   * Cập nhật sản phẩm
   */
  update: async (id: number, data: any) => {
    return put<Product>(`/products/${id}`, data);
  },

  /**
   * Xóa sản phẩm
   */
  delete: async (id: number) => {
    return del<{ message: string; id: number }>(`/products/${id}`);
  },

  /**
   * Tìm kiếm sản phẩm
   */
  search: async (query: string, limit: number = 10) => {
    const params = new URLSearchParams({
      q: query,
      limit: limit.toString(),
    });

    return get<Product[]>(`/products/search?${params.toString()}`);
  },

  /**
   * Lấy danh sách đơn vị tính có sẵn
   */
  getUnitTypes: async () => {
    return get<string[]>('/products/unit-types');
  },

  /**
   * Lấy mã sản phẩm tiếp theo
   */
  getNextCode: async () => {
    return get<{ nextCode: string; preview: string }>('/products/next-code');
  },

  /**
   * Kiểm tra tên sản phẩm đã tồn tại
   */
  checkName: async (name: string, excludeId?: number) => {
    const params = new URLSearchParams({ name });
    if (excludeId) {
      params.append('excludeId', excludeId.toString());
    }

    return get<{
      exists: boolean;
      exactMatch: { id: number; code: string; name: string } | null;
      suggestions: Array<{ id: number; code: string; name: string; similarity: number }>;
    }>(`/products/check-name?${params.toString()}`);
  },
};

export default productService;
