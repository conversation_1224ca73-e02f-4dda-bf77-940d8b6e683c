/**
 * Production Status Chip Component
 * <PERSON><PERSON><PERSON> thị trạng thái sản lượng với màu sắc phù hợp
 */

import React from 'react';
import { Chip, Tooltip, Box } from '@mui/material';
import { 
  DraftsOutlined as DraftIcon,
  CheckCircleOutlined as ConfirmedIcon,
  ReceiptOutlined as InvoicedIcon
} from '@mui/icons-material';
import { ProductionStatus, PRODUCTION_STATUS_CONFIG } from '../../types/dailyProduction';

interface ProductionStatusChipProps {
  status: ProductionStatus;
  size?: 'small' | 'medium';
  showIcon?: boolean;
  showTooltip?: boolean;
  variant?: 'filled' | 'outlined';
}

const ProductionStatusChip: React.FC<ProductionStatusChipProps> = ({
  status,
  size = 'small',
  showIcon = true,
  showTooltip = true,
  variant = 'filled'
}) => {
  const config = PRODUCTION_STATUS_CONFIG[status];
  
  if (!config) {
    return (
      <Chip
        label="Không xác đ<PERSON>nh"
        size={size}
        color="default"
        variant={variant}
      />
    );
  }

  const getIcon = () => {
    if (!showIcon) return undefined;
    
    const iconProps = { 
      fontSize: size === 'small' ? 'small' : 'medium' as const,
      sx: { fontSize: '0.85rem' }
    };
    
    switch (status) {
      case 'Mới tạo':
        return <DraftIcon {...iconProps} />;
      case 'Đã xác nhận':
        return <ConfirmedIcon {...iconProps} />;
      case 'Đã ghi nhận công nợ':
        return <InvoicedIcon {...iconProps} />;
      default:
        return undefined;
    }
  };

  const chipElement = (
    <Chip
      icon={getIcon()}
      label={config.label}
      size={size}
      color={config.color}
      variant={variant}
      sx={{
        fontSize: '0.85rem',
        fontWeight: 500,
        backgroundColor: variant === 'filled' ? config.bgColor : 'transparent',
        color: config.textColor,
        border: variant === 'outlined' ? `1px solid ${config.textColor}` : 'none',
        '& .MuiChip-icon': {
          color: config.textColor,
          fontSize: '0.85rem'
        },
        '& .MuiChip-label': {
          fontSize: '0.85rem',
          paddingLeft: showIcon ? '4px' : '12px',
          paddingRight: '12px'
        }
      }}
    />
  );

  if (!showTooltip) {
    return chipElement;
  }

  const getTooltipContent = () => {
    switch (status) {
      case 'Mới tạo':
        return 'Sản lượng vừa được tạo, chưa được xác nhận';
      case 'Đã xác nhận':
        return 'Sản lượng đã được xác nhận, có thể tạo công nợ';
      case 'Đã ghi nhận công nợ':
        return 'Sản lượng đã được ghi nhận vào công nợ phải thu';
      default:
        return config.label;
    }
  };

  return (
    <Tooltip title={getTooltipContent()} arrow placement="top">
      <Box component="span">
        {chipElement}
      </Box>
    </Tooltip>
  );
};

export default ProductionStatusChip;
