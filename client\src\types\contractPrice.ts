/**
 * Contract Price Type Definitions
 */

/**
 * Contract Price Interface
 */
export interface ContractPrice {
  id: number;
  contract_id: number;
  product_id: number;
  price: number;
  effective_date: string;
  expiry_date?: string | null;
  is_active: boolean;
  notes?: string | null;
  created_at: string;
  updated_at: string;
  created_by_name?: string;
  // Joined fields
  contract_number?: string;
  contract_name?: string;
  product_code?: string;
  product_name?: string;
  product_unit_type?: string;
}

/**
 * Contract Price Create Request Interface
 */
export interface ContractPriceCreateRequest {
  contract_id: number;
  product_id: number;
  price: number;
  effective_date: string;
  expiry_date?: string;
  notes?: string;
}

/**
 * Contract Price Update Request Interface
 */
export interface ContractPriceUpdateRequest {
  price: number;
  effective_date: string;
  expiry_date?: string;
  is_active?: boolean;
  notes?: string;
}

/**
 * Set Price Request Interface (with auto deactivation)
 */
export interface SetPriceRequest {
  contract_id: number;
  product_id: number;
  price: number;
  effective_date: string;
  expiry_date?: string;
  notes?: string;
  auto_deactivate_old?: boolean;
}

/**
 * Contract Price Search/Filter Options Interface
 */
export interface ContractPriceFilterOptions {
  page?: number;
  limit?: number;
  contract_id?: number;
  product_id?: number;
  is_active?: boolean;
  effective_date_from?: string;
  effective_date_to?: string;
  sortBy?: ContractPriceSortField;
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Contract Price Sort Fields
 */
export type ContractPriceSortField =
  | 'id'
  | 'price'
  | 'effective_date'
  | 'expiry_date'
  | 'is_active'
  | 'created_at'
  | 'updated_at';

/**
 * Contract Price Sort Options for UI
 */
export const CONTRACT_PRICE_SORT_OPTIONS: Array<{
  value: ContractPriceSortField;
  label: string;
}> = [
  { value: 'effective_date', label: 'Ngày hiệu lực' },
  { value: 'expiry_date', label: 'Ngày hết hạn' },
  { value: 'price', label: 'Đơn giá' },
  { value: 'is_active', label: 'Trạng thái' },
  { value: 'created_at', label: 'Ngày tạo' },
];

/**
 * Contract Price Form Data Interface (for forms)
 */
export interface ContractPriceFormData {
  contract_id: number;
  product_id: number;
  price: number;
  effective_date: string;
  expiry_date?: string;
  notes?: string;
}

/**
 * Contract Price Validation Error Interface
 */
export interface ContractPriceValidationError {
  field: keyof ContractPriceFormData;
  message: string;
}

/**
 * Contract Price Validation Result Interface
 */
export interface ContractPriceValidationResult {
  isValid: boolean;
  errors: ContractPriceValidationError[];
}

/**
 * Contract Price Table Column Interface
 */
export interface ContractPriceTableColumn {
  id: keyof ContractPrice | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

/**
 * Contract Price Table Columns Configuration
 */
export const CONTRACT_PRICE_TABLE_COLUMNS: ContractPriceTableColumn[] = [
  {
    id: 'contract_number',
    label: 'Số hợp đồng',
    minWidth: 140,
    sortable: false,
  },
  {
    id: 'product_name',
    label: 'Sản phẩm',
    minWidth: 180,
    sortable: false,
  },
  {
    id: 'price',
    label: 'Đơn giá',
    minWidth: 120,
    align: 'right',
    sortable: true,
    format: (value: number) => new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value),
  },
  {
    id: 'effective_date',
    label: 'Ngày hiệu lực',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'expiry_date',
    label: 'Ngày hết hạn',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string | null) =>
      value ? new Date(value).toLocaleDateString('vi-VN') : 'Không giới hạn',
  },
  {
    id: 'is_active',
    label: 'Trạng thái',
    minWidth: 100,
    align: 'center',
    sortable: true,
  },
  {
    id: 'created_at',
    label: 'Ngày tạo',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'actions',
    label: 'Thao tác',
    minWidth: 120,
    align: 'center',
    sortable: false,
  },
];

/**
 * Current Price Response Interface
 */
export interface CurrentPriceResponse {
  id: number;
  contract_id: number;
  product_id: number;
  price: number;
  effective_date: string;
  expiry_date?: string | null;
  is_active: boolean;
  notes?: string | null;
}

/**
 * Price History Item Interface
 */
export interface PriceHistoryItem {
  id: number;
  price: number;
  effective_date: string;
  expiry_date?: string | null;
  is_active: boolean;
  notes?: string | null;
  created_at: string;
  created_by_name?: string;
}

/**
 * Contract Price Summary Interface
 */
export interface ContractPriceSummary {
  total_prices: number;
  active_prices: number;
  inactive_prices: number;
  contracts_with_prices: number;
  products_with_prices: number;
  average_price: number;
  min_price: number;
  max_price: number;
}

/**
 * Price Conflict Check Request Interface
 */
export interface PriceConflictCheckRequest {
  contract_id: number;
  product_id: number;
  effective_date: string;
  expiry_date?: string;
  exclude_id?: number;
}

/**
 * Price Conflict Check Response Interface
 */
export interface PriceConflictCheckResponse {
  has_conflict: boolean;
  conflicting_prices?: ContractPrice[];
  message?: string;
}

/**
 * Contract Price Error Types
 */
export type ContractPriceErrorType =
  | 'PRICE_NOT_FOUND'
  | 'PRICE_CONFLICT'
  | 'CONTRACT_NOT_FOUND'
  | 'PRODUCT_NOT_FOUND'
  | 'INVALID_DATE_RANGE'
  | 'INVALID_PRICE'
  | 'VALIDATION_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Contract Price Error Interface
 */
export interface ContractPriceError {
  type: ContractPriceErrorType;
  message: string;
  details?: string[];
}

/**
 * Contract Price Action Types for State Management
 */
export type ContractPriceActionType =
  | 'FETCH_PRICES_START'
  | 'FETCH_PRICES_SUCCESS'
  | 'FETCH_PRICES_FAILURE'
  | 'FETCH_CURRENT_PRICE_START'
  | 'FETCH_CURRENT_PRICE_SUCCESS'
  | 'FETCH_CURRENT_PRICE_FAILURE'
  | 'FETCH_PRICE_HISTORY_START'
  | 'FETCH_PRICE_HISTORY_SUCCESS'
  | 'FETCH_PRICE_HISTORY_FAILURE'
  | 'CREATE_PRICE_START'
  | 'CREATE_PRICE_SUCCESS'
  | 'CREATE_PRICE_FAILURE'
  | 'UPDATE_PRICE_START'
  | 'UPDATE_PRICE_SUCCESS'
  | 'UPDATE_PRICE_FAILURE'
  | 'DELETE_PRICE_START'
  | 'DELETE_PRICE_SUCCESS'
  | 'DELETE_PRICE_FAILURE'
  | 'SET_PRICE_START'
  | 'SET_PRICE_SUCCESS'
  | 'SET_PRICE_FAILURE'
  | 'SET_SELECTED_PRICE'
  | 'CLEAR_SELECTED_PRICE'
  | 'CLEAR_ERROR';

/**
 * Contract Price State Interface for Context/Redux
 */
export interface ContractPriceState {
  prices: ContractPrice[];
  selectedPrice: ContractPrice | null;
  currentPrice: CurrentPriceResponse | null;
  priceHistory: PriceHistoryItem[];
  loading: boolean;
  error: ContractPriceError | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  summary: ContractPriceSummary | null;
}

/**
 * Contract Price Context Type
 */
export interface ContractPriceContextType {
  state: ContractPriceState;
  fetchPrices: (options?: ContractPriceFilterOptions) => Promise<void>;
  fetchCurrentPrice: (contractId: number, productId: number, date?: string) => Promise<void>;
  fetchPriceHistory: (contractId: number, productId: number) => Promise<void>;
  createPrice: (data: ContractPriceCreateRequest) => Promise<ContractPrice>;
  updatePrice: (id: number, data: ContractPriceUpdateRequest) => Promise<ContractPrice>;
  deletePrice: (id: number) => Promise<void>;
  setPrice: (data: SetPriceRequest) => Promise<ContractPrice>;
  setSelectedPrice: (price: ContractPrice | null) => void;
  clearError: () => void;
}

/**
 * Contract Price Hook Return Type
 */
export interface UseContractPriceReturn extends ContractPriceContextType {
  // Additional computed properties
  activePrices: ContractPrice[];
  inactivePrices: ContractPrice[];
  pricesExpiringSoon: ContractPrice[];
}

/**
 * Contract Price Export Data Interface
 */
export interface ContractPriceExportData {
  contract_number: string;
  contract_name: string;
  product_code: string;
  product_name: string;
  price: string;
  effective_date: string;
  expiry_date: string;
  status: string;
  notes: string;
  created_date: string;
}

/**
 * Bulk Price Update Request Interface
 */
export interface BulkPriceUpdateRequest {
  contract_id: number;
  effective_date: string;
  price_updates: Array<{
    product_id: number;
    price: number;
    notes?: string;
  }>;
  auto_deactivate_old?: boolean;
}
