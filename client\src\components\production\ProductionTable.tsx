/**
 * ProductionTable Component
 * Bảng hiển thị danh sách sản lượng với pagination, filter, search
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Box,
  Typography,
  TablePagination,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Toolbar,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  TableSortLabel,
  Skeleton
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  Print as PrintIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  FileDownload as ExportIcon,
  Add as AddIcon,
  Speed as QuickAddIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';

// Types
import { DailyProduction, DailyProductionFilterOptions, ProductionStatus } from '../../types/dailyProduction';
import ProductionStatusChip from './ProductionStatusChip';

// Utils
const visuallyHidden = {
  border: 0,
  clip: 'rect(0 0 0 0)',
  height: 1,
  margin: -1,
  overflow: 'hidden',
  padding: 0,
  position: 'absolute',
  top: 20,
  width: 1,
};

interface ProductionTableProps {
  productions: DailyProduction[];
  loading?: boolean;
  totalCount?: number;
  page?: number;
  rowsPerPage?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
  searchQuery?: string;
  filters?: DailyProductionFilterOptions;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onSort?: (orderBy: string, order: 'asc' | 'desc') => void;
  onSearch?: (query: string) => void;
  onFilter?: (filters: DailyProductionFilterOptions) => void;
  onAdd?: () => void;
  onEdit?: (production: DailyProduction) => void;
  onDelete?: (production: DailyProduction) => void;
  onView?: (production: DailyProduction) => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onQuickAdd?: () => void;
  onPrint?: (production: DailyProduction) => void;
}

interface HeadCell {
  id: keyof DailyProduction;
  label: string;
  numeric: boolean;
  sortable: boolean;
  width?: number;
}

const headCells: HeadCell[] = [
  { id: 'production_date', label: 'Ngày sản xuất', numeric: false, sortable: true, width: 120 },
  { id: 'customer_name', label: 'Khách hàng', numeric: false, sortable: false, width: 150 },
  { id: 'contract_number', label: 'Hợp đồng', numeric: false, sortable: false, width: 120 },
  { id: 'product_name', label: 'Sản phẩm', numeric: false, sortable: false, width: 150 },
  { id: 'quantity', label: 'Số lượng', numeric: true, sortable: true, width: 100 },
  { id: 'unit_price', label: 'Đơn giá', numeric: true, sortable: true, width: 120 },
  { id: 'total_amount', label: 'Thành tiền', numeric: true, sortable: true, width: 120 },
  { id: 'status', label: 'Trạng thái', numeric: false, sortable: true, width: 100 },
];

const ProductionTable: React.FC<ProductionTableProps> = ({
  productions,
  loading = false,
  totalCount = 0,
  page = 0,
  rowsPerPage = 10,
  orderBy = 'production_date',
  order = 'desc',
  searchQuery = '',
  filters = {},
  onPageChange,
  onRowsPerPageChange,
  onSort,
  onSearch,
  onFilter,
  onAdd,
  onEdit,
  onDelete,
  onView,
  onRefresh,
  onExport,
  onQuickAdd,
  onPrint
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProduction, setSelectedProduction] = useState<DailyProduction | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearchQuery !== searchQuery) {
        onSearch?.(localSearchQuery);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localSearchQuery, searchQuery, onSearch]);

  const handleRequestSort = useCallback((property: keyof DailyProduction) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    onSort?.(property as string, newOrder);
  }, [orderBy, order, onSort]);

  const handleChangePage = useCallback((_: unknown, newPage: number) => {
    onPageChange?.(newPage);
  }, [onPageChange]);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange?.(parseInt(event.target.value, 10));
  }, [onRowsPerPageChange]);

  const handleMenuClick = useCallback((event: React.MouseEvent<HTMLElement>, production: DailyProduction) => {
    setAnchorEl(event.currentTarget);
    setSelectedProduction(production);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedProduction(null);
  }, []);

  const handleStatusFilterChange = useCallback((status: string) => {
    setStatusFilter(status);
    onFilter?.({ ...filters, status: status || undefined });
  }, [filters, onFilter]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return format(new Date(dateString), 'dd/MM/yyyy', { locale: vi });
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const getStatusChip = (status: ProductionStatus) => {
    return <ProductionStatusChip status={status} size="small" />;
  };

  const renderSkeletonRows = () => {
    return Array.from(new Array(rowsPerPage)).map((_, index) => (
      <TableRow key={index}>
        {headCells.map((headCell) => (
          <TableCell key={headCell.id}>
            <Skeleton variant="text" width="80%" />
          </TableCell>
        ))}
        {onQuickAdd && (
          <TableCell>
            <Skeleton variant="circular" width={24} height={24} />
          </TableCell>
        )}
        <TableCell>
          <Skeleton variant="circular" width={24} height={24} />
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <Paper sx={{ width: '100%', mb: 2 }}>
      <Toolbar
        sx={{
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
          minHeight: { xs: 56, sm: 64 },
        }}
      >
        <Typography
          sx={{ flex: '1 1 100%' }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          Danh sách sản lượng
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            size="small"
            placeholder="Tìm kiếm sản lượng..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ width: 250 }}
          />

          <FormControl size="small" sx={{ minWidth: 140 }}>
            <InputLabel>Trạng thái</InputLabel>
            <Select
              value={statusFilter}
              label="Trạng thái"
              onChange={(e) => handleStatusFilterChange(e.target.value as string)}
            >
              <MenuItem value="">Tất cả</MenuItem>
              <MenuItem value="Mới tạo">Mới tạo</MenuItem>
              <MenuItem value="Đã xác nhận">Đã xác nhận</MenuItem>
              <MenuItem value="Đã ghi nhận công nợ">Đã ghi nhận công nợ</MenuItem>
            </Select>
          </FormControl>

          <Tooltip title="Làm mới">
            <IconButton onClick={onRefresh} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xuất Excel">
            <IconButton onClick={onExport} size="small">
              <ExportIcon />
            </IconButton>
          </Tooltip>

          {onQuickAdd && (
            <Tooltip title="Thêm nhanh">
              <IconButton onClick={onQuickAdd} size="small" color="success">
                <QuickAddIcon />
              </IconButton>
            </Tooltip>
          )}

          <Tooltip title="Thêm sản lượng">
            <IconButton onClick={onAdd} size="small" color="primary">
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>

      <TableContainer>
        <Table
          sx={{ minWidth: 750 }}
          aria-labelledby="tableTitle"
          size="small"
        >
          <TableHead>
            <TableRow>
              {headCells.map((headCell) => (
                <TableCell
                  key={headCell.id}
                  align={headCell.numeric ? 'right' : 'left'}
                  padding="normal"
                  sortDirection={orderBy === headCell.id ? order : false}
                  sx={{ width: headCell.width }}
                >
                  {headCell.sortable ? (
                    <TableSortLabel
                      active={orderBy === headCell.id}
                      direction={orderBy === headCell.id ? order : 'asc'}
                      onClick={() => handleRequestSort(headCell.id)}
                    >
                      {headCell.label}
                      {orderBy === headCell.id ? (
                        <Box component="span" sx={visuallyHidden}>
                          {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      ) : null}
                    </TableSortLabel>
                  ) : (
                    headCell.label
                  )}
                </TableCell>
              ))}
              {onQuickAdd && (
                <TableCell align="center" sx={{ width: 80 }}>
                  Nhanh
                </TableCell>
              )}
              <TableCell align="center" sx={{ width: 80 }}>
                Thao tác
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              renderSkeletonRows()
            ) : productions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={headCells.length + (onQuickAdd ? 2 : 1)} align="center" sx={{ py: 3 }}>
                  <Typography variant="body2" color="textSecondary">
                    Không có dữ liệu sản lượng
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              productions.map((production) => (
                <TableRow
                  hover
                  key={production.id}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => onView?.(production)}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      {formatDate(production.production_date)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {production.customer_name || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {production.contract_number || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {production.product_name}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {production.quantity.toLocaleString('vi-VN')} {production.unit || ''}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {formatCurrency(production.unit_price)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight={500}>
                      {formatCurrency(production.total_amount)}
                    </Typography>
                  </TableCell>
                  <TableCell>{getStatusChip(production.status as ProductionStatus)}</TableCell>
                  {onQuickAdd && (
                    <TableCell align="center">
                      <Tooltip title="Thêm nhanh từ sản lượng này">
                        <IconButton
                          size="small"
                          color="success"
                          onClick={(e) => {
                            e.stopPropagation();
                            onQuickAdd();
                          }}
                        >
                          <QuickAddIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  )}
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuClick(e, production);
                      }}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Số dòng mỗi trang:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}–${to} của ${count !== -1 ? count : `hơn ${to}`}`
        }
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { onView?.(selectedProduction!); handleMenuClose(); }}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xem chi tiết</ListItemText>
        </MenuItem>

        <MenuItem onClick={() => { onEdit?.(selectedProduction!); handleMenuClose(); }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Chỉnh sửa</ListItemText>
        </MenuItem>

        {onPrint && (
          <MenuItem onClick={() => { onPrint(selectedProduction!); handleMenuClose(); }}>
            <ListItemIcon>
              <PrintIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>In phiếu</ListItemText>
          </MenuItem>
        )}

        <MenuItem onClick={() => { onDelete?.(selectedProduction!); handleMenuClose(); }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xóa</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ProductionTable;
