import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// import { useAuth } from '../../contexts/AuthContext';

/**
 * Permission Interface
 */
interface Permission {
  resourceType: string;
  action: string;
}

/**
 * Protected Route Props
 */
interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermission?: Permission;
  fallbackPath?: string;
}

/**
 * Loading Component
 */
const LoadingComponent: React.FC = () => (
  <Box
    display="flex"
    flexDirection="column"
    justifyContent="center"
    alignItems="center"
    minHeight="100vh"
    bgcolor="background.default"
  >
    <CircularProgress size={40} thickness={4} />
    <Typography
      variant="body2"
      color="text.secondary"
      sx={{ mt: 2 }}
    >
      <PERSON><PERSON> tải...
    </Typography>
  </Box>
);

/**
 * Check if user has required permission
 */
const hasPermission = (user: any, permission?: Permission): boolean => {
  if (!permission) {
    return true; // No specific permission required
  }

  if (!user) {
    return false;
  }

  // Simple permission check - can be extended based on your permission system
  // For now, we'll allow all authenticated users to access all resources
  // In a real application, you would check user roles/permissions here

  const { resourceType, action } = permission;

  // Admin has all permissions
  if (user.position && user.position.toLowerCase().includes('admin')) {
    return true;
  }

  // Basic permissions for regular users
  const basicPermissions = {
    customer: ['view', 'create', 'edit'],
    user: ['view']
  };

  if (basicPermissions[resourceType as keyof typeof basicPermissions]) {
    return basicPermissions[resourceType as keyof typeof basicPermissions].includes(action);
  }

  // Default: no permission
  return false;
};

/**
 * Protected Route Component
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 * Hiện tại component này chỉ render children mà không kiểm tra authentication
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission,
  fallbackPath = '/login'
}) => {
  // NOTE: Tạm thời bỏ qua authentication check
  // const { state } = useAuth();
  // const location = useLocation();

  // Mock user for permission check (nếu cần)
  const mockUser = {
    position: 'admin',
    name: 'Admin'
  };

  // Check permissions if required (vẫn giữ logic permission check)
  if (requiredPermission && !hasPermission(mockUser, requiredPermission)) {
    return (
      <Navigate
        to="/forbidden"
        replace
      />
    );
  }

  // Render children without authentication check
  return <>{children}</>;
};

export default ProtectedRoute;
