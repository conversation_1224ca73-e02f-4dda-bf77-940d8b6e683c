/**
 * Common Form Fields Components
 * Reusable form field components with react-hook-form integration
 */

import React from 'react';
import {
  TextField,
  TextFieldProps,
  Typography,
} from '@mui/material';
import { Control, Controller, FieldPath, FieldValues } from 'react-hook-form';

interface FormTextFieldProps<T extends FieldValues> extends Omit<TextFieldProps, 'name' | 'value' | 'onChange'> {
  name: FieldPath<T>;
  control: Control<T>;
  label: string;
  helperText?: string;
}

/**
 * FormTextField Component
 * TextField component integrated with react-hook-form
 */
export function FormTextField<T extends FieldValues>({
  name,
  control,
  label,
  helperText,
  required = false,
  disabled = false,
  multiline = false,
  rows,
  type = 'text',
  size = 'medium',
  ...textFieldProps
}: FormTextFieldProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => (
        <TextField
          {...field}
          {...textFieldProps}
          label={label}
          required={required}
          disabled={disabled}
          multiline={multiline}
          rows={rows}
          type={type}
          size={size}
          fullWidth
          error={!!fieldState.error}
          helperText={fieldState.error?.message || helperText}
          InputProps={{
            sx: {
              fontSize: '0.85rem',
              minHeight: multiline ? 'auto' : '48px',
            },
            ...textFieldProps.InputProps,
          }}
          InputLabelProps={{
            sx: {
              fontSize: '0.85rem',
            },
            ...textFieldProps.InputLabelProps,
          }}
          sx={{
            '& .MuiFormHelperText-root': {
              fontSize: '0.75rem',
            },
            ...textFieldProps.sx,
          }}
        />
      )}
    />
  );
}

export default FormTextField;
