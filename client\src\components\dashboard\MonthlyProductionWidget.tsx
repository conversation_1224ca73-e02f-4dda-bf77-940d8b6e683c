import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Chip,
  useTheme,
  FormControl,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ComposedChart,
  Bar
} from 'recharts';
import { MonthlyProductionStats } from '../../services/dashboardService';
import { formatCurrencyToMillions } from '../../utils/formatters';
import dashboardService from '../../services/dashboardService';

interface MonthlyProductionWidgetProps {
  data: MonthlyProductionStats | null;
  loading?: boolean;
}

const MonthlyProductionWidget: React.FC<MonthlyProductionWidgetProps> = ({
  data: initialData,
  loading: initialLoading = false
}) => {
  const theme = useTheme();
  const [data, setData] = useState<MonthlyProductionStats | null>(initialData);
  const [loading, setLoading] = useState(initialLoading);
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toLocaleString('vi-VN');
  };

  // Load data when month/year changes
  useEffect(() => {
    const loadMonthlyData = async () => {
      if (selectedMonth === new Date().getMonth() + 1 && selectedYear === new Date().getFullYear()) {
        // Use initial data for current month
        setData(initialData);
        setLoading(initialLoading);
        return;
      }

      try {
        setLoading(true);
        const monthlyStats = await dashboardService.getMonthlyProductionStats(selectedMonth, selectedYear);
        setData(monthlyStats);
      } catch (error) {
        console.error('Error loading monthly data:', error);
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    loadMonthlyData();
  }, [selectedMonth, selectedYear, initialData, initialLoading]);

  const handleMonthChange = (event: SelectChangeEvent<string>) => {
    setSelectedMonth(parseInt(event.target.value));
  };

  const handleYearChange = (event: SelectChangeEvent<string>) => {
    setSelectedYear(parseInt(event.target.value));
  };

  const getMonthName = (month: number): string => {
    const months = [
      'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
      'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];
    return months[month - 1] || `Tháng ${month}`;
  };

  // Generate year options (current year and 2 years back)
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = 0; i <= 2; i++) {
      years.push(currentYear - i);
    }
    return years;
  };

  // Prepare chart data
  const chartData = data?.daily_breakdown.map(item => ({
    date: new Date(item.date).getDate(),
    quantity: parseFloat(item.quantity),
    value: parseFloat(item.value) / 1000000, // Convert to millions
    day: new Date(item.date).toLocaleDateString('vi-VN', { weekday: 'short' })
  })) || [];

  const getTrendIcon = (value: number) => {
    return value >= 0 ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />;
  };

  const getTrendColor = (value: number) => {
    return value >= 0 ? 'success' : 'error';
  };

  return (
    <Card
      elevation={2}
      className="laundry-hover-lift"
      sx={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: '1px solid rgba(6, 182, 212, 0.1)',
        borderRadius: 2,
        fontSize: '0.85rem'
      }}
    >
      <CardContent sx={{ p: 2.5 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <CalendarIcon
            sx={{
              color: theme.palette.info.main,
              mr: 1.5,
              fontSize: '1.5rem'
            }}
          />
          <Box sx={{ flex: 1 }}>
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                color: theme.palette.info.main,
                fontSize: '1.1rem'
              }}
            >
              Sản lượng {getMonthName(selectedMonth)} {selectedYear}
            </Typography>
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ fontSize: '0.75rem' }}
            >
              Biểu đồ sản lượng và giá trị theo ngày
            </Typography>
          </Box>

          {/* Month/Year Selectors */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <Select
                value={selectedMonth.toString()}
                onChange={handleMonthChange}
                sx={{ fontSize: '0.8rem' }}
              >
                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                  <MenuItem key={month} value={month.toString()}>
                    T{month}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 80 }}>
              <Select
                value={selectedYear.toString()}
                onChange={handleYearChange}
                sx={{ fontSize: '0.8rem' }}
              >
                {getYearOptions().map((year) => (
                  <MenuItem key={year} value={year.toString()}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              Đang tải dữ liệu...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Summary Stats */}
            <Grid container spacing={2} sx={{ mb: 2.5 }}>
              <Grid item xs={6}>
                <Box
                  sx={{
                    p: 1.5,
                    borderRadius: 1.5,
                    backgroundColor: `${theme.palette.info.main}08`,
                    border: `1px solid ${theme.palette.info.main}20`,
                    textAlign: 'center'
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: theme.palette.info.main,
                      fontSize: '1.1rem',
                      mb: 0.5
                    }}
                  >
                    {data ? formatNumber(data.total_quantity) : '0'}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ fontSize: '0.75rem' }}
                  >
                    Tổng sản lượng
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={6}>
                <Box
                  sx={{
                    p: 1.5,
                    borderRadius: 1.5,
                    backgroundColor: `${theme.palette.success.main}08`,
                    border: `1px solid ${theme.palette.success.main}20`,
                    textAlign: 'center'
                  }}
                >
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: theme.palette.success.main,
                      fontSize: '1.1rem',
                      mb: 0.5
                    }}
                  >
                    {data ? formatCurrencyToMillions(data.total_value) : '0 tr'}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ fontSize: '0.75rem' }}
                  >
                    Tổng giá trị
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            {/* Comparison with Previous Month */}
            {data && (
              <Box sx={{ display: 'flex', gap: 1, mb: 2.5, justifyContent: 'center' }}>
                <Chip
                  icon={getTrendIcon(data.comparison_with_previous_month.quantity_change_percent)}
                  label={`SL: ${data.comparison_with_previous_month.quantity_change_percent > 0 ? '+' : ''}${data.comparison_with_previous_month.quantity_change_percent.toFixed(1)}%`}
                  size="small"
                  color={getTrendColor(data.comparison_with_previous_month.quantity_change_percent)}
                  variant="outlined"
                  sx={{ fontSize: '0.75rem' }}
                />
                <Chip
                  icon={getTrendIcon(data.comparison_with_previous_month.value_change_percent)}
                  label={`GT: ${data.comparison_with_previous_month.value_change_percent > 0 ? '+' : ''}${data.comparison_with_previous_month.value_change_percent.toFixed(1)}%`}
                  size="small"
                  color={getTrendColor(data.comparison_with_previous_month.value_change_percent)}
                  variant="outlined"
                  sx={{ fontSize: '0.75rem' }}
                />
              </Box>
            )}

            {/* Chart */}
            {chartData.length > 0 ? (
              <Box sx={{ height: 250, mb: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.grey[300]} />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 10, fill: theme.palette.text.secondary }}
                      axisLine={{ stroke: theme.palette.grey[300] }}
                    />
                    <YAxis
                      yAxisId="quantity"
                      orientation="left"
                      tick={{ fontSize: 10, fill: theme.palette.info.main }}
                      axisLine={{ stroke: theme.palette.info.main }}
                      label={{ value: 'Sản lượng', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fontSize: '10px', fill: theme.palette.info.main } }}
                    />
                    <YAxis
                      yAxisId="value"
                      orientation="right"
                      tick={{ fontSize: 10, fill: theme.palette.success.main }}
                      axisLine={{ stroke: theme.palette.success.main }}
                      label={{ value: 'Giá trị (tr)', angle: 90, position: 'insideRight', style: { textAnchor: 'middle', fontSize: '10px', fill: theme.palette.success.main } }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 8,
                        fontSize: '0.8rem'
                      }}
                      formatter={(value: any, name: string) => [
                        name === 'quantity' ? `${value} sản phẩm` : `${value.toFixed(2)} tr`,
                        name === 'quantity' ? 'Sản lượng' : 'Giá trị'
                      ]}
                      labelFormatter={(label) => `Ngày ${label}`}
                    />
                    <Bar
                      yAxisId="quantity"
                      dataKey="quantity"
                      fill={theme.palette.info.main}
                      fillOpacity={0.6}
                      name="quantity"
                    />
                    <Line
                      yAxisId="value"
                      type="monotone"
                      dataKey="value"
                      stroke={theme.palette.success.main}
                      strokeWidth={3}
                      dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: theme.palette.success.main, strokeWidth: 2 }}
                      name="value"
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </Box>
            ) : (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 4,
                  color: 'text.secondary'
                }}
              >
                <CalendarIcon sx={{ fontSize: '3rem', opacity: 0.3, mb: 1 }} />
                <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                  Chưa có dữ liệu sản lượng trong tháng
                </Typography>
              </Box>
            )}

            {/* Production Days Summary */}
            {data && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.75rem' }}>
                <Typography variant="caption" color="text.secondary">
                  {data.production_days} ngày sản xuất
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {data.non_production_days} ngày nghỉ
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {data.total_records} bản ghi
                </Typography>
              </Box>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default MonthlyProductionWidget;
