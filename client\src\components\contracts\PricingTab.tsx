/**
 * PricingTab Component
 * Tab hiển thị bảng giá sản phẩm trong hợp đồng
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Skeleton,
  Alert,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// Types
import { Contract } from '../../types/contract';
import { ContractPrice } from '../../types/contractPrice';

// Services
import { contractPriceService } from '../../services/contractPriceService';

// Components
import QuickPriceForm from '../pricing/QuickPriceForm';

// Utils
import { formatCurrency } from '../../utils/formatters';

interface PricingTabProps {
  contract: Contract | null;
  loading?: boolean;
  onRefresh?: () => void;
}

interface ContractPriceDisplay {
  id: number;
  product_code: string;
  product_name: string;
  price: number;
  effective_date: string;
  expiry_date?: string | null;
  is_active: boolean;
}

const PricingTab: React.FC<PricingTabProps> = ({
  contract,
  loading: parentLoading = false,
  onRefresh,
}) => {
  // State
  const [prices, setPrices] = useState<ContractPriceDisplay[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quickPriceOpen, setQuickPriceOpen] = useState(false);
  const [quickFormLoading, setQuickFormLoading] = useState(false);
  const [quickFormError, setQuickFormError] = useState<string | null>(null);

  // Load contract prices
  const loadContractPrices = useCallback(async () => {
    if (!contract?.id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await contractPriceService.getAll({
        contract_id: contract.id,
        is_active: true, // Chỉ lấy giá đang hoạt động
      });

      if (response.success && response.data) {
        // Transform data for display
        const displayPrices: ContractPriceDisplay[] = response.data.map((price: ContractPrice) => ({
          id: price.id,
          product_code: price.product_code || '',
          product_name: price.product_name || '',
          price: price.price,
          effective_date: price.effective_date,
          expiry_date: price.expiry_date,
          is_active: price.is_active,
        }));

        setPrices(displayPrices);
      } else {
        throw new Error('Không thể tải danh sách giá');
      }
    } catch (err: any) {
      console.error('Error loading contract prices:', err);
      setError(err.message || 'Có lỗi xảy ra khi tải dữ liệu');
      setPrices([]);
    } finally {
      setLoading(false);
    }
  }, [contract?.id]);

  // Load data when contract changes
  useEffect(() => {
    loadContractPrices();
  }, [loadContractPrices]);

  // Handle quick price form
  const handleQuickPriceOpen = () => {
    setQuickPriceOpen(true);
    setQuickFormError(null);
  };

  const handleQuickPriceClose = () => {
    setQuickPriceOpen(false);
    setQuickFormError(null);
  };

  const handleQuickPriceSubmit = async (data: any) => {
    setQuickFormLoading(true);
    setQuickFormError(null);

    try {
      // Create multiple price entries for each item
      const promises = data.items.map((item: any) =>
        contractPriceService.create({
          contract_id: data.contract_id,
          product_id: item.product_id,
          price: item.price,
          effective_date: data.effective_date,
          expiry_date: data.expiry_date || null,
          is_active: true
        })
      );

      await Promise.all(promises);

      // Refresh data
      await loadContractPrices();
      if (onRefresh) onRefresh();

      setQuickPriceOpen(false);
    } catch (error: any) {
      console.error('Error submitting quick price:', error);
      setQuickFormError(error.message || 'Có lỗi xảy ra khi cập nhật giá');
    } finally {
      setQuickFormLoading(false);
    }
  };

  const handleRefresh = () => {
    loadContractPrices();
    if (onRefresh) onRefresh();
  };

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <TableContainer component={Paper} variant="outlined">
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Mã sản phẩm</TableCell>
            <TableCell>Tên sản phẩm</TableCell>
            <TableCell align="right">Đơn giá</TableCell>
            <TableCell align="center">Trạng thái</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {Array.from(new Array(5)).map((_, index) => (
            <TableRow key={index}>
              <TableCell><Skeleton variant="text" width={80} /></TableCell>
              <TableCell><Skeleton variant="text" width={200} /></TableCell>
              <TableCell align="right"><Skeleton variant="text" width={100} /></TableCell>
              <TableCell align="center"><Skeleton variant="rectangular" width={60} height={24} /></TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );

  // Render empty state
  const renderEmptyState = () => (
    <Paper variant="outlined" sx={{ p: 4, textAlign: 'center' }}>
      <Typography variant="body1" color="textSecondary" gutterBottom>
        Chưa có sản phẩm nào được thiết lập giá
      </Typography>
      <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
        Nhấn "Cập nhật giá nhanh" để thêm giá cho các sản phẩm
      </Typography>
      <Button
        variant="contained"
        startIcon={<AddIcon />}
        onClick={handleQuickPriceOpen}
        disabled={!contract || parentLoading}
      >
        Cập nhật giá nhanh
      </Button>
    </Paper>
  );

  if (!contract) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body1" color="textSecondary">
          Vui lòng chọn hợp đồng để xem bảng giá
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 'medium' }}>
          Bảng giá sản phẩm
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={loading || parentLoading}
          >
            Làm mới
          </Button>
          <Button
            variant="contained"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleQuickPriceOpen}
            disabled={!contract || loading || parentLoading}
          >
            Cập nhật giá nhanh
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Content */}
      {loading || parentLoading ? (
        renderLoadingSkeleton()
      ) : prices.length === 0 ? (
        renderEmptyState()
      ) : (
        <TableContainer component={Paper} variant="outlined">
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold' }}>Mã sản phẩm</TableCell>
                <TableCell sx={{ fontWeight: 'bold' }}>Tên sản phẩm</TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>Đơn giá</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Trạng thái</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {prices.map((price) => (
                <TableRow key={price.id} hover>
                  <TableCell sx={{ fontSize: '0.85rem' }}>
                    {price.product_code}
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.85rem' }}>
                    {price.product_name}
                  </TableCell>
                  <TableCell align="right" sx={{ fontSize: '0.85rem', fontWeight: 'medium' }}>
                    {formatCurrency(price.price)}
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={price.is_active ? 'Hoạt động' : 'Không hoạt động'}
                      color={price.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Quick Price Form Dialog */}
      {contract && (
        <QuickPriceForm
          open={quickPriceOpen}
          onClose={handleQuickPriceClose}
          onSubmit={handleQuickPriceSubmit}
          loading={quickFormLoading}
          error={quickFormError}
          title="Cập nhật giá nhanh"
          subtitle={`Cập nhật giá sản phẩm cho hợp đồng: ${contract.contract_number || contract.id}`}
          prefilledContractId={contract.id}
          prefilledCustomerId={contract.customer_id}
        />
      )}
    </Box>
  );
};

export default PricingTab;
