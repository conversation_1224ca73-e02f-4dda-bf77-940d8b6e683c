# 🚀 Development Guide - Laundry Management App

## 📦 Khởi động nhanh

### Cài đặt dependencies
```bash
npm run install-deps
```

### Khởi động cả server và client cùng lúc
```bash
npm run dev
```

## 🌐 Cấu hình Ports

- **Client**: http://localhost:5373
- **Server**: http://localhost:8501  
- **Health Check**: http://localhost:8501/health
- **Database**: PostgreSQL (localhost:5432)

## 📋 Scripts có sẵn

| Script | Mô tả |
|--------|-------|
| `npm run dev` | 🎯 **KHỞI ĐỘNG CHÍNH** - Cả server và client với màu sắc phân biệt |
| `npm run dev:server` | Chỉ khởi động server |
| `npm run dev:client` | Chỉ khởi động client |
| `npm run dev:silent` | Khởi động cả hai không có màu sắc |
| `npm run build` | Build production cho cả client và server |
| `npm run build:client` | Chỉ build client |
| `npm run install-deps` | Cài đặt dependencies cho tất cả modules |
| `npm run clean` | Xóa tất cả node_modules |
| `npm run health` | Kiểm tra health của server |

## 🎨 Output của `npm run dev`

Khi chạy `npm run dev`, bạn sẽ thấy output như sau:

```
[SERVER] 🚀 Server đang chạy trên port 8501
[SERVER] 📊 Health check: http://localhost:8501/health
[SERVER] 🌍 Environment: development
[SERVER] ✅ Kết nối database thành công

[CLIENT] VITE v5.4.19  ready in 199 ms
[CLIENT] ➜  Local:   http://localhost:5373/
[CLIENT] ➜  Network: http://***************:5373/
```

## 🛠️ Cấu hình concurrently

Script `dev` sử dụng các tùy chọn sau:
- `--kill-others-on-fail`: Tắt tất cả khi một process bị lỗi
- `--prefix "[{name}]"`: Hiển thị prefix cho mỗi dòng log
- `--names "SERVER,CLIENT"`: Đặt tên cho các process
- `--prefix-colors "blue,green"`: Màu sắc cho prefix

## 🔧 Troubleshooting

### Port bị chiếm dụng
Nếu gặp lỗi `EADDRINUSE`, thay đổi port trong:
- Server: `server/.env` → `PORT=8502`
- Client: `client/.env` → `VITE_API_URL=http://localhost:8502/api/v1`
- Client: `client/vite.config.ts` → `target: 'http://localhost:8502'`

### Restart riêng từng service
```bash
# Trong terminal riêng
cd server && npm run dev

# Trong terminal khác  
cd client && npm run dev
```

## 📁 Cấu trúc dự án

```
├── client/          # Frontend React TypeScript (Port: 5373)
├── server/          # Backend Node.js Express (Port: 8501)
├── database/        # Database scripts và migrations
├── docs/           # Documentation
└── package.json    # Root scripts với concurrently
```

## 🎯 Lợi ích của cấu hình này

1. **Khởi động nhanh**: Một lệnh duy nhất cho cả stack
2. **Màu sắc phân biệt**: Dễ theo dõi log của từng service
3. **Auto-restart**: Nodemon cho server, Vite HMR cho client
4. **Fail-safe**: Tắt tất cả khi một service bị lỗi
5. **Flexible**: Vẫn có thể chạy riêng từng service khi cần

## 🚀 Bắt đầu phát triển

```bash
# 1. Cài đặt dependencies
npm run install-deps

# 2. Khởi động development
npm run dev

# 3. Mở browser tại http://localhost:5373
```

Vậy là xong! Bạn đã sẵn sàng để phát triển! 🎉
