import api from './api';
import {
  Payment,
  CreatePaymentData,
  UpdatePaymentData,
  PaymentFilters,
  PaymentsResponse,
  AllocationPreviewData,
  AllocationPreviewResult,
  PAYMENT_METHODS,
  PAYMENT_STATUSES
} from '../types/payment';

/**
 * Payment Service
 * Xử lý các API calls liên quan đến payments (Thanh toán)
 */

const BASE_URL = '/payments';

export const paymentService = {
  /**
   * Lấy danh sách payments với filtering và pagination
   */
  async getPayments(filters: PaymentFilters = {}): Promise<PaymentsResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.customerId) params.append('customerId', filters.customerId.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.paymentMethod) params.append('paymentMethod', filters.paymentMethod);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      const response = await api.get(`${BASE_URL}?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }
  },

  /**
   * Lấy payment theo ID với thông tin allocation
   */
  async getPaymentById(id: number): Promise<Payment> {
    try {
      const response = await api.get(`${BASE_URL}/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }
  },

  /**
   * Tạo payment mới
   */
  async createPayment(data: CreatePaymentData): Promise<Payment> {
    try {
      const response = await api.post(BASE_URL, data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating payment:', error);
      throw error;
    }
  },

  /**
   * Cập nhật payment
   */
  async updatePayment(id: number, data: UpdatePaymentData): Promise<Payment> {
    try {
      const response = await api.put(`${BASE_URL}/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error updating payment:', error);
      throw error;
    }
  },

  /**
   * Xóa payment
   */
  async deletePayment(id: number): Promise<void> {
    try {
      await api.delete(`${BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error deleting payment:', error);
      throw error;
    }
  },

  /**
   * Xác nhận payment
   */
  async confirmPayment(id: number): Promise<Payment> {
    try {
      const response = await api.post(`${BASE_URL}/${id}/confirm`);
      return response.data.data;
    } catch (error) {
      console.error('Error confirming payment:', error);
      throw error;
    }
  },

  /**
   * Lấy preview FIFO allocation
   */
  async getPaymentAllocationPreview(data: AllocationPreviewData): Promise<AllocationPreviewResult> {
    try {
      const response = await api.post(`${BASE_URL}/allocation-preview`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error getting allocation preview:', error);
      throw error;
    }
  },

  /**
   * Validate payment data trước khi submit
   */
  validatePaymentData(data: CreatePaymentData | UpdatePaymentData): string[] {
    const errors: string[] = [];

    if (!data.customer_id) {
      errors.push('Khách hàng là bắt buộc');
    }

    if (!data.payment_date) {
      errors.push('Ngày thanh toán là bắt buộc');
    }

    if (!data.amount || data.amount <= 0) {
      errors.push('Số tiền phải lớn hơn 0');
    }

    if (!data.payment_method?.trim()) {
      errors.push('Phương thức thanh toán là bắt buộc');
    }

    // Validate payment method
    if (data.payment_method && !PAYMENT_METHODS.includes(data.payment_method as any)) {
      errors.push('Phương thức thanh toán không hợp lệ');
    }

    // Validate payment date không quá xa trong tương lai
    if (data.payment_date) {
      const paymentDate = new Date(data.payment_date);
      const today = new Date();
      const maxFutureDate = new Date();
      maxFutureDate.setDate(today.getDate() + 30); // Cho phép tối đa 30 ngày trong tương lai

      if (paymentDate > maxFutureDate) {
        errors.push('Ngày thanh toán không thể quá 30 ngày trong tương lai');
      }
    }

    return errors;
  },

  /**
   * Format payment data cho display
   */
  formatPaymentForDisplay(payment: Payment) {
    return {
      ...payment,
      amount_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(payment.amount),
      payment_date_formatted: new Date(payment.payment_date).toLocaleDateString('vi-VN'),
      status_label: this.getStatusLabel(payment.status),
      status_color: this.getStatusColor(payment.status)
    };
  },

  /**
   * Lấy label cho status
   */
  getStatusLabel(status: string): string {
    const statusItem = PAYMENT_STATUSES.find(s => s.value === status);
    return statusItem?.label || status;
  },

  /**
   * Lấy color cho status
   */
  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' {
    const statusItem = PAYMENT_STATUSES.find(s => s.value === status);
    return (statusItem?.color as any) || 'info';
  },

  /**
   * Lấy danh sách payment methods
   */
  getPaymentMethods() {
    return PAYMENT_METHODS.map(method => ({
      value: method,
      label: method
    }));
  },

  /**
   * Lấy danh sách payment statuses
   */
  getPaymentStatuses() {
    return PAYMENT_STATUSES.map(status => ({
      value: status.value,
      label: status.label,
      color: status.color
    }));
  },

  /**
   * Format allocation preview cho display
   */
  formatAllocationPreview(preview: AllocationPreviewResult) {
    return {
      ...preview,
      allocations: preview.allocations.map(allocation => ({
        ...allocation,
        original_amount_formatted: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(allocation.original_amount),
        remaining_balance_formatted: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(allocation.remaining_balance),
        allocated_amount_formatted: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(allocation.allocated_amount),
        remaining_after_allocation_formatted: new Intl.NumberFormat('vi-VN', {
          style: 'currency',
          currency: 'VND'
        }).format(allocation.remaining_after_allocation),
        transaction_date_formatted: new Date(allocation.transaction_date).toLocaleDateString('vi-VN'),
        due_date_formatted: new Date(allocation.due_date).toLocaleDateString('vi-VN')
      })),
      total_allocated_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(preview.total_allocated),
      unallocated_amount_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(preview.unallocated_amount)
    };
  }
};

export default paymentService;
