const paymentService = require('../services/paymentService');
const { successResponse, errorResponse } = require('../utils/responseUtils');

/**
 * Payment Controller
 * Xử lý các request liên quan đến payments (Thanh toán)
 */

/**
 * L<PERSON>y danh sách payments
 * GET /api/payments
 */
const getPayments = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      customerId,
      status,
      paymentMethod,
      sortBy = 'payment_date',
      sortOrder = 'DESC'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      search: search.trim(),
      customerId: customerId ? parseInt(customerId) : undefined,
      status,
      paymentMethod,
      sortBy,
      sortOrder
    };

    const result = await paymentService.getPayments(options);
    
    return successResponse(res, result, 'L<PERSON>y danh sách thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.getPayments:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy payment theo ID
 * GET /api/payments/:id
 */
const getPaymentById = async (req, res) => {
  try {
    const { id } = req.params;
    const payment = await paymentService.getPaymentById(parseInt(id));
    
    return successResponse(res, payment, 'Lấy thông tin thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.getPaymentById:', error);
    const statusCode = error.message.includes('không tìm thấy') ? 404 : 500;
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Tạo payment mới
 * POST /api/payments
 */
const createPayment = async (req, res) => {
  try {
    const paymentData = req.body;
    const createdBy = req.user?.id || 1; // Temporary fallback

    const newPayment = await paymentService.createPayment(paymentData, createdBy);
    
    return successResponse(res, newPayment, 'Tạo thanh toán thành công', 201);
  } catch (error) {
    console.error('Error in paymentController.createPayment:', error);
    return errorResponse(res, error.message, 400);
  }
};

/**
 * Cập nhật payment
 * PUT /api/payments/:id
 */
const updatePayment = async (req, res) => {
  try {
    const { id } = req.params;
    const paymentData = req.body;

    const updatedPayment = await paymentService.updatePayment(parseInt(id), paymentData);
    
    return successResponse(res, updatedPayment, 'Cập nhật thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.updatePayment:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    else if (error.message.includes('không hợp lệ') || error.message.includes('Thiếu')) statusCode = 400;
    
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Xóa payment
 * DELETE /api/payments/:id
 */
const deletePayment = async (req, res) => {
  try {
    const { id } = req.params;
    await paymentService.deletePayment(parseInt(id));
    
    return successResponse(res, null, 'Xóa thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.deletePayment:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Xác nhận payment và thực hiện FIFO allocation
 * POST /api/payments/:id/confirm
 */
const confirmPayment = async (req, res) => {
  try {
    const { id } = req.params;
    const confirmedPayment = await paymentService.confirmPayment(parseInt(id));
    
    return successResponse(res, confirmedPayment, 'Xác nhận thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.confirmPayment:', error);
    let statusCode = 500;
    if (error.message.includes('không tìm thấy')) statusCode = 404;
    else if (error.message.includes('Chỉ có thể')) statusCode = 400;
    
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Lấy preview FIFO allocation
 * POST /api/payments/allocation-preview
 */
const getPaymentAllocationPreview = async (req, res) => {
  try {
    const { customerId, amount, paymentDate } = req.body;

    // Validate input
    if (!customerId || !amount || !paymentDate) {
      return errorResponse(res, 'Thiếu thông tin bắt buộc: customerId, amount, paymentDate', 400);
    }

    if (isNaN(customerId) || isNaN(amount) || amount <= 0) {
      return errorResponse(res, 'Dữ liệu không hợp lệ', 400);
    }

    const preview = await paymentService.getPaymentAllocationPreview(
      parseInt(customerId),
      parseFloat(amount),
      paymentDate
    );
    
    return successResponse(res, preview, 'Lấy preview phân bổ thanh toán thành công');
  } catch (error) {
    console.error('Error in paymentController.getPaymentAllocationPreview:', error);
    return errorResponse(res, error.message, 400);
  }
};

/**
 * Validate payment data middleware
 */
const validatePaymentData = (req, res, next) => {
  const {
    customer_id,
    payment_date,
    amount,
    payment_method
  } = req.body;

  const errors = [];

  // Kiểm tra các trường bắt buộc
  if (!customer_id) errors.push('customer_id là bắt buộc');
  if (!payment_date) errors.push('payment_date là bắt buộc');
  if (!amount) errors.push('amount là bắt buộc');
  if (!payment_method) errors.push('payment_method là bắt buộc');

  // Validate data types
  if (customer_id && isNaN(customer_id)) errors.push('customer_id phải là số');
  if (amount && (isNaN(amount) || amount <= 0)) {
    errors.push('amount phải là số dương');
  }

  // Validate date
  if (payment_date && isNaN(new Date(payment_date).getTime())) {
    errors.push('payment_date không hợp lệ');
  }

  // Validate payment_method
  const validPaymentMethods = ['Tiền mặt', 'Chuyển khoản', 'Thẻ tín dụng', 'Séc', 'Khác'];
  if (payment_method && !validPaymentMethods.includes(payment_method)) {
    errors.push(`payment_method phải là một trong: ${validPaymentMethods.join(', ')}`);
  }

  // Validate status if provided
  if (req.body.status) {
    const validStatuses = ['pending', 'confirmed', 'cancelled'];
    if (!validStatuses.includes(req.body.status)) {
      errors.push(`status phải là một trong: ${validStatuses.join(', ')}`);
    }
  }

  if (errors.length > 0) {
    return errorResponse(res, `Dữ liệu không hợp lệ: ${errors.join(', ')}`, 400);
  }

  next();
};

/**
 * Validate ID parameter middleware
 */
const validateIdParam = (req, res, next) => {
  const { id } = req.params;
  
  if (!id || isNaN(id) || parseInt(id) <= 0) {
    return errorResponse(res, 'ID không hợp lệ', 400);
  }
  
  next();
};

/**
 * Validate allocation preview data middleware
 */
const validateAllocationPreviewData = (req, res, next) => {
  const { customerId, amount, paymentDate } = req.body;
  const errors = [];

  if (!customerId) errors.push('customerId là bắt buộc');
  if (!amount) errors.push('amount là bắt buộc');
  if (!paymentDate) errors.push('paymentDate là bắt buộc');

  if (customerId && isNaN(customerId)) errors.push('customerId phải là số');
  if (amount && (isNaN(amount) || amount <= 0)) errors.push('amount phải là số dương');
  if (paymentDate && isNaN(new Date(paymentDate).getTime())) {
    errors.push('paymentDate không hợp lệ');
  }

  if (errors.length > 0) {
    return errorResponse(res, `Dữ liệu không hợp lệ: ${errors.join(', ')}`, 400);
  }

  next();
};

module.exports = {
  getPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
  confirmPayment,
  getPaymentAllocationPreview,
  validatePaymentData,
  validateIdParam,
  validateAllocationPreviewData
};
