{"name": "customer-management-client", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "~5.14.0", "@mui/material": "~5.14.0", "@mui/x-data-grid": "~6.14.0", "@mui/x-date-pickers": "~6.14.0", "@types/lodash": "^4.17.17", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/xlsx": "^0.0.35", "@vitejs/plugin-react": "^4.1.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-router-dom": "^6.20.1", "recharts": "^2.15.3", "typescript": "^5.2.2", "vite": "^5.0.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yup": "^1.6.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4"}}