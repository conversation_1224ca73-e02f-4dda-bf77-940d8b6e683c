const express = require('express');
const router = express.Router();
const receivableController = require('../controllers/receivableController');

/**
 * Receivable Routes
 * Định nghĩa các route cho receivables (Công nợ phải thu)
 */

/**
 * @route   GET /api/receivables
 * @desc    Lấy danh sách receivables với filtering và pagination
 * @access  Private
 * @query   {number} page - Trang hiện tại (default: 1)
 * @query   {number} limit - Số lượng bản ghi trên mỗi trang (default: 10)
 * @query   {string} search - Từ khóa tìm kiếm
 * @query   {number} customerId - Lọ<PERSON> theo khách hàng
 * @query   {number} contractId - Lọc theo hợp đồng
 * @query   {string} status - Lọ<PERSON> theo trạng thái (active, overdue, paid)
 * @query   {string} sortBy - Cột để sắp xếp
 * @query   {string} sortOrder - <PERSON><PERSON><PERSON> tự sắp xếp (ASC/DESC)
 */
router.get('/', receivableController.getReceivables);

/**
 * @route   GET /api/receivables/:id
 * @desc    Lấy receivable theo ID
 * @access  Private
 * @param   {number} id - ID của receivable
 */
router.get('/:id', 
  receivableController.validateIdParam,
  receivableController.getReceivableById
);

/**
 * @route   POST /api/receivables
 * @desc    Tạo receivable mới
 * @access  Private
 * @body    {Object} receivableData - Dữ liệu receivable
 * @body    {number} receivableData.customer_id - ID khách hàng
 * @body    {number} receivableData.contract_id - ID hợp đồng
 * @body    {string} receivableData.invoice_number - Số hóa đơn
 * @body    {string} receivableData.transaction_date - Ngày giao dịch
 * @body    {string} receivableData.due_date - Ngày đến hạn
 * @body    {string} receivableData.description - Mô tả
 * @body    {number} receivableData.original_amount - Số tiền gốc
 * @body    {string} receivableData.currency - Loại tiền tệ (default: VND)
 * @body    {number} receivableData.payment_terms - Thời hạn thanh toán (default: 30)
 * @body    {string} receivableData.notes - Ghi chú
 */
router.post('/', 
  receivableController.validateReceivableData,
  receivableController.createReceivable
);

/**
 * @route   PUT /api/receivables/:id
 * @desc    Cập nhật receivable
 * @access  Private
 * @param   {number} id - ID của receivable
 * @body    {Object} receivableData - Dữ liệu cập nhật
 */
router.put('/:id', 
  receivableController.validateIdParam,
  receivableController.validateReceivableData,
  receivableController.updateReceivable
);

/**
 * @route   DELETE /api/receivables/:id
 * @desc    Xóa receivable (soft delete)
 * @access  Private
 * @param   {number} id - ID của receivable
 */
router.delete('/:id', 
  receivableController.validateIdParam,
  receivableController.deleteReceivable
);

/**
 * @route   POST /api/receivables/from-production
 * @desc    Tạo receivable từ sản lượng đã chọn
 * @access  Private
 * @body    {Object} receivableData - Dữ liệu receivable từ production
 * @body    {number} receivableData.customer_id - ID khách hàng
 * @body    {number} receivableData.contract_id - ID hợp đồng
 * @body    {string} receivableData.invoice_number - Số hóa đơn
 * @body    {string} receivableData.transaction_date - Ngày giao dịch
 * @body    {string} receivableData.due_date - Ngày đến hạn
 * @body    {string} receivableData.description - Mô tả
 * @body    {number} receivableData.original_amount - Số tiền gốc
 * @body    {Array} receivableData.production_ids - Danh sách ID sản lượng
 */
router.post('/from-production',
  receivableController.validateReceivableFromProductionData,
  receivableController.createReceivableFromProduction
);

/**
 * @route   POST /api/receivables/bulk-create
 * @desc    Tạo receivables từ dữ liệu sản xuất
 * @access  Private
 * @body    {Object} options - Tùy chọn tạo bulk
 * @body    {number} options.contractId - ID hợp đồng
 * @body    {string} options.startDate - Ngày bắt đầu
 * @body    {string} options.endDate - Ngày kết thúc
 * @body    {string} options.invoicePrefix - Tiền tố hóa đơn (default: INV)
 */
router.post('/bulk-create', receivableController.createReceivablesFromProduction);

module.exports = router;
