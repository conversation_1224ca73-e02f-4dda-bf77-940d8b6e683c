const { pool } = require('../db');

/**
 * Daily Production Model
 * Xử lý các thao tác CRUD cho bảng daily_production
 */

/**
 * L<PERSON>y tất cả sản lượng với filter
 * @param {Object} options - T<PERSON>y chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.production_date - Filter theo ngày sản xuất
 * @param {number} options.contract_id - Filter theo hợp đồng
 * @param {number} options.product_id - Filter theo sản phẩm
 * @param {string} options.date_from - Filter từ ngày
 * @param {string} options.date_to - Filter đến ngày
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllDailyProduction = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    production_date = '',
    contract_id = '',
    product_id = '',
    date_from = '',
    date_to = '',
    sortBy = 'production_date',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'production_date', 'quantity', 'unit_price', 'total_amount', 'created_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'production_date';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    // Filter theo production_date
    if (production_date && production_date.trim()) {
      whereClause += ` AND DATE(dp.production_date) = $${paramIndex}`;
      queryParams.push(production_date.trim());
      paramIndex++;
    }

    // Filter theo contract_id
    if (contract_id && !isNaN(parseInt(contract_id))) {
      whereClause += ` AND dp.contract_id = $${paramIndex}`;
      queryParams.push(parseInt(contract_id));
      paramIndex++;
    }

    // Filter theo product_id
    if (product_id && !isNaN(parseInt(product_id))) {
      whereClause += ` AND dp.product_id = $${paramIndex}`;
      queryParams.push(parseInt(product_id));
      paramIndex++;
    }

    // Filter theo khoảng thời gian
    if (date_from && date_from.trim()) {
      whereClause += ` AND dp.production_date >= $${paramIndex}`;
      queryParams.push(date_from.trim());
      paramIndex++;
    }

    if (date_to && date_to.trim()) {
      whereClause += ` AND dp.production_date <= $${paramIndex}`;
      queryParams.push(date_to.trim());
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT
        dp.id,
        TO_CHAR(dp.production_date, 'YYYY-MM-DD') as production_date,
        dp.contract_id,
        dp.product_id,
        dp.quantity,
        dp.unit_price,
        dp.total_amount,
        dp.notes,
        dp.created_at,
        dp.updated_at,
        c.contract_number,
        c.contract_name,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type,
        u.name as created_by_name
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN products p ON dp.product_id = p.id
      LEFT JOIN users u ON dp.created_by = u.id
      ${whereClause}
      ORDER BY dp.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    // Tính tổng kết
    const summaryQuery = `
      SELECT
        SUM(dp.quantity) as total_quantity,
        SUM(dp.total_amount) as total_amount
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
    `;

    const summaryResult = await pool.query(summaryQuery, queryParams.slice(0, -2));
    const summary = summaryResult.rows[0];

    return {
      productions: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      summary: {
        total_quantity: parseFloat(summary.total_quantity) || 0,
        total_amount: parseFloat(summary.total_amount) || 0
      }
    };
  } catch (error) {
    console.error('Error in getAllDailyProduction:', error);
    throw error;
  }
};

/**
 * Lấy sản lượng theo ID
 * @param {number} id - ID của sản lượng
 * @returns {Object|null} Thông tin sản lượng hoặc null nếu không tìm thấy
 */
const getDailyProductionById = async (id) => {
  try {
    const query = `
      SELECT
        dp.id,
        dp.production_date,
        dp.contract_id,
        dp.product_id,
        dp.quantity,
        dp.unit_price,
        dp.total_amount,
        dp.notes,
        dp.created_at,
        dp.updated_at,
        c.contract_number,
        c.contract_name,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type,
        u.name as created_by_name
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN products p ON dp.product_id = p.id
      LEFT JOIN users u ON dp.created_by = u.id
      WHERE dp.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getDailyProductionById:', error);
    throw error;
  }
};

/**
 * Tạo sản lượng mới
 * @param {Object} productionData - Dữ liệu sản lượng
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin sản lượng vừa tạo
 */
const createDailyProduction = async (productionData, createdBy) => {
  const {
    production_date,
    contract_id,
    product_id,
    quantity,
    unit_price,
    notes,
    status = 'Mới tạo' // Mặc định là 'Mới tạo' theo workflow mới
  } = productionData;

  try {
    // Tính total_amount
    const total_amount = quantity * unit_price;

    const query = `
      INSERT INTO daily_production (
        production_date, contract_id, product_id, quantity, unit_price, total_amount, notes, status, created_by, updated_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `;

    const values = [
      production_date,
      contract_id,
      product_id,
      quantity,
      unit_price,
      total_amount,
      notes || null,
      status,
      createdBy,
      createdBy // updated_by = created_by khi tạo mới
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createDailyProduction:', error);
    throw error;
  }
};

/**
 * Cập nhật sản lượng
 * @param {number} id - ID của sản lượng
 * @param {Object} productionData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin sản lượng sau khi cập nhật
 */
const updateDailyProduction = async (id, productionData) => {
  const {
    production_date,
    contract_id,
    product_id,
    quantity,
    unit_price,
    notes
  } = productionData;

  try {
    // Tính total_amount
    const total_amount = quantity * unit_price;

    const query = `
      UPDATE daily_production
      SET
        production_date = $1,
        contract_id = $2,
        product_id = $3,
        quantity = $4,
        unit_price = $5,
        total_amount = $6,
        notes = $7,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $8
      RETURNING *
    `;

    const values = [
      production_date,
      contract_id,
      product_id,
      quantity,
      unit_price,
      total_amount,
      notes || null,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateDailyProduction:', error);
    throw error;
  }
};

/**
 * Xóa sản lượng (hard delete)
 * @param {number} id - ID của sản lượng
 * @returns {boolean} True nếu xóa thành công
 */
const deleteDailyProduction = async (id) => {
  try {
    const query = `
      DELETE FROM daily_production
      WHERE id = $1
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteDailyProduction:', error);
    throw error;
  }
};

/**
 * Kiểm tra sản lượng đã tồn tại
 * @param {string} productionDate - Ngày sản xuất
 * @param {number} contractId - ID hợp đồng
 * @param {number} productId - ID sản phẩm
 * @param {number} excludeId - ID sản lượng cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu đã tồn tại
 */
const checkProductionExists = async (productionDate, contractId, productId, excludeId = null) => {
  try {
    let query = `
      SELECT id FROM daily_production
      WHERE production_date = $1 AND contract_id = $2 AND product_id = $3
    `;
    const params = [productionDate, contractId, productId];

    if (excludeId) {
      query += ' AND id != $4';
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkProductionExists:', error);
    throw error;
  }
};

/**
 * Tạo sản lượng hàng loạt
 * @param {string} productionDate - Ngày sản xuất
 * @param {number} contractId - ID hợp đồng
 * @param {Array} items - Danh sách sản phẩm và số lượng
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Kết quả tạo hàng loạt
 */
const createBulkProduction = async (productionDate, contractId, items, createdBy) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const createdItems = [];
    let totalAmount = 0;

    for (const item of items) {
      const { product_id, quantity, unit_price, notes } = item;
      const itemTotalAmount = quantity * unit_price;

      const query = `
        INSERT INTO daily_production (
          production_date, contract_id, product_id, quantity, unit_price, total_amount, notes, created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const values = [
        productionDate,
        contractId,
        product_id,
        quantity,
        unit_price,
        itemTotalAmount,
        notes || null,
        createdBy
      ];

      const result = await client.query(query, values);
      createdItems.push(result.rows[0]);
      totalAmount += itemTotalAmount;
    }

    await client.query('COMMIT');

    return {
      created_count: createdItems.length,
      total_amount: totalAmount,
      items: createdItems
    };
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in createBulkProduction:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Lấy báo cáo sản lượng theo khoảng thời gian
 * @param {string} dateFrom - Từ ngày
 * @param {string} dateTo - Đến ngày
 * @param {number} contractId - ID hợp đồng (optional)
 * @returns {Object} Báo cáo sản lượng
 */
const getProductionReport = async (dateFrom, dateTo, contractId = null) => {
  try {
    let whereClause = 'WHERE dp.production_date >= $1 AND dp.production_date <= $2';
    const params = [dateFrom, dateTo];

    if (contractId) {
      whereClause += ' AND dp.contract_id = $3';
      params.push(contractId);
    }

    // Báo cáo tổng quan
    const summaryQuery = `
      SELECT
        COUNT(*) as total_records,
        COUNT(DISTINCT dp.contract_id) as total_contracts,
        COUNT(DISTINCT dp.product_id) as total_products,
        SUM(dp.quantity) as total_quantity,
        SUM(dp.total_amount) as total_amount,
        AVG(dp.total_amount) as avg_amount_per_day
      FROM daily_production dp
      ${whereClause}
    `;

    // Báo cáo theo sản phẩm
    const productQuery = `
      SELECT
        p.code as product_code,
        p.name as product_name,
        p.unit_type,
        SUM(dp.quantity) as total_quantity,
        SUM(dp.total_amount) as total_amount,
        AVG(dp.unit_price) as avg_unit_price,
        COUNT(*) as production_days
      FROM daily_production dp
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
      GROUP BY p.id, p.code, p.name, p.unit_type
      ORDER BY total_amount DESC
    `;

    // Báo cáo theo hợp đồng
    const contractQuery = `
      SELECT
        c.contract_number,
        c.contract_name,
        cust.name as customer_name,
        SUM(dp.quantity) as total_quantity,
        SUM(dp.total_amount) as total_amount,
        COUNT(DISTINCT dp.product_id) as total_products,
        COUNT(*) as production_days
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      ${whereClause}
      GROUP BY c.id, c.contract_number, c.contract_name, cust.name
      ORDER BY total_amount DESC
    `;

    // Báo cáo theo ngày
    const dailyQuery = `
      SELECT
        dp.production_date,
        COUNT(*) as total_records,
        SUM(dp.quantity) as total_quantity,
        SUM(dp.total_amount) as total_amount
      FROM daily_production dp
      ${whereClause}
      GROUP BY dp.production_date
      ORDER BY dp.production_date DESC
    `;

    const [summaryResult, productResult, contractResult, dailyResult] = await Promise.all([
      pool.query(summaryQuery, params),
      pool.query(productQuery, params),
      pool.query(contractQuery, params),
      pool.query(dailyQuery, params)
    ]);

    return {
      summary: summaryResult.rows[0],
      by_product: productResult.rows,
      by_contract: contractResult.rows,
      by_date: dailyResult.rows
    };
  } catch (error) {
    console.error('Error in getProductionReport:', error);
    throw error;
  }
};

/**
 * Lấy sản lượng theo ngày cụ thể
 * @param {string} productionDate - Ngày sản xuất
 * @param {number} contractId - ID hợp đồng (optional)
 * @returns {Array} Danh sách sản lượng trong ngày
 */
const getProductionByDate = async (productionDate, contractId = null) => {
  try {
    let whereClause = 'WHERE dp.production_date = $1';
    const params = [productionDate];

    if (contractId) {
      whereClause += ' AND dp.contract_id = $2';
      params.push(contractId);
    }

    const query = `
      SELECT
        dp.id,
        TO_CHAR(dp.production_date, 'YYYY-MM-DD') as production_date,
        dp.contract_id,
        dp.product_id,
        dp.quantity,
        dp.unit_price,
        dp.total_amount,
        dp.notes,
        c.contract_number,
        c.contract_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
      ORDER BY dp.created_at ASC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  } catch (error) {
    console.error('Error in getProductionByDate:', error);
    throw error;
  }
};

/**
 * Tìm kiếm sản lượng để tạo công nợ
 * @param {Object} options - Tùy chọn tìm kiếm
 * @param {number} options.contractId - ID hợp đồng
 * @param {string} options.status - Trạng thái sản lượng
 * @param {string} options.startDate - Ngày bắt đầu (optional)
 * @param {string} options.endDate - Ngày kết thúc (optional)
 * @returns {Array} Danh sách sản lượng phù hợp
 */
const searchForReceivable = async (options) => {
  const { contractId, status, startDate, endDate } = options;

  try {
    let whereClause = 'WHERE dp.contract_id = $1';
    const params = [contractId];
    let paramIndex = 2;

    // Filter theo status nếu được cung cấp
    if (status) {
      whereClause += ` AND dp.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    // Filter theo khoảng thời gian
    if (startDate) {
      whereClause += ` AND dp.production_date >= $${paramIndex}`;
      params.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereClause += ` AND dp.production_date <= $${paramIndex}`;
      params.push(endDate);
      paramIndex++;
    }

    const query = `
      SELECT
        dp.id as production_id,
        dp.production_date,
        dp.contract_id,
        dp.quantity,
        dp.unit_price,
        dp.total_amount,
        dp.status,
        dp.notes,
        c.contract_number as contract_code,
        c.contract_name,
        cust.id as customer_id,
        cust.name as customer_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
      ORDER BY dp.production_date DESC, dp.id ASC
    `;

    const result = await pool.query(query, params);

    // Convert numeric fields to numbers for JavaScript
    const processedRows = result.rows.map(row => ({
      ...row,
      quantity: parseFloat(row.quantity) || 0,
      unit_price: parseFloat(row.unit_price) || 0,
      total_amount: parseFloat(row.total_amount) || 0
    }));

    return processedRows;
  } catch (error) {
    console.error('Error in searchForReceivable:', error);
    throw error;
  }
};

/**
 * Bulk update status cho nhiều sản lượng
 * @param {Array} productionIds - Mảng ID sản lượng
 * @param {string} newStatus - Trạng thái mới
 * @param {number} updatedBy - ID người cập nhật
 * @returns {Object} Kết quả cập nhật
 */
const bulkUpdateStatus = async (productionIds, newStatus, updatedBy) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Validate status value
    const validStatuses = ['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ'];
    if (!validStatuses.includes(newStatus)) {
      throw new Error(`Trạng thái không hợp lệ: ${newStatus}`);
    }

    // Get current status of productions to validate transitions
    const checkQuery = `
      SELECT id, status, production_date, contract_id
      FROM daily_production
      WHERE id = ANY($1)
    `;
    const checkResult = await client.query(checkQuery, [productionIds]);

    // Validate each production can be updated
    const errors = [];
    const validIds = [];

    for (const production of checkResult.rows) {
      // Cannot change from 'Đã ghi nhận công nợ'
      if (production.status === 'Đã ghi nhận công nợ' && newStatus !== 'Đã ghi nhận công nợ') {
        errors.push(`ID ${production.id}: Không thể thay đổi từ trạng thái "Đã ghi nhận công nợ"`);
        continue;
      }

      // Cannot skip from 'Mới tạo' to 'Đã ghi nhận công nợ'
      if (production.status === 'Mới tạo' && newStatus === 'Đã ghi nhận công nợ') {
        errors.push(`ID ${production.id}: Không thể chuyển trực tiếp từ "Mới tạo" sang "Đã ghi nhận công nợ"`);
        continue;
      }

      validIds.push(production.id);
    }

    if (validIds.length === 0) {
      throw new Error(`Không có bản ghi nào có thể cập nhật: ${errors.join(', ')}`);
    }

    // Update status for valid productions
    const updateQuery = `
      UPDATE daily_production
      SET status = $1, updated_by = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = ANY($3)
      RETURNING id, status, production_date, contract_id
    `;

    const updateResult = await client.query(updateQuery, [newStatus, updatedBy, validIds]);

    await client.query('COMMIT');

    return {
      success: true,
      updated_count: updateResult.rowCount,
      updated_records: updateResult.rows,
      errors: errors,
      skipped_count: productionIds.length - validIds.length
    };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in bulkUpdateStatus:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Lấy sản lượng theo tháng hoặc giai đoạn để xác nhận
 * @param {number} year - Năm (optional)
 * @param {number} month - Tháng (optional)
 * @param {number} contractId - ID hợp đồng (optional)
 * @param {string} status - Trạng thái lọc (optional)
 * @param {string} startDate - Ngày bắt đầu (optional)
 * @param {string} endDate - Ngày kết thúc (optional)
 * @returns {Array} Danh sách sản lượng
 */
const getProductionForConfirmation = async (year = null, month = null, contractId = null, status = null, startDate = null, endDate = null) => {
  try {
    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramIndex = 1;

    // Lọc theo tháng/năm hoặc giai đoạn
    if (year && month) {
      whereClause += ` AND EXTRACT(YEAR FROM dp.production_date) = $${paramIndex}`;
      params.push(year);
      paramIndex++;
      whereClause += ` AND EXTRACT(MONTH FROM dp.production_date) = $${paramIndex}`;
      params.push(month);
      paramIndex++;
    } else if (startDate && endDate) {
      whereClause += ` AND dp.production_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
      params.push(startDate, endDate);
      paramIndex += 2;
    }

    if (contractId) {
      whereClause += ` AND dp.contract_id = $${paramIndex}`;
      params.push(contractId);
      paramIndex++;
    }

    if (status) {
      whereClause += ` AND dp.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    const query = `
      SELECT
        dp.id,
        TO_CHAR(dp.production_date, 'YYYY-MM-DD') as production_date,
        dp.contract_id,
        dp.product_id,
        dp.quantity,
        dp.unit_price,
        dp.total_amount,
        dp.status,
        dp.notes,
        dp.created_at,
        dp.updated_at,
        c.contract_number,
        c.contract_name,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        p.code as product_code,
        p.name as product_name,
        p.unit_type as product_unit_type,
        u.name as created_by_name
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN products p ON dp.product_id = p.id
      LEFT JOIN users u ON dp.created_by = u.id
      ${whereClause}
      ORDER BY dp.production_date DESC, dp.id ASC
    `;

    const result = await pool.query(query, params);
    return result.rows;
  } catch (error) {
    console.error('Error in getProductionForConfirmation:', error);
    throw error;
  }
};

/**
 * Lấy thống kê sản lượng theo trạng thái
 * @param {number} year - Năm (optional)
 * @param {number} month - Tháng (optional)
 * @param {number} contractId - ID hợp đồng (optional)
 * @param {string} startDate - Ngày bắt đầu (optional)
 * @param {string} endDate - Ngày kết thúc (optional)
 * @returns {Object} Thống kê theo trạng thái
 */
const getProductionStatusStats = async (year = null, month = null, contractId = null, startDate = null, endDate = null) => {
  try {
    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramIndex = 1;

    // Lọc theo tháng/năm hoặc giai đoạn
    if (year && month) {
      whereClause += ` AND EXTRACT(YEAR FROM production_date) = $${paramIndex}`;
      params.push(year);
      paramIndex++;
      whereClause += ` AND EXTRACT(MONTH FROM production_date) = $${paramIndex}`;
      params.push(month);
      paramIndex++;
    } else if (startDate && endDate) {
      whereClause += ` AND production_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
      params.push(startDate, endDate);
      paramIndex += 2;
    }

    if (contractId) {
      whereClause += ` AND contract_id = $${paramIndex}`;
      params.push(contractId);
      paramIndex++;
    }

    // Query để lấy tất cả trạng thái, kể cả trạng thái có giá trị 0
    const query = `
      WITH all_statuses AS (
        SELECT unnest(ARRAY['Mới tạo', 'Đã xác nhận', 'Đã ghi nhận công nợ']) as status
      ),
      production_stats AS (
        SELECT
          status,
          COUNT(*) as count,
          COALESCE(SUM(total_amount), 0) as total_amount
        FROM daily_production
        ${whereClause}
        GROUP BY status
      )
      SELECT
        s.status,
        COALESCE(ps.count, 0) as count,
        COALESCE(ps.total_amount, 0) as total_amount
      FROM all_statuses s
      LEFT JOIN production_stats ps ON s.status = ps.status
      ORDER BY
        CASE s.status
          WHEN 'Mới tạo' THEN 1
          WHEN 'Đã xác nhận' THEN 2
          WHEN 'Đã ghi nhận công nợ' THEN 3
          ELSE 4
        END
    `;

    const result = await pool.query(query, params);
    return result.rows;
  } catch (error) {
    console.error('Error in getProductionStatusStats:', error);
    throw error;
  }
};

/**
 * Lấy sản lượng đã nhóm theo hợp đồng và ngày
 * @param {Object} options - Các tùy chọn lọc và phân trang
 * @returns {Promise<Object>} Kết quả sản lượng đã nhóm và thông tin phân trang
 */
const getGroupedProduction = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      date_from,
      date_to,
      contract_id,
      sortBy = 'production_date',
      sortOrder = 'desc'
    } = options;

    // Validate và xử lý các tham số
    const validPage = Math.max(1, parseInt(page));
    const validLimit = Math.min(100, Math.max(1, parseInt(limit)));
    const offset = (validPage - 1) * validLimit;

    // Xây dựng điều kiện WHERE
    const whereConditions = [];
    const params = [];
    let paramIndex = 1;

    if (date_from && date_to) {
      whereConditions.push(`dp.production_date BETWEEN $${paramIndex} AND $${paramIndex + 1}`);
      params.push(date_from, date_to);
      paramIndex += 2;
    } else if (date_from) {
      whereConditions.push(`dp.production_date >= $${paramIndex}`);
      params.push(date_from);
      paramIndex += 1;
    } else if (date_to) {
      whereConditions.push(`dp.production_date <= $${paramIndex}`);
      params.push(date_to);
      paramIndex += 1;
    }

    if (contract_id) {
      whereConditions.push(`dp.contract_id = $${paramIndex}`);
      params.push(contract_id);
      paramIndex += 1;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Validate sortBy và sortOrder
    const validSortBy = ['production_date', 'contract_id', 'total_amount'].includes(sortBy) ? sortBy : 'production_date';
    const validSortOrder = sortOrder.toLowerCase() === 'asc' ? 'ASC' : 'DESC';

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) FROM (
        SELECT dp.contract_id, dp.production_date
        FROM daily_production dp
        ${whereClause}
        GROUP BY dp.contract_id, dp.production_date
      ) AS grouped_count
    `;

    // Query để lấy dữ liệu đã nhóm
    const dataQuery = `
      SELECT
        TO_CHAR(dp.production_date, 'YYYY-MM-DD') as production_date,
        dp.contract_id,
        c.contract_number,
        c.contract_name,
        cust.id as customer_id,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        COUNT(DISTINCT dp.product_id) as product_count,
        SUM(dp.total_amount) as total_amount
      FROM daily_production dp
      LEFT JOIN contracts c ON dp.contract_id = c.id
      LEFT JOIN customers cust ON c.customer_id = cust.id
      ${whereClause}
      GROUP BY dp.production_date, dp.contract_id, c.contract_number, c.contract_name, cust.id, cust.name, cust.short_name
      ORDER BY dp.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    // Thêm tham số cho LIMIT và OFFSET
    params.push(validLimit, offset);

    // Thực hiện các truy vấn
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, params.slice(0, paramIndex - 1)),
      pool.query(dataQuery, params)
    ]);

    const totalCount = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(totalCount / validLimit);

    return {
      productions: dataResult.rows,
      pagination: {
        page: validPage,
        limit: validLimit,
        totalCount,
        totalPages
      }
    };
  } catch (error) {
    console.error('Error in getGroupedProduction:', error);
    throw error;
  }
};

/**
 * Lấy báo cáo sản lượng theo tháng
 * @param {number} year - Năm
 * @param {number} month - Tháng
 * @param {number} contractId - ID hợp đồng
 * @param {string} status - Trạng thái (optional)
 * @returns {Object} Báo cáo sản lượng theo tháng
 */
const getMonthlyReport = async (year, month, contractId, status = null) => {
  try {
    let whereClause = `WHERE EXTRACT(YEAR FROM dp.production_date) = $1
                       AND EXTRACT(MONTH FROM dp.production_date) = $2
                       AND dp.contract_id = $3`;
    const params = [year, month, contractId];
    let paramIndex = 4;

    if (status && status !== 'all') {
      whereClause += ` AND dp.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }

    // Lấy thông tin hợp đồng và khách hàng
    const contractQuery = `
      SELECT
        c.id,
        c.contract_number,
        c.contract_name,
        cust.name as customer_name,
        cust.short_name as customer_short_name
      FROM contracts c
      LEFT JOIN customers cust ON c.customer_id = cust.id
      WHERE c.id = $1
    `;
    const contractResult = await pool.query(contractQuery, [contractId]);

    if (contractResult.rows.length === 0) {
      throw new Error('Không tìm thấy hợp đồng');
    }

    const contract = contractResult.rows[0];

    // Lấy danh sách sản phẩm có sản lượng trong tháng
    const productsQuery = `
      SELECT DISTINCT
        p.id,
        p.code,
        p.name,
        p.unit_type,
        dp.unit_price
      FROM daily_production dp
      LEFT JOIN products p ON dp.product_id = p.id
      ${whereClause}
      ORDER BY p.code
    `;
    const productsResult = await pool.query(productsQuery, params);
    const products = productsResult.rows;

    // Lấy dữ liệu sản lượng theo ngày
    const dailyDataQuery = `
      SELECT
        dp.product_id,
        EXTRACT(DAY FROM dp.production_date) as day,
        SUM(dp.quantity) as quantity
      FROM daily_production dp
      ${whereClause}
      GROUP BY dp.product_id, EXTRACT(DAY FROM dp.production_date)
      ORDER BY dp.product_id, day
    `;
    const dailyDataResult = await pool.query(dailyDataQuery, params);

    // Tổ chức dữ liệu theo ngày
    const dailyData = {};
    products.forEach(product => {
      dailyData[product.id] = new Array(31).fill(0);
    });

    dailyDataResult.rows.forEach(row => {
      const dayIndex = parseInt(row.day) - 1; // Convert to 0-based index
      if (dayIndex >= 0 && dayIndex < 31) {
        dailyData[row.product_id][dayIndex] = parseFloat(row.quantity);
      }
    });

    // Tính tổng cho mỗi sản phẩm
    const productTotals = {};
    products.forEach(product => {
      const totalQuantity = dailyData[product.id].reduce((sum, qty) => sum + qty, 0);
      const totalAmount = totalQuantity * parseFloat(product.unit_price);

      productTotals[product.id] = {
        quantity: totalQuantity,
        amount: totalAmount
      };
    });

    // Tính tổng cộng
    const grandTotal = Object.values(productTotals).reduce((sum, item) => sum + item.amount, 0);
    const vatAmount = grandTotal * 0.08; // 8% VAT
    const totalWithVat = grandTotal + vatAmount;

    return {
      contract,
      year,
      month,
      products,
      dailyData,
      productTotals,
      summary: {
        total_before_vat: grandTotal,
        vat_amount: vatAmount,
        total_with_vat: totalWithVat,
        total_paid: 0, // Có thể tính từ payment records
        remaining_amount: totalWithVat
      }
    };
  } catch (error) {
    console.error('Error in getMonthlyReport:', error);
    throw error;
  }
};

module.exports = {
  getAllDailyProduction,
  getDailyProductionById,
  createDailyProduction,
  updateDailyProduction,
  deleteDailyProduction,
  checkProductionExists,
  createBulkProduction,
  getProductionReport,
  getProductionByDate,
  searchForReceivable,
  bulkUpdateStatus,
  getProductionForConfirmation,
  getProductionStatusStats,
  getGroupedProduction,
  getMonthlyReport
};
