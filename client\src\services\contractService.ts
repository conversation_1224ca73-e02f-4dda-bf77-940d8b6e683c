/**
 * Contract Service
 * API service for contract management
 */

import { get, post, put, del, patch } from './api';
import { Contract, ContractFilterOptions } from '../types/contract';

/**
 * Contract API Service
 */
export const contractService = {
  /**
   * <PERSON><PERSON><PERSON> tất cả hợp đồng với filter và pagination
   */
  getAll: async (options?: ContractFilterOptions) => {
    const params = new URLSearchParams();

    if (options?.page) params.append('page', options.page.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.search) params.append('search', options.search);
    if (options?.customer_id) params.append('customer_id', options.customer_id.toString());
    if (options?.status) params.append('status', options.status);
    if (options?.start_date_from) params.append('start_date_from', options.start_date_from);
    if (options?.start_date_to) params.append('start_date_to', options.start_date_to);
    if (options?.end_date_from) params.append('end_date_from', options.end_date_from);
    if (options?.end_date_to) params.append('end_date_to', options.end_date_to);
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const url = queryString ? `/contracts?${queryString}` : '/contracts';

    return get<Contract[]>(url);
  },

  /**
   * Lấy hợp đồng theo ID
   */
  getById: async (id: number) => {
    return get<Contract>(`/contracts/${id}`);
  },

  /**
   * Tạo hợp đồng mới
   */
  create: async (data: any) => {
    return post<Contract>('/contracts', data);
  },

  /**
   * Cập nhật hợp đồng
   */
  update: async (id: number, data: any) => {
    return put<Contract>(`/contracts/${id}`, data);
  },

  /**
   * Xóa hợp đồng
   */
  delete: async (id: number) => {
    return del<{ message: string; id: number }>(`/contracts/${id}`);
  },

  /**
   * Tìm kiếm hợp đồng
   */
  search: async (query: string, limit: number = 10) => {
    const params = new URLSearchParams({
      q: query,
      limit: limit.toString(),
    });

    return get<Contract[]>(`/contracts/search?${params.toString()}`);
  },

  /**
   * Lấy danh sách trạng thái hợp đồng có sẵn
   */
  getStatuses: async () => {
    return get<string[]>('/contracts/statuses');
  },

  /**
   * Lấy hợp đồng theo khách hàng
   */
  getByCustomer: async (customerId: number) => {
    return get<Contract[]>(`/contracts/customer/${customerId}`);
  },

  /**
   * Cập nhật trạng thái hợp đồng
   */
  updateStatus: async (id: number, data: { status: string; notes?: string }) => {
    return patch<Contract>(`/contracts/${id}/status`, data);
  },

  /**
   * Xuất Excel
   */
  exportToExcel: async (filters?: ContractFilterOptions) => {
    const params = new URLSearchParams();

    if (filters?.search) params.append('search', filters.search);
    if (filters?.customer_id) params.append('customer_id', filters.customer_id.toString());
    if (filters?.status) params.append('status', filters.status);
    if (filters?.start_date_from) params.append('start_date_from', filters.start_date_from);
    if (filters?.start_date_to) params.append('start_date_to', filters.start_date_to);
    if (filters?.end_date_from) params.append('end_date_from', filters.end_date_from);
    if (filters?.end_date_to) params.append('end_date_to', filters.end_date_to);

    const queryString = params.toString();
    const url = queryString ? `/contracts/export?${queryString}` : '/contracts/export';

    const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:8501/api/v1'}${url}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Không thể xuất file Excel');
    }

    return response.blob();
  },

  /**
   * Get contracts (for receivables compatibility)
   */
  getContracts: async (options?: ContractFilterOptions) => {
    const response = await contractService.getAll(options);
    // Return in expected format for receivables
    return {
      contracts: response.data || response || [],
      pagination: response.meta?.pagination || {
        page: 1,
        limit: options?.limit || 10,
        total: Array.isArray(response.data) ? response.data.length : Array.isArray(response) ? response.length : 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  },

};

export default contractService;
