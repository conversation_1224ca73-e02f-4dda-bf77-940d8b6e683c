const db = require('../db');

/**
 * Dashboard Service
 * Xử lý logic business cho dashboard KPIs
 */

/**
 * L<PERSON>y thống kê tổng quan hệ thống
 * @returns {Object} Thống kê tổng quan
 */
const getGeneralStats = async () => {
  try {
    const query = `
      SELECT 
        (SELECT COUNT(*) FROM customers WHERE is_active = true) as total_customers,
        (SELECT COUNT(*) FROM products WHERE is_active = true) as total_products,
        (SELECT COUNT(*) FROM contracts WHERE status = 'active') as total_contracts,
        (SELECT COUNT(*) FROM contracts WHERE status = 'active' AND start_date <= CURRENT_DATE AND (end_date IS NULL OR end_date >= CURRENT_DATE)) as active_contracts,
        (SELECT COUNT(*) FROM daily_production WHERE production_date = CURRENT_DATE) as today_production_records,
        (SELECT COALESCE(SUM(total_amount), 0) FROM daily_production WHERE production_date = CURRENT_DATE) as today_production_value
    `;
    
    const result = await db.query(query);
    const row = result.rows[0];

    // Đảm bảo kiểu dữ liệu số đúng cho general stats
    return {
      ...row,
      total_customers: parseInt(row.total_customers) || 0,
      total_products: parseInt(row.total_products) || 0,
      total_contracts: parseInt(row.total_contracts) || 0,
      active_contracts: parseInt(row.active_contracts) || 0,
      today_production_records: parseInt(row.today_production_records) || 0,
      today_production_value: parseFloat(row.today_production_value) || 0
    };
  } catch (error) {
    console.error('Error in dashboardService.getGeneralStats:', error);
    throw error;
  }
};

/**
 * Lấy sản lượng hôm nay chi tiết
 * @returns {Object} Thông tin sản lượng hôm nay
 */
const getTodayProduction = async () => {
  try {
    const query = `
      SELECT 
        COALESCE(SUM(dp.quantity), 0) as total_quantity,
        COALESCE(SUM(dp.total_amount), 0) as total_value,
        COUNT(DISTINCT dp.product_id) as product_types,
        COUNT(DISTINCT dp.contract_id) as contracts_involved,
        COUNT(*) as total_records
      FROM daily_production dp
      WHERE dp.production_date = CURRENT_DATE
    `;
    
    const result = await db.query(query);
    const row = result.rows[0];

    // Đảm bảo kiểu dữ liệu số đúng
    return {
      ...row,
      total_quantity: parseFloat(row.total_quantity) || 0,
      total_value: parseFloat(row.total_value) || 0,
      product_types: parseInt(row.product_types) || 0,
      contracts_involved: parseInt(row.contracts_involved) || 0,
      total_records: parseInt(row.total_records) || 0
    };
  } catch (error) {
    console.error('Error in dashboardService.getTodayProduction:', error);
    throw error;
  }
};

/**
 * Lấy top 5 sản phẩm có sản lượng cao nhất
 * @param {string} period - Khoảng thời gian: 'today', 'week', 'month'
 * @returns {Array} Danh sách top 5 sản phẩm
 */
const getTopProducts = async (period = 'today') => {
  try {
    let dateCondition = '';
    
    switch (period) {
      case 'today':
        dateCondition = 'dp.production_date = CURRENT_DATE';
        break;
      case 'week':
        dateCondition = 'dp.production_date >= CURRENT_DATE - INTERVAL \'7 days\'';
        break;
      case 'month':
        dateCondition = 'dp.production_date >= CURRENT_DATE - INTERVAL \'30 days\'';
        break;
      default:
        dateCondition = 'dp.production_date = CURRENT_DATE';
    }
    
    const query = `
      SELECT 
        p.id,
        p.code as product_code,
        p.name as product_name,
        p.unit_type,
        COALESCE(SUM(dp.quantity), 0) as total_quantity,
        COALESCE(SUM(dp.total_amount), 0) as total_value,
        COUNT(dp.id) as production_records,
        COUNT(DISTINCT dp.contract_id) as contracts_count
      FROM products p
      LEFT JOIN daily_production dp ON p.id = dp.product_id AND ${dateCondition}
      WHERE p.is_active = true
      GROUP BY p.id, p.code, p.name, p.unit_type
      HAVING COALESCE(SUM(dp.quantity), 0) > 0
      ORDER BY total_quantity DESC, total_value DESC
      LIMIT 5
    `;
    
    const result = await db.query(query);

    // Đảm bảo kiểu dữ liệu số đúng cho top products
    return result.rows.map(row => ({
      ...row,
      total_quantity: parseFloat(row.total_quantity) || 0,
      total_value: parseFloat(row.total_value) || 0,
      production_records: parseInt(row.production_records) || 0,
      contracts_count: parseInt(row.contracts_count) || 0
    }));
  } catch (error) {
    console.error('Error in dashboardService.getTopProducts:', error);
    throw error;
  }
};

/**
 * Lấy tóm tắt tình hình công nợ
 * Sử dụng aging_analysis_summary view để đảm bảo nhất quán với báo cáo tuổi nợ
 * @returns {Object} Thông tin tóm tắt công nợ
 */
const getDebtSummary = async () => {
  try {
    const query = `
      SELECT
        COUNT(DISTINCT aas.customer_id) as customers_with_debt,
        COALESCE(SUM(aas.total_outstanding), 0) as total_receivables,
        COALESCE(SUM(aas.days_1_30 + aas.days_31_60 + aas.days_61_90 + aas.over_90_days), 0) as total_overdue,
        SUM(aas.overdue_invoices) as overdue_invoices,
        SUM(aas.active_invoices) as active_invoices,
        -- Tính số ngày quá hạn trung bình có trọng số theo số tiền
        CASE
          WHEN SUM(aas.days_1_30 + aas.days_31_60 + aas.days_61_90 + aas.over_90_days) > 0 THEN
            ROUND(
              (SUM(aas.days_1_30 * 15) + SUM(aas.days_31_60 * 45) + SUM(aas.days_61_90 * 75) + SUM(aas.over_90_days * 120)) /
              SUM(aas.days_1_30 + aas.days_31_60 + aas.days_61_90 + aas.over_90_days), 0
            )
          ELSE 0
        END as avg_overdue_days,
        -- Thêm thông tin chi tiết aging buckets
        COALESCE(SUM(aas.current_amount), 0) as current_amount,
        COALESCE(SUM(aas.days_1_30), 0) as days_1_30,
        COALESCE(SUM(aas.days_31_60), 0) as days_31_60,
        COALESCE(SUM(aas.days_61_90), 0) as days_61_90,
        COALESCE(SUM(aas.over_90_days), 0) as over_90_days
      FROM aging_analysis_summary aas
      WHERE aas.total_outstanding > 0
    `;

    const result = await db.query(query);
    const row = result.rows[0];

    // Đảm bảo kiểu dữ liệu số đúng cho debt summary
    return {
      ...row,
      customers_with_debt: parseInt(row.customers_with_debt) || 0,
      total_receivables: parseFloat(row.total_receivables) || 0,
      total_overdue: parseFloat(row.total_overdue) || 0,
      overdue_invoices: parseInt(row.overdue_invoices) || 0,
      active_invoices: parseInt(row.active_invoices) || 0,
      avg_overdue_days: parseInt(row.avg_overdue_days) || 0,
      current_amount: parseFloat(row.current_amount) || 0,
      days_1_30: parseFloat(row.days_1_30) || 0,
      days_31_60: parseFloat(row.days_31_60) || 0,
      days_61_90: parseFloat(row.days_61_90) || 0,
      over_90_days: parseFloat(row.over_90_days) || 0
    };
  } catch (error) {
    console.error('Error in dashboardService.getDebtSummary:', error);
    throw error;
  }
};

/**
 * Lấy top 5 khách hàng có công nợ quá hạn cao nhất
 * Sử dụng aging_analysis_summary view để đảm bảo nhất quán với báo cáo tuổi nợ
 * @returns {Array} Danh sách top 5 khách hàng nợ quá hạn
 */
const getTopOverdueCustomers = async () => {
  try {
    const query = `
      SELECT
        aas.customer_id,
        aas.customer_name,
        aas.short_name,
        aas.tax_code,
        (aas.days_1_30 + aas.days_31_60 + aas.days_61_90 + aas.over_90_days) as overdue_amount,
        aas.overdue_invoices,
        aas.oldest_overdue_date,
        aas.max_days_overdue,
        aas.total_outstanding,
        aas.overdue_percentage,
        -- Thêm thông tin chi tiết aging buckets
        aas.days_1_30,
        aas.days_31_60,
        aas.days_61_90,
        aas.over_90_days
      FROM aging_analysis_summary aas
      WHERE (aas.days_1_30 + aas.days_31_60 + aas.days_61_90 + aas.over_90_days) > 0
      ORDER BY overdue_amount DESC, aas.max_days_overdue DESC
      LIMIT 5
    `;

    const result = await db.query(query);

    // Đảm bảo kiểu dữ liệu số đúng cho top overdue customers
    return result.rows.map(row => ({
      ...row,
      overdue_amount: parseFloat(row.overdue_amount) || 0,
      overdue_invoices: parseInt(row.overdue_invoices) || 0,
      max_days_overdue: parseInt(row.max_days_overdue) || 0,
      total_outstanding: parseFloat(row.total_outstanding) || 0,
      overdue_percentage: parseFloat(row.overdue_percentage) || 0,
      days_1_30: parseFloat(row.days_1_30) || 0,
      days_31_60: parseFloat(row.days_31_60) || 0,
      days_61_90: parseFloat(row.days_61_90) || 0,
      over_90_days: parseFloat(row.over_90_days) || 0
    }));
  } catch (error) {
    console.error('Error in dashboardService.getTopOverdueCustomers:', error);
    throw error;
  }
};

/**
 * Lấy thống kê sản lượng theo tháng
 * @param {number} month - Tháng (1-12)
 * @param {number} year - Năm (YYYY)
 * @returns {Object} Thống kê sản lượng tháng
 */
const getMonthlyProductionStats = async (month, year) => {
  try {
    // Validate input parameters
    const currentDate = new Date();
    const targetMonth = month || (currentDate.getMonth() + 1);
    const targetYear = year || currentDate.getFullYear();

    if (targetMonth < 1 || targetMonth > 12) {
      throw new Error('Tháng phải từ 1 đến 12');
    }

    if (targetYear < 2000 || targetYear > 2100) {
      throw new Error('Năm không hợp lệ');
    }

    // Tính tháng trước để so sánh
    let previousMonth = targetMonth - 1;
    let previousYear = targetYear;
    if (previousMonth === 0) {
      previousMonth = 12;
      previousYear = targetYear - 1;
    }

    // Query 1: Tổng sản lượng tháng hiện tại
    const currentMonthQuery = `
      SELECT
        COALESCE(SUM(dp.quantity), 0) as total_quantity,
        COALESCE(SUM(dp.total_amount), 0) as total_value,
        COUNT(DISTINCT dp.production_date) as production_days,
        COUNT(*) as total_records,
        COUNT(DISTINCT dp.product_id) as product_types,
        COUNT(DISTINCT dp.contract_id) as contracts_involved
      FROM daily_production dp
      WHERE EXTRACT(MONTH FROM dp.production_date) = $1
        AND EXTRACT(YEAR FROM dp.production_date) = $2
    `;

    const currentMonthResult = await db.query(currentMonthQuery, [targetMonth, targetYear]);
    const currentMonthStats = currentMonthResult.rows[0];

    // Query 2: Tổng sản lượng tháng trước để so sánh
    const previousMonthQuery = `
      SELECT
        COALESCE(SUM(dp.quantity), 0) as total_quantity,
        COALESCE(SUM(dp.total_amount), 0) as total_value
      FROM daily_production dp
      WHERE EXTRACT(MONTH FROM dp.production_date) = $1
        AND EXTRACT(YEAR FROM dp.production_date) = $2
    `;

    const previousMonthResult = await db.query(previousMonthQuery, [previousMonth, previousYear]);
    const previousMonthStats = previousMonthResult.rows[0];

    // Tính phần trăm thay đổi
    const quantityChangePercent = previousMonthStats.total_quantity > 0
      ? ((currentMonthStats.total_quantity - previousMonthStats.total_quantity) / previousMonthStats.total_quantity * 100)
      : (currentMonthStats.total_quantity > 0 ? 100 : 0);

    const valueChangePercent = previousMonthStats.total_value > 0
      ? ((currentMonthStats.total_value - previousMonthStats.total_value) / previousMonthStats.total_value * 100)
      : (currentMonthStats.total_value > 0 ? 100 : 0);

    // Query 3: Sản lượng theo từng ngày trong tháng
    const dailyBreakdownQuery = `
      SELECT
        TO_CHAR(dp.production_date, 'YYYY-MM-DD') as date,
        COALESCE(SUM(dp.quantity), 0) as quantity,
        COALESCE(SUM(dp.total_amount), 0) as value,
        COUNT(*) as records_count,
        COUNT(DISTINCT dp.product_id) as product_types
      FROM daily_production dp
      WHERE EXTRACT(MONTH FROM dp.production_date) = $1
        AND EXTRACT(YEAR FROM dp.production_date) = $2
      GROUP BY dp.production_date
      ORDER BY dp.production_date
    `;

    const dailyBreakdownResult = await db.query(dailyBreakdownQuery, [targetMonth, targetYear]);

    // Query 4: Top 5 sản phẩm trong tháng
    const topProductsQuery = `
      SELECT
        p.id,
        p.code as product_code,
        p.name as product_name,
        p.unit_type,
        COALESCE(SUM(dp.quantity), 0) as total_quantity,
        COALESCE(SUM(dp.total_amount), 0) as total_value,
        COUNT(dp.id) as production_records,
        COUNT(DISTINCT dp.contract_id) as contracts_count,
        COUNT(DISTINCT dp.production_date) as production_days
      FROM products p
      INNER JOIN daily_production dp ON p.id = dp.product_id
      WHERE EXTRACT(MONTH FROM dp.production_date) = $1
        AND EXTRACT(YEAR FROM dp.production_date) = $2
        AND p.is_active = true
      GROUP BY p.id, p.code, p.name, p.unit_type
      ORDER BY total_quantity DESC, total_value DESC
      LIMIT 5
    `;

    const topProductsResult = await db.query(topProductsQuery, [targetMonth, targetYear]);

    // Tính số ngày trong tháng và số ngày không sản xuất
    const daysInMonth = new Date(targetYear, targetMonth, 0).getDate();
    const productionDays = parseInt(currentMonthStats.production_days);
    const nonProductionDays = daysInMonth - productionDays;

    return {
      month: targetMonth,
      year: targetYear,
      total_quantity: parseFloat(currentMonthStats.total_quantity) || 0,
      total_value: parseFloat(currentMonthStats.total_value) || 0,
      comparison_with_previous_month: {
        previous_month: previousMonth,
        previous_year: previousYear,
        quantity_change_percent: parseFloat(quantityChangePercent.toFixed(2)),
        value_change_percent: parseFloat(valueChangePercent.toFixed(2)),
        previous_quantity: parseFloat(previousMonthStats.total_quantity) || 0,
        previous_value: parseFloat(previousMonthStats.total_value) || 0
      },
      daily_breakdown: dailyBreakdownResult.rows.map(row => ({
        ...row,
        quantity: parseFloat(row.quantity) || 0,
        value: parseFloat(row.value) || 0,
        records_count: parseInt(row.records_count) || 0,
        product_types: parseInt(row.product_types) || 0
      })),
      top_products: topProductsResult.rows.map(row => ({
        ...row,
        total_quantity: parseFloat(row.total_quantity) || 0,
        total_value: parseFloat(row.total_value) || 0,
        production_records: parseInt(row.production_records) || 0,
        contracts_count: parseInt(row.contracts_count) || 0,
        production_days: parseInt(row.production_days) || 0
      })),
      production_days: productionDays,
      non_production_days: nonProductionDays,
      total_days_in_month: daysInMonth,
      total_records: parseInt(currentMonthStats.total_records),
      product_types: parseInt(currentMonthStats.product_types),
      contracts_involved: parseInt(currentMonthStats.contracts_involved)
    };
  } catch (error) {
    console.error('Error in dashboardService.getMonthlyProductionStats:', error);
    throw error;
  }
};

/**
 * Lấy tất cả dữ liệu dashboard trong một lần gọi
 * @returns {Object} Tất cả dữ liệu dashboard
 */
const getAllDashboardData = async () => {
  try {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();

    const [
      generalStats,
      todayProduction,
      topProductsToday,
      topProductsWeek,
      debtSummary,
      topOverdueCustomers,
      monthlyProductionStats
    ] = await Promise.all([
      getGeneralStats(),
      getTodayProduction(),
      getTopProducts('today'),
      getTopProducts('week'),
      getDebtSummary(),
      getTopOverdueCustomers(),
      getMonthlyProductionStats(currentMonth, currentYear)
    ]);

    return {
      general_stats: generalStats,
      today_production: todayProduction,
      top_products_today: topProductsToday,
      top_products_week: topProductsWeek,
      debt_summary: debtSummary,
      top_overdue_customers: topOverdueCustomers,
      monthly_production_stats: monthlyProductionStats,
      last_updated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error in dashboardService.getAllDashboardData:', error);
    throw error;
  }
};

module.exports = {
  getGeneralStats,
  getTodayProduction,
  getTopProducts,
  getDebtSummary,
  getTopOverdueCustomers,
  getMonthlyProductionStats,
  getAllDashboardData
};
