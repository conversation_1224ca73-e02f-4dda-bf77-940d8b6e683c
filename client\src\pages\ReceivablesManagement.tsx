import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Paper,
  Alert,
  Snackbar
} from '@mui/material';
import { AccountBalance } from '@mui/icons-material';

// Components
import ReceivablesList from '../components/receivables/ReceivablesList';
import PaymentsList from '../components/receivables/PaymentsList';
import AgingReport from '../components/receivables/AgingReport';
import DebtSummary from '../components/receivables/DebtSummary';

// Types
import { Receivable } from '../types/receivable';
import { Payment } from '../types/payment';

/**
 * Receivables Management Page
 * Trang chính quản lý công nợ phải thu với các tab:
 * - Danh sách công nợ
 * - Thanh toán
 * - Báo cáo tuổi nợ
 * - Tổng hợp công nợ
 */

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`receivables-tabpanel-${index}`}
      aria-labelledby={`receivables-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `receivables-tab-${index}`,
    'aria-controls': `receivables-tabpanel-${index}`,
  };
}

const ReceivablesManagement: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // State để refresh data khi có thay đổi
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const showSnackbar = (message: string, severity: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };

  const handleDataChange = () => {
    setRefreshTrigger(prev => prev + 1);
    showSnackbar('Dữ liệu đã được cập nhật', 'success');
  };

  const handleReceivableCreated = (receivable: Receivable) => {
    handleDataChange();
    showSnackbar(`Đã tạo công nợ ${receivable.invoice_number}`, 'success');
  };

  const handleReceivableUpdated = (receivable: Receivable) => {
    handleDataChange();
    showSnackbar(`Đã cập nhật công nợ ${receivable.invoice_number}`, 'success');
  };

  const handleReceivableDeleted = (invoiceNumber: string) => {
    handleDataChange();
    showSnackbar(`Đã xóa công nợ ${invoiceNumber}`, 'success');
  };

  const handlePaymentCreated = (payment: Payment) => {
    handleDataChange();
    showSnackbar(`Đã tạo thanh toán ${payment.reference_number || payment.id}`, 'success');
  };

  const handlePaymentUpdated = (payment: Payment) => {
    handleDataChange();
    showSnackbar(`Đã cập nhật thanh toán ${payment.reference_number || payment.id}`, 'success');
  };

  const handlePaymentDeleted = (referenceNumber: string) => {
    handleDataChange();
    showSnackbar(`Đã xóa thanh toán ${referenceNumber}`, 'success');
  };

  const handleError = (error: string) => {
    showSnackbar(error, 'error');
  };

  return (
    <Container maxWidth="xl" sx={{ py: 2 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AccountBalance sx={{ mr: 2, color: 'primary.main', fontSize: 32 }} />
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
            Quản lý Công nợ Phải thu
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Theo dõi công nợ, thanh toán và phân tích tuổi nợ với phân bổ FIFO tự động
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="receivables management tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab 
              label="Danh sách Công nợ" 
              {...a11yProps(0)}
              sx={{ fontSize: '0.85rem', minHeight: 48 }}
            />
            <Tab 
              label="Thanh toán" 
              {...a11yProps(1)}
              sx={{ fontSize: '0.85rem', minHeight: 48 }}
            />
            <Tab 
              label="Báo cáo Tuổi nợ" 
              {...a11yProps(2)}
              sx={{ fontSize: '0.85rem', minHeight: 48 }}
            />
            <Tab 
              label="Tổng hợp Công nợ" 
              {...a11yProps(3)}
              sx={{ fontSize: '0.85rem', minHeight: 48 }}
            />
          </Tabs>
        </Box>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          <ReceivablesList
            refreshTrigger={refreshTrigger}
            onReceivableCreated={handleReceivableCreated}
            onReceivableUpdated={handleReceivableUpdated}
            onReceivableDeleted={handleReceivableDeleted}
            onError={handleError}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <PaymentsList
            refreshTrigger={refreshTrigger}
            onPaymentCreated={handlePaymentCreated}
            onPaymentUpdated={handlePaymentUpdated}
            onPaymentDeleted={handlePaymentDeleted}
            onError={handleError}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <AgingReport
            refreshTrigger={refreshTrigger}
            onError={handleError}
          />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <DebtSummary
            refreshTrigger={refreshTrigger}
            onError={handleError}
          />
        </TabPanel>
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default ReceivablesManagement;
