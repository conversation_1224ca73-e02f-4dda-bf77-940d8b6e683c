# 🚀 Quick Start Guide

## Khởi động nhanh trong 3 bước

### 1. Cài đặt dependencies
```bash
npm run install-deps
```

### 2. Khởi động ứng dụng
```bash
npm run dev
```

### 3. Mở browser
- **Frontend**: http://localhost:5373
- **API Health**: http://localhost:8501/health

## 🎯 Kết quả mong đợi

Khi chạy `npm run dev`, bạn sẽ thấy:

```
[SERVER] 🚀 Server đang chạy trên port 8501
[SERVER] 📊 Health check: http://localhost:8501/health
[SERVER] 🌍 Environment: development
[SERVER] ✅ Kết nối database thành công

[CLIENT] VITE v5.4.19  ready in 1398 ms
[CLIENT] ➜  Local:   http://localhost:5373/
[CLIENT] ➜  Network: http://***********:5373/
```

## 📋 Scripts chính

| Lệnh | Mô tả |
|------|-------|
| `npm run dev` | 🎯 **LỆNH CHÍNH** - Khởi động cả server và client |
| `npm run dev:server` | Chỉ server |
| `npm run dev:client` | Chỉ client |
| `npm run health` | Kiểm tra server |

## 🔧 Ports

- **Client**: 5373
- **Server**: 8501
- **Database**: 5432

## ⚡ Tính năng

- ✅ Khởi động cả stack với 1 lệnh
- ✅ Màu sắc phân biệt log
- ✅ Auto-restart khi có thay đổi
- ✅ Fail-safe: tắt tất cả khi có lỗi

Vậy là xong! 🎉
