import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  MenuItem,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as ConfirmIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';

// Services
import paymentService from '../../services/paymentService';
import customerService from '../../services/customerService';

// Components
import ConfirmDialog from '../common/ConfirmDialog';
import PaymentDialog from './PaymentDialog';

// Types
import { Payment, PaymentFilters } from '../../types/payment';
import { Customer } from '../../types/customer';

interface PaymentsListProps {
  refreshTrigger: number;
  onPaymentCreated: (payment: Payment) => void;
  onPaymentUpdated: (payment: Payment) => void;
  onPaymentDeleted: (referenceNumber: string) => void;
  onError: (error: string) => void;
}

const PaymentsList: React.FC<PaymentsListProps> = ({
  refreshTrigger,
  onPaymentCreated,
  onPaymentUpdated,
  onPaymentDeleted,
  onError
}) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 10,
    total: 0
  });

  // Filters
  const [filters, setFilters] = useState<PaymentFilters>({
    page: 1,
    limit: 10,
    search: '',
    customerId: undefined,
    status: '',
    paymentMethod: '',
    sortBy: 'payment_date',
    sortOrder: 'DESC'
  });

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [paymentToDelete, setPaymentToDelete] = useState<Payment | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [paymentToConfirm, setPaymentToConfirm] = useState<Payment | null>(null);

  // Load data
  const loadPayments = async () => {
    try {
      setLoading(true);
      const response = await paymentService.getPayments(filters);
      setPayments(response.payments);
      setPagination({
        page: response.pagination.page - 1,
        pageSize: response.pagination.limit,
        total: response.pagination.total
      });
    } catch (error: any) {
      onError(error.message || 'Không thể tải danh sách thanh toán');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const response = await customerService.getCustomers({ limit: 1000 });
      // Ensure we're setting an array, handle both response formats
      const customersArray = response.customers || response.data || [];
      setCustomers(Array.isArray(customersArray) ? customersArray : []);
    } catch (error: any) {
      console.error('Error loading customers:', error);
      setCustomers([]); // Set empty array on error to prevent map error
    }
  };

  useEffect(() => {
    loadPayments();
  }, [filters, refreshTrigger]);

  useEffect(() => {
    loadCustomers();
  }, []);

  // Handlers
  const handleFilterChange = (field: keyof PaymentFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1
    }));
  };

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({
      ...prev,
      page: newPage + 1
    }));
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setFilters(prev => ({
      ...prev,
      limit: newPageSize,
      page: 1
    }));
  };

  const handleCreate = () => {
    setSelectedPayment(null);
    setDialogOpen(true);
  };

  const handleEdit = (payment: Payment) => {
    setSelectedPayment(payment);
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setSelectedPayment(null);
  };

  const handlePaymentSaved = (payment: Payment) => {
    if (selectedPayment) {
      onPaymentUpdated(payment);
    } else {
      onPaymentCreated(payment);
    }
    handleDialogClose();
  };

  const handleConfirm = (payment: Payment) => {
    setPaymentToConfirm(payment);
    setConfirmDialogOpen(true);
  };

  const handleConfirmPayment = async () => {
    if (!paymentToConfirm) return;

    try {
      const updatedPayment = await paymentService.confirmPayment(paymentToConfirm.id);
      onPaymentUpdated(updatedPayment);
      setConfirmDialogOpen(false);
      setPaymentToConfirm(null);
    } catch (error: any) {
      onError(error.message || 'Không thể xác nhận thanh toán');
    }
  };

  const handleDelete = (payment: Payment) => {
    setPaymentToDelete(payment);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!paymentToDelete) return;

    try {
      await paymentService.deletePayment(paymentToDelete.id);
      onPaymentDeleted(paymentToDelete.reference_number || paymentToDelete.id.toString());
      setDeleteDialogOpen(false);
      setPaymentToDelete(null);
    } catch (error: any) {
      onError(error.message || 'Không thể xóa thanh toán');
    }
  };

  const handleRefresh = () => {
    loadPayments();
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: 'payment_date',
      headerName: 'Ngày TT',
      width: 100,
      renderCell: (params) => (
        <Typography variant="body2">
          {new Date(params.value).toLocaleDateString('vi-VN')}
        </Typography>
      )
    },
    {
      field: 'customer_name',
      headerName: 'Khách hàng',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2">{params.value}</Typography>
          {params.row.customer_tax_code && (
            <Typography variant="caption" color="text.secondary">
              MST: {params.row.customer_tax_code}
            </Typography>
          )}
        </Box>
      )
    },
    {
      field: 'amount',
      headerName: 'Số tiền',
      width: 140,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {new Intl.NumberFormat('vi-VN').format(params.value)} VND
        </Typography>
      )
    },
    {
      field: 'payment_method',
      headerName: 'Phương thức',
      width: 130,
      renderCell: (params) => (
        <Chip
          label={params.value}
          variant="outlined"
          size="small"
          sx={{ fontSize: '0.75rem' }}
        />
      )
    },
    {
      field: 'reference_number',
      headerName: 'Số tham chiếu',
      width: 140,
      renderCell: (params) => (
        <Typography variant="body2" color="text.secondary">
          {params.value || '-'}
        </Typography>
      )
    },
    {
      field: 'status',
      headerName: 'Trạng thái',
      width: 110,
      renderCell: (params) => (
        <Chip
          label={paymentService.getStatusLabel(params.value)}
          color={paymentService.getStatusColor(params.value)}
          size="small"
          sx={{ fontSize: '0.75rem' }}
        />
      )
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Thao tác',
      width: 120,
      getActions: (params) => {
        const actions = [
          <GridActionsCellItem
            icon={<ViewIcon />}
            label="Xem"
            onClick={() => {}}
          />
        ];

        if (params.row.status === 'pending') {
          actions.push(
            <GridActionsCellItem
              icon={<ConfirmIcon />}
              label="Xác nhận"
              onClick={() => handleConfirm(params.row)}
            />
          );
        }

        if (params.row.status !== 'confirmed') {
          actions.push(
            <GridActionsCellItem
              icon={<EditIcon />}
              label="Sửa"
              onClick={() => handleEdit(params.row)}
            />
          );
        }

        actions.push(
          <GridActionsCellItem
            icon={<DeleteIcon />}
            label="Xóa"
            onClick={() => handleDelete(params.row)}
          />
        );

        return actions;
      }
    }
  ];

  return (
    <Box>
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              label="Tìm kiếm"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Số tham chiếu, mô tả..."
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              select
              label="Khách hàng"
              value={filters.customerId || ''}
              onChange={(e) => handleFilterChange('customerId', e.target.value ? parseInt(e.target.value) : undefined)}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="">Tất cả</MenuItem>
              {Array.isArray(customers) && customers.map((customer) => (
                <MenuItem key={customer.id} value={customer.id}>
                  {customer.name}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} sm={2}>
            <TextField
              fullWidth
              size="small"
              select
              label="Trạng thái"
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="">Tất cả</MenuItem>
              <MenuItem value="pending">Chờ xác nhận</MenuItem>
              <MenuItem value="confirmed">Đã xác nhận</MenuItem>
              <MenuItem value="cancelled">Đã hủy</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreate}
                sx={{ fontSize: '0.85rem', height: 40 }}
              >
                Thêm thanh toán
              </Button>
              <Tooltip title="Làm mới">
                <IconButton onClick={handleRefresh} sx={{ height: 40, width: 40 }}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={payments}
          columns={columns}
          loading={loading}
          pagination
          paginationMode="server"
          paginationModel={{
            page: pagination.page,
            pageSize: pagination.pageSize
          }}
          rowCount={pagination.total}
          onPaginationModelChange={(model) => {
            if (model.page !== pagination.page) {
              handlePageChange(model.page);
            }
            if (model.pageSize !== pagination.pageSize) {
              handlePageSizeChange(model.pageSize);
            }
          }}
          pageSizeOptions={[10, 25, 50, 100]}
          disableRowSelectionOnClick
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.85rem'
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontSize: '0.85rem',
              fontWeight: 600
            }
          }}
        />
      </Paper>

      {/* Dialogs */}
      <PaymentDialog
        open={dialogOpen}
        payment={selectedPayment}
        customers={customers}
        onClose={handleDialogClose}
        onSave={handlePaymentSaved}
        onError={onError}
      />

      <ConfirmDialog
        open={confirmDialogOpen}
        title="Xác nhận thanh toán"
        message={`Bạn có chắc chắn muốn xác nhận thanh toán "${paymentToConfirm?.reference_number || paymentToConfirm?.id}"?

Hành động này sẽ:
• Thực hiện phân bổ FIFO tự động vào các công nợ cũ nhất
• Không thể hoàn tác sau khi xác nhận
• Cập nhật trạng thái thanh toán thành "Đã xác nhận"`}
        onConfirm={handleConfirmPayment}
        onCancel={() => setConfirmDialogOpen(false)}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa"
        message={`Bạn có chắc chắn muốn xóa thanh toán "${paymentToDelete?.reference_number || paymentToDelete?.id}"?`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
      />
    </Box>
  );
};

export default PaymentsList;
