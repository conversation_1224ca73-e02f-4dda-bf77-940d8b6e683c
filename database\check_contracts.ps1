# PowerShell script kiem tra danh sach hop dong

Write-Host "=== KIEM TRA DANH SACH HOP DONG ===" -ForegroundColor Green
Write-Host ""

# Thiet lap bien moi truong
$env:PGPASSWORD = "110591"
$env:PGCLIENTENCODING = "UTF8"

# Duong dan database
$dbHost = "localhost"
$dbPort = "5432"
$dbName = "tinhtam-hp"
$dbUser = "postgres"

Write-Host "Ket noi database: $dbName" -ForegroundColor Yellow
Write-Host ""

# Kiem tra tong so hop dong
Write-Host "1. Tong so hop dong:" -ForegroundColor Cyan
try {
    $totalResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -t -c "SELECT COUNT(*) FROM contracts;" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   Tong cong: $($totalResult.Trim()) hop dong" -ForegroundColor Green
    } else {
        Write-Host "   Loi kiem tra: $totalResult" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   Khong the kiem tra: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Hien thi danh sach hop dong
Write-Host "2. Danh sach hop dong:" -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
    SELECT 
        ROW_NUMBER() OVER (ORDER BY co.id) as stt,
        co.contract_number as so_hop_dong,
        cu.short_name as ten_viet_tat,
        co.start_date as ngay_bat_dau,
        co.end_date as ngay_ket_thuc,
        CASE 
            WHEN co.status = 'active' THEN 'Hoat dong'
            WHEN co.status = 'expired' THEN 'Het han'
            WHEN co.status = 'terminated' THEN 'Da huy'
            ELSE co.status
        END as trang_thai
    FROM contracts co
    JOIN customers cu ON co.customer_id = cu.id
    ORDER BY co.id;
    "
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Hien thi thanh cong!" -ForegroundColor Green
    } else {
        Write-Host "Loi hien thi danh sach!" -ForegroundColor Red
    }
} catch {
    Write-Host "Khong the hien thi danh sach: $_" -ForegroundColor Red
}

Write-Host ""

# Kiem tra hop dong theo trang thai
Write-Host "3. Thong ke theo trang thai:" -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
    SELECT 
        CASE 
            WHEN status = 'active' THEN 'Hoat dong'
            WHEN status = 'expired' THEN 'Het han'
            WHEN status = 'terminated' THEN 'Da huy'
            ELSE status
        END as trang_thai,
        COUNT(*) as so_luong
    FROM contracts 
    GROUP BY status
    ORDER BY COUNT(*) DESC;
    "
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Thong ke thanh cong!" -ForegroundColor Green
    }
} catch {
    Write-Host "Khong the thong ke: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== HOAN THANH KIEM TRA ===" -ForegroundColor Green
Write-Host ""
Write-Host "Ghi chu:" -ForegroundColor Yellow
Write-Host "- Tat ca hop dong deu co thoi han tu 01/01/2025 den 31/12/2025"
Write-Host "- Tat ca hop dong deu dang hoat dong"
Write-Host "- Co the tao bang gia cho cac hop dong nay"
