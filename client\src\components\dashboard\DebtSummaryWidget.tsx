import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  useTheme
} from '@mui/material';
import {
  AccountBalance as DebtIcon,
  Warning as WarningIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { DebtSummary, OverdueCustomer } from '../../services/dashboardService';

interface DebtSummaryWidgetProps {
  debtSummary: DebtSummary | null;
  topOverdueCustomers: OverdueCustomer[] | null;
  loading?: boolean;
}

const DebtSummaryWidget: React.FC<DebtSummaryWidgetProps> = ({
  debtSummary,
  topOverdueCustomers,
  loading = false
}) => {
  const theme = useTheme();



  const formatCurrency = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : (numValue / 1000000).toFixed(1);
  };

  const formatNumber = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? '0' : numValue.toLocaleString('vi-VN');
  };

  const getOverduePercentage = (): number => {
    if (!debtSummary) return 0;
    const total = parseFloat(debtSummary.total_receivables);
    const overdue = parseFloat(debtSummary.total_overdue);
    if (total === 0) return 0;
    return (overdue / total) * 100;
  };

  const summaryStats = [
    {
      label: 'Tổng phải thu',
      value: `${debtSummary ? formatCurrency(debtSummary.total_receivables) : '0'} tr`,
      icon: <DebtIcon />,
      color: theme.palette.info.main
    },
    {
      label: 'Quá hạn',
      value: `${debtSummary ? formatCurrency(debtSummary.total_overdue) : '0'} tr`,
      icon: <WarningIcon />,
      color: theme.palette.error.main
    },
    {
      label: 'KH có nợ',
      value: debtSummary ? formatNumber(debtSummary.customers_with_debt) : '0',
      icon: <PersonIcon />,
      color: theme.palette.warning.main
    },
    {
      label: 'Ngày quá hạn TB',
      value: debtSummary ? `${parseFloat(debtSummary.avg_overdue_days).toFixed(0)} ngày` : '0 ngày',
      icon: <ScheduleIcon />,
      color: theme.palette.secondary.main
    }
  ];

  return (
    <Card
      elevation={2}
      className="laundry-hover-lift"
      sx={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: '1px solid rgba(239, 68, 68, 0.1)',
        borderRadius: 2,
        fontSize: '0.85rem'
      }}
    >
      <CardContent sx={{ p: 2.5 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2.5 }}>
          <DebtIcon 
            sx={{ 
              color: theme.palette.error.main, 
              mr: 1.5,
              fontSize: '1.5rem'
            }} 
          />
          <Typography
            variant="h6"
            component="h3"
            sx={{
              fontWeight: 600,
              color: theme.palette.error.main,
              fontSize: '1.1rem'
            }}
          >
            Tình hình công nợ
          </Typography>
        </Box>

        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              Đang tải dữ liệu...
            </Typography>
          </Box>
        ) : (
          <>
            {/* Summary Stats */}
            <Grid container spacing={2} sx={{ mb: 2.5 }}>
              {summaryStats.map((stat, index) => (
                <Grid item xs={6} key={index}>
                  <Box
                    sx={{
                      p: 1.5,
                      borderRadius: 1.5,
                      backgroundColor: `${stat.color}08`,
                      border: `1px solid ${stat.color}20`,
                      textAlign: 'center'
                    }}
                  >
                    <Box
                      sx={{
                        color: stat.color,
                        mb: 0.5,
                        display: 'flex',
                        justifyContent: 'center'
                      }}
                    >
                      {stat.icon}
                    </Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 700,
                        color: stat.color,
                        fontSize: '1rem',
                        mb: 0.5
                      }}
                    >
                      {stat.value}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ fontSize: '0.75rem' }}
                    >
                      {stat.label}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>

            {/* Overdue Percentage */}
            {debtSummary && parseFloat(debtSummary.total_receivables) > 0 && (
              <Box sx={{ mb: 2.5 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                    Tỷ lệ quá hạn
                  </Typography>
                  <Typography 
                    variant="caption" 
                    color={getOverduePercentage() > 10 ? 'error' : 'success'}
                    sx={{ fontSize: '0.75rem', fontWeight: 600 }}
                  >
                    {getOverduePercentage().toFixed(1)}%
                  </Typography>
                </Box>
                <Box
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: theme.palette.grey[200],
                    overflow: 'hidden'
                  }}
                >
                  <Box
                    sx={{
                      height: '100%',
                      width: `${getOverduePercentage()}%`,
                      backgroundColor: getOverduePercentage() > 10 ? theme.palette.error.main : theme.palette.success.main,
                      transition: 'width 0.3s ease'
                    }}
                  />
                </Box>
              </Box>
            )}

            <Divider sx={{ my: 2 }} />

            {/* Top Overdue Customers */}
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                mb: 1.5,
                fontSize: '0.9rem'
              }}
            >
              Top khách hàng nợ quá hạn
            </Typography>

            {topOverdueCustomers && topOverdueCustomers.length > 0 ? (
              <List sx={{ p: 0 }}>
                {topOverdueCustomers.slice(0, 3).map((customer, index) => (
                  <ListItem
                    key={customer.customer_id}
                    sx={{
                      px: 0,
                      py: 1,
                      borderBottom: index < Math.min(topOverdueCustomers.length, 3) - 1 ? '1px solid' : 'none',
                      borderColor: 'divider'
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: `${theme.palette.error.main}15`,
                          color: theme.palette.error.main,
                          width: 32,
                          height: 32,
                          fontSize: '0.8rem',
                          fontWeight: 700
                        }}
                      >
                        #{index + 1}
                      </Avatar>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontWeight: 600,
                              fontSize: '0.8rem',
                              flex: 1
                            }}
                          >
                            {customer.customer_name}
                          </Typography>
                          <Chip
                            label={`${formatCurrency(customer.overdue_amount)} tr`}
                            size="small"
                            color="error"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        </Box>
                      }
                      secondary={
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{ fontSize: '0.7rem' }}
                        >
                          {formatNumber(customer.overdue_invoices)} hóa đơn • {parseInt(customer.max_days_overdue) || 0} ngày
                        </Typography>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Box
                sx={{
                  textAlign: 'center',
                  py: 2,
                  color: 'text.secondary'
                }}
              >
                <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                  Không có khách hàng nào nợ quá hạn
                </Typography>
              </Box>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default DebtSummaryWidget;
