# Environment Configuration Example
# Copy this file to .env and update the values

# Database Configuration (Local Development)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tinhtam-hp
DB_USER=postgres
DB_PASSWORD=your_password_here

# Production Database (<PERSON><PERSON> will set DAT<PERSON>ASE_URL automatically)
# DATABASE_URL=postgres://username:password@hostname:port/database

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=8500
NODE_ENV=development

# Client URL (for CORS)
CLIENT_URL=http://localhost:5373

# Production Client URL (for Hero<PERSON>)
# CLIENT_URL=https://your-app-name.herokuapp.com
