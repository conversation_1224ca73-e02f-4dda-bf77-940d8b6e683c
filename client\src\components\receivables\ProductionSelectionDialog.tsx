import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  MenuItem,
  Box,
  Typography,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  Chip,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import {
  Search as SearchIcon,
  Link as LinkIcon,
  CheckCircleOutlined as ConfirmIcon,
  WarningAmberOutlined as WarningIcon
} from '@mui/icons-material';

// Services
import contractService from '../../services/contractService';
import { dailyProductionService } from '../../services/dailyProductionService';

// Components
import ProductionStatusChip from '../production/ProductionStatusChip';

// Types
import { ProductionStatus, DailyProduction } from '../../types/dailyProduction';
import { Contract } from '../../types/contract';

// Interface for search results (different from DailyProduction)
interface ProductionSearchResult {
  production_id: number;
  production_date: string;
  contract_id: number;
  quantity: string;
  unit_price: string;
  total_amount: number;
  status: ProductionStatus;
  notes: string;
  contract_code: string;
  contract_name: string;
  customer_id: number;
  customer_name: string;
  product_code: string;
  product_name: string;
  product_unit: string;
}

interface ProductionSelectionDialogProps {
  open: boolean;
  onClose: () => void;
  onCreateReceivable: (productionData: any) => void;
  onError: (error: string) => void;
}

const ProductionSelectionDialog: React.FC<ProductionSelectionDialogProps> = ({
  open,
  onClose,
  onCreateReceivable,
  onError
}) => {
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [confirming, setConfirming] = useState(false);
  const [hasSearched, setHasSearched] = useState(false); // Track if search has been performed
  
  // Filter states
  const [filters, setFilters] = useState({
    contractId: '',
    startDate: null as Date | null,
    endDate: null as Date | null,
    status: '' // Show all productions to allow warning display
  });

  // Production data
  const [productions, setProductions] = useState<ProductionSearchResult[]>([]);
  const [selectedProductions, setSelectedProductions] = useState<number[]>([]);

  // Load contracts
  const loadContracts = async () => {
    try {
      console.log('Loading contracts...');
      const response = await contractService.getContracts({ limit: 1000 });
      console.log('Contracts response:', response);

      // Handle different response structures
      let contractsArray = [];
      if (response.data && Array.isArray(response.data)) {
        contractsArray = response.data;
      } else if (response.contracts && Array.isArray(response.contracts)) {
        contractsArray = response.contracts;
      } else if (Array.isArray(response)) {
        contractsArray = response;
      }

      console.log('Contracts array:', contractsArray);
      setContracts(contractsArray);
    } catch (error: any) {
      console.error('Error loading contracts:', error);
      onError('Không thể tải danh sách hợp đồng');
      setContracts([]);
    }
  };

  useEffect(() => {
    if (open) {
      setLoading(true);
      loadContracts().finally(() => setLoading(false));
    }
  }, [open]);

  // Handle filter changes
  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Search productions
  const handleSearch = async () => {
    if (!filters.contractId) {
      onError('Vui lòng chọn hợp đồng');
      return;
    }

    try {
      setSearching(true);
      setHasSearched(true); // Mark that search has been performed

      // Format dates properly for API call
      const formatDateForAPI = (date: Date | null): string | undefined => {
        if (!date) return undefined;
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // Use the service layer for API call
      const response = await dailyProductionService.searchForReceivable(
        parseInt(filters.contractId),
        undefined, // status - let it show all to display warnings
        formatDateForAPI(filters.startDate),
        formatDateForAPI(filters.endDate)
      );

      if (response.success && response.data) {
        setProductions(response.data);
        setSelectedProductions([]);
      } else {
        throw new Error(response.message || 'Không thể tải dữ liệu sản lượng');
      }

    } catch (error: any) {
      console.error('Search error:', error);
      onError(error.message || 'Lỗi khi tìm kiếm sản lượng');
      setProductions([]);
      setSelectedProductions([]);
    } finally {
      setSearching(false);
    }
  };

  // Handle production selection
  const handleProductionSelect = (productionId: number, checked: boolean) => {
    setSelectedProductions(prev => {
      if (checked) {
        return [...prev, productionId];
      } else {
        return prev.filter(id => id !== productionId);
      }
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProductions(productions.map(p => p.production_id));
    } else {
      setSelectedProductions([]);
    }
  };

  // Calculate totals
  const selectedProductionRecords = productions.filter(p =>
    selectedProductions.includes(p.production_id)
  );
  const totalAmount = selectedProductionRecords.reduce((sum, p) => sum + (p.total_amount || 0), 0);

  // Handle bulk confirm productions
  const handleBulkConfirm = async () => {
    const draftProductions = selectedProductionRecords.filter(p => p.status === 'Mới tạo');
    if (draftProductions.length === 0) {
      onError('Không có sản lượng nào cần xác nhận');
      return;
    }

    try {
      setConfirming(true);
      const response = await dailyProductionService.bulkUpdateStatus({
        production_ids: draftProductions.map(p => p.production_id),
        new_status: 'Đã xác nhận'
      });

      if (response.success) {
        // Refresh production data
        await handleSearch();
        onError(`Đã xác nhận ${response.data?.updated_count || 0} sản lượng`);
      } else {
        onError('Không thể xác nhận sản lượng');
      }
    } catch (error: any) {
      onError(error.message || 'Lỗi khi xác nhận sản lượng');
    } finally {
      setConfirming(false);
    }
  };

  // Handle create receivable
  const handleCreateReceivable = () => {
    if (selectedProductions.length === 0) {
      onError('Vui lòng chọn ít nhất một bản ghi sản lượng');
      return;
    }

    // Check if any selected production is not confirmed
    const unconfirmedProductions = selectedProductionRecords.filter(p => p.status !== 'Đã xác nhận');
    if (unconfirmedProductions.length > 0) {
      onError(`Không thể tạo công nợ từ ${unconfirmedProductions.length} sản lượng chưa được xác nhận. Vui lòng xác nhận trước.`);
      return;
    }

    const selectedContract = contracts.find(c => c.id === parseInt(filters.contractId));
    if (!selectedContract) {
      onError('Không tìm thấy thông tin hợp đồng');
      return;
    }

    // Prepare data for receivable creation
    const productionData = {
      contractId: filters.contractId,
      contract: selectedContract,
      selectedProductions: selectedProductionRecords,
      totalAmount,
      dateRange: {
        startDate: filters.startDate,
        endDate: filters.endDate
      }
    };

    onCreateReceivable(productionData);
  };

  const handleClose = () => {
    setFilters({
      contractId: '',
      startDate: null,
      endDate: null,
      status: ''
    });
    setProductions([]);
    setSelectedProductions([]);
    setHasSearched(false); // Reset search state
    onClose();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog 
        open={open} 
        onClose={handleClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: '90vh' }
        }}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LinkIcon color="primary" />
            <Typography variant="h6">Tạo công nợ từ sản lượng</Typography>
          </Box>
        </DialogTitle>

        <DialogContent dividers>
          {/* Filters */}
          <Paper sx={{ p: 2, mb: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
              Bộ lọc tìm kiếm
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth size="small" required>
                  <InputLabel sx={{ fontSize: '0.85rem' }}>Hợp đồng</InputLabel>
                  <Select
                    value={filters.contractId}
                    onChange={(e) => handleFilterChange('contractId', e.target.value)}
                    label="Hợp đồng"
                    sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                        },
                      },
                    }}
                  >
                    <MenuItem value="">
                      {loading ? 'Đang tải...' : 'Chọn hợp đồng'}
                    </MenuItem>
                    {contracts.length > 0 ? (
                      contracts.map((contract) => (
                        <MenuItem key={contract.id} value={contract.id.toString()}>
                          {contract.contract_number} - {contract.contract_name}
                        </MenuItem>
                      ))
                    ) : (
                      !loading && (
                        <MenuItem disabled>
                          Không có hợp đồng nào
                        </MenuItem>
                      )
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <DatePicker
                  label="Từ ngày"
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      sx: { '& .MuiInputBase-input': { fontSize: '0.85rem' } }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <DatePicker
                  label="Đến ngày"
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  slotProps={{
                    textField: {
                      size: 'small',
                      fullWidth: true,
                      sx: { '& .MuiInputBase-input': { fontSize: '0.85rem' } }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={2}>
                <Button
                  fullWidth
                  variant="contained"
                  startIcon={<SearchIcon />}
                  onClick={handleSearch}
                  disabled={searching || !filters.contractId}
                  sx={{ fontSize: '0.85rem', height: 40 }}
                >
                  {searching ? 'Đang tìm...' : 'Tìm kiếm'}
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Results */}
          {productions.length > 0 && (
            <Paper sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                  Kết quả tìm kiếm ({productions.length} bản ghi)
                </Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedProductions.length === productions.length && productions.length > 0}
                      indeterminate={selectedProductions.length > 0 && selectedProductions.length < productions.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  }
                  label="Chọn tất cả"
                />
              </Box>

              <TableContainer sx={{ maxHeight: 300 }}>
                <Table size="small" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell padding="checkbox">Chọn</TableCell>
                      <TableCell>Ngày SX</TableCell>
                      <TableCell>Khách hàng</TableCell>
                      <TableCell>Hợp đồng</TableCell>
                      <TableCell align="right">Tổng tiền</TableCell>
                      <TableCell>Trạng thái</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {productions.map((production) => (
                      <TableRow key={production.production_id}>
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={selectedProductions.includes(production.production_id)}
                            onChange={(e) => handleProductionSelect(production.production_id, e.target.checked)}
                          />
                        </TableCell>
                        <TableCell>
                          {new Date(production.production_date).toLocaleDateString('vi-VN')}
                        </TableCell>
                        <TableCell>{production.customer_name}</TableCell>
                        <TableCell>{production.contract_code}</TableCell>
                        <TableCell align="right">
                          {new Intl.NumberFormat('vi-VN').format(production.total_amount || 0)}
                        </TableCell>
                        <TableCell>
                          <ProductionStatusChip status={production.status} size="small" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {selectedProductions.length > 0 && (
                <>
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      Đã chọn {selectedProductions.length} bản ghi.
                      Tổng giá trị: <strong>{new Intl.NumberFormat('vi-VN').format(totalAmount)} VND</strong>
                    </Typography>
                  </Alert>

                  {/* Warning for unconfirmed productions */}
                  {(() => {
                    const unconfirmedCount = selectedProductionRecords.filter(p => p.status === 'Mới tạo').length;
                    const invoicedCount = selectedProductionRecords.filter(p => p.status === 'Đã ghi nhận công nợ').length;

                    if (unconfirmedCount > 0 || invoicedCount > 0) {
                      return (
                        <Alert
                          severity="warning"
                          sx={{ mt: 1 }}
                          action={
                            unconfirmedCount > 0 ? (
                              <Button
                                color="inherit"
                                size="small"
                                startIcon={<ConfirmIcon />}
                                onClick={handleBulkConfirm}
                                disabled={confirming}
                                sx={{ fontSize: '0.75rem' }}
                              >
                                {confirming ? 'Đang xác nhận...' : `Xác nhận ngay (${unconfirmedCount})`}
                              </Button>
                            ) : null
                          }
                        >
                          <Typography variant="body2">
                            {unconfirmedCount > 0 && (
                              <>
                                <WarningIcon sx={{ fontSize: '1rem', mr: 0.5, verticalAlign: 'middle' }} />
                                {unconfirmedCount} sản lượng chưa được xác nhận, không thể tạo công nợ.
                              </>
                            )}
                            {invoicedCount > 0 && (
                              <>
                                {unconfirmedCount > 0 && <br />}
                                <WarningIcon sx={{ fontSize: '1rem', mr: 0.5, verticalAlign: 'middle' }} />
                                {invoicedCount} sản lượng đã được ghi nhận công nợ.
                              </>
                            )}
                          </Typography>
                        </Alert>
                      );
                    }
                    return null;
                  })()}
                </>
              )}
            </Paper>
          )}

          {productions.length === 0 && hasSearched && (
            <Alert severity="info">
              Không tìm thấy dữ liệu sản lượng phù hợp với điều kiện tìm kiếm.
            </Alert>
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2, gap: 1 }}>
          <Button
            onClick={handleClose}
            sx={{ fontSize: '0.85rem' }}
          >
            Hủy
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateReceivable}
            disabled={
              selectedProductions.length === 0 ||
              selectedProductionRecords.some(p => p.status !== 'Đã xác nhận')
            }
            sx={{ fontSize: '0.85rem' }}
          >
            Tạo công nợ ({selectedProductions.length})
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ProductionSelectionDialog;
