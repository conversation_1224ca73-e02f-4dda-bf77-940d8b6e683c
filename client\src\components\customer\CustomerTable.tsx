import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Typography,
  Box,
  CircularProgress,
  Paper,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import { Customer } from '../../types/customer';
import { formatDate } from '../../utils/responseUtils';
import PermissionCheck from '../permission/PermissionCheck';

/**
 * Customer Table Props Interface
 */
export interface CustomerTableProps {
  customers: Customer[];
  loading: boolean;
  searchTerm: string;
  onView: (customer: Customer) => void;
  onEdit: (customer: Customer) => void;
  onDelete: (customer: Customer) => void;
}

/**
 * Customer Table Component
 * Displays customers in a table format with actions
 */
const CustomerTable: React.FC<CustomerTableProps> = ({
  customers,
  loading,
  searchTerm,
  onView,
  onEdit,
  onDelete,
}) => {
  return (
    <Paper elevation={2}>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>ID</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Mã số thuế</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Tên khách hàng</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Tên viết tắt</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Người liên hệ</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Số điện thoại</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Email</TableCell>
              <TableCell sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Ngày tạo</TableCell>
              <TableCell align="center" sx={{ fontSize: '0.85rem', fontWeight: 600, py: 1.5 }}>Thao tác</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                  <CircularProgress size={40} />
                </TableCell>
              </TableRow>
            ) : customers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} align="center" sx={{ py: 4 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                    {searchTerm ? 'Không tìm thấy khách hàng nào' : 'Chưa có khách hàng nào'}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              customers.map((customer) => (
                <TableRow key={customer.id} hover>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.id}</TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>
                    {customer.tax_code ? (
                      <Chip
                        label={customer.tax_code}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.75rem', height: 24 }}
                      />
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>
                    <Typography variant="body2" sx={{ fontWeight: 500, fontSize: '0.8rem' }}>
                      {customer.name}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.short_name || '-'}</TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.contact_person || '-'}</TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.phone || '-'}</TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{customer.email || '-'}</TableCell>
                  <TableCell sx={{ fontSize: '0.8rem', py: 1.5 }}>{formatDate(customer.created_at)}</TableCell>
                  <TableCell align="center" sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                      <IconButton
                        size="small"
                        onClick={() => onView(customer)}
                        title="Xem chi tiết"
                        sx={{
                          '&:hover': {
                            backgroundColor: 'primary.light',
                            color: 'primary.contrastText',
                          },
                        }}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                      <PermissionCheck resourceType="customer" action="edit">
                        <IconButton
                          size="small"
                          onClick={() => onEdit(customer)}
                          title="Chỉnh sửa"
                          sx={{
                            '&:hover': {
                              backgroundColor: 'warning.light',
                              color: 'warning.contrastText',
                            },
                          }}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </PermissionCheck>
                      <PermissionCheck resourceType="customer" action="delete">
                        <IconButton
                          size="small"
                          onClick={() => onDelete(customer)}
                          title="Xóa"
                          color="error"
                          sx={{
                            '&:hover': {
                              backgroundColor: 'error.light',
                              color: 'error.contrastText',
                            },
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </PermissionCheck>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default CustomerTable;
