/**
 * Response utilities
 * <PERSON><PERSON><PERSON> hàm tiện ích để format response API theo chuẩn
 */

/**
 * Tạo response thành công
 * @param {*} data - Dữ liệu trả về
 * @param {Object} meta - Metadata (pagination, etc.)
 * @param {Object} links - Links (pagination links, etc.)
 * @returns {Object} Formatted response
 */
const createResponse = (data, meta = null, links = null) => {
  const response = {
    success: true,
    data
  };

  if (meta) {
    response.meta = meta;
  }

  if (links) {
    response.links = links;
  }

  return response;
};

/**
 * Tạo response lỗi
 * @param {string} code - Mã lỗi
 * @param {string} message - Thông điệp lỗi
 * @param {Array} details - Chi tiết lỗi
 * @param {number} statusCode - HTTP status code
 * @returns {Object} Formatted error response
 */
const createErrorResponse = (code, message, details = [], statusCode = 500) => {
  return {
    success: false,
    error: {
      code,
      message,
      details: Array.isArray(details) ? details : [details]
    },
    statusCode
  };
};

/**
 * Tạo metadata cho pagination
 * @param {number} page - Trang hiện tại
 * @param {number} limit - Số lượng items per page
 * @param {number} total - Tổng số items
 * @returns {Object} Pagination metadata
 */
const createPaginationMeta = (page, limit, total) => {
  const totalPages = Math.ceil(total / limit);
  
  return {
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: parseInt(total),
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

/**
 * Tạo pagination links
 * @param {Object} req - Express request object
 * @param {number} page - Trang hiện tại
 * @param {number} totalPages - Tổng số trang
 * @returns {Object} Pagination links
 */
const createPaginationLinks = (req, page, totalPages) => {
  const baseUrl = `${req.protocol}://${req.get('host')}${req.baseUrl}${req.path}`;
  const queryParams = { ...req.query };
  
  const createUrl = (pageNum) => {
    queryParams.page = pageNum;
    const searchParams = new URLSearchParams(queryParams);
    return `${baseUrl}?${searchParams.toString()}`;
  };

  const links = {
    self: createUrl(page),
    first: createUrl(1),
    last: createUrl(totalPages)
  };

  if (page > 1) {
    links.prev = createUrl(page - 1);
  }

  if (page < totalPages) {
    links.next = createUrl(page + 1);
  }

  return links;
};

/**
 * Xử lý query parameters cho pagination và filtering
 * @param {Object} req - Express request object
 * @returns {Object} Processed query parameters
 */
const processQueryParams = (req) => {
  const {
    page = 1,
    limit = 10,
    sort = 'created_at',
    order = 'DESC',
    sortBy,
    sortOrder,
    search = '',
    ...filters
  } = req.query;

  // Validate và sanitize parameters
  const pagination = {
    page: Math.max(1, parseInt(page) || 1),
    limit: Math.min(100, Math.max(1, parseInt(limit) || 10)) // Giới hạn tối đa 100 items per page
  };

  // Hỗ trợ cả sort/order và sortBy/sortOrder
  const finalSortBy = sortBy || sort || 'created_at';
  const finalSortOrder = sortOrder || order || 'DESC';

  const sorting = {
    sortBy: finalSortBy,
    sortOrder: ['ASC', 'DESC'].includes(finalSortOrder.toUpperCase()) ? finalSortOrder.toUpperCase() : 'DESC'
  };

  const searchFilters = {
    search: search.trim()
  };

  // Xử lý các filters khác
  const additionalFilters = {};
  Object.keys(filters).forEach(key => {
    if (filters[key] !== undefined && filters[key] !== '') {
      additionalFilters[key] = filters[key];
    }
  });

  return {
    pagination,
    sorting,
    filters: { ...searchFilters, ...additionalFilters }
  };
};

/**
 * Validate required fields
 * @param {Object} data - Dữ liệu cần validate
 * @param {Array} requiredFields - Danh sách các field bắt buộc
 * @returns {Array} Danh sách lỗi validation
 */
const validateRequiredFields = (data, requiredFields) => {
  const errors = [];
  
  requiredFields.forEach(field => {
    if (!data[field] || (typeof data[field] === 'string' && !data[field].trim())) {
      errors.push({
        field,
        message: `${field} là bắt buộc`
      });
    }
  });

  return errors;
};

/**
 * Validate email format
 * @param {string} email - Email cần validate
 * @returns {boolean} True nếu email hợp lệ
 */
const validateEmail = (email) => {
  if (!email) return true; // Email không bắt buộc
  
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (Vietnam)
 * @param {string} phone - Số điện thoại cần validate
 * @returns {boolean} True nếu số điện thoại hợp lệ
 */
const validatePhone = (phone) => {
  if (!phone) return true; // Phone không bắt buộc
  
  // Regex cho số điện thoại Việt Nam
  const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
};

/**
 * Sanitize string input
 * @param {string} str - Chuỗi cần sanitize
 * @returns {string} Chuỗi đã được sanitize
 */
const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  
  return str
    .trim()
    .replace(/\s+/g, ' ') // Thay thế nhiều khoảng trắng bằng 1 khoảng trắng
    .replace(/[<>]/g, ''); // Loại bỏ các ký tự có thể gây XSS
};

/**
 * Format date for response
 * @param {Date} date - Date object
 * @returns {string} Formatted date string
 */
const formatDate = (date) => {
  if (!date) return null;
  
  return new Date(date).toISOString();
};

/**
 * Tạo response cho list với pagination
 * @param {Array} items - Danh sách items
 * @param {Object} pagination - Thông tin pagination
 * @param {Object} req - Express request object
 * @returns {Object} Formatted response
 */
const createListResponse = (items, pagination, req) => {
  const meta = createPaginationMeta(
    pagination.page,
    pagination.limit,
    pagination.total || pagination.totalCount
  );

  const links = createPaginationLinks(
    req,
    pagination.page,
    pagination.totalPages
  );

  return createResponse(items, meta, links);
};

/**
 * Send success response
 * @param {Object} res - Express response object
 * @param {*} data - Data to send
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code
 * @returns {Object} Response
 */
const successResponse = (res, data, message = 'Success', statusCode = 200) => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

/**
 * Send error response
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {*} details - Error details
 * @returns {Object} Response
 */
const errorResponse = (res, message = 'Internal Server Error', statusCode = 500, details = null) => {
  const response = {
    success: false,
    message,
    error: {
      code: statusCode,
      message
    }
  };

  if (details) {
    response.error.details = details;
  }

  return res.status(statusCode).json(response);
};

module.exports = {
  createResponse,
  createErrorResponse,
  createPaginationMeta,
  createPaginationLinks,
  processQueryParams,
  validateRequiredFields,
  validateEmail,
  validatePhone,
  sanitizeString,
  formatDate,
  createListResponse,
  successResponse,
  errorResponse
};
