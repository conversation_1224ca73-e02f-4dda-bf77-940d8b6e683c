/**
 * Response Utilities
 * Utility functions for handling API responses and data processing
 */

import { ApiResponse } from '../services/api';

/**
 * Extract data from API response
 */
export const extractData = <T>(response: ApiResponse<T>): T => {
  return response.data;
};

/**
 * Extract pagination info from API response
 */
export const extractPagination = (response: ApiResponse<any>) => {
  return response.meta?.pagination || null;
};

/**
 * Extract links from API response
 */
export const extractLinks = (response: ApiResponse<any>) => {
  return response.links || null;
};

/**
 * Check if API response is successful
 */
export const isSuccessResponse = (response: ApiResponse<any>): boolean => {
  return response.success === true;
};

/**
 * Format error message from API error
 */
export const formatErrorMessage = (error: any): string => {
  if (error?.message) {
    return error.message;
  }
  
  if (error?.response?.data?.error?.message) {
    return error.response.data.error.message;
  }
  
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return 'Có lỗi xảy ra, vui lòng thử lại';
};

/**
 * Format error details from API error
 */
export const formatErrorDetails = (error: any): string[] => {
  if (error?.details && Array.isArray(error.details)) {
    return error.details;
  }
  
  if (error?.response?.data?.error?.details && Array.isArray(error.response.data.error.details)) {
    return error.response.data.error.details;
  }
  
  return [];
};

/**
 * Get error code from API error
 */
export const getErrorCode = (error: any): string | null => {
  if (error?.code) {
    return error.code;
  }
  
  if (error?.response?.data?.error?.code) {
    return error.response.data.error.code;
  }
  
  return null;
};

/**
 * Check if error is a network error
 */
export const isNetworkError = (error: any): boolean => {
  return !error?.response && error?.request;
};

/**
 * Check if error is a timeout error
 */
export const isTimeoutError = (error: any): boolean => {
  return error?.code === 'ECONNABORTED' || error?.message?.includes('timeout');
};

/**
 * Check if error is a validation error
 */
export const isValidationError = (error: any): boolean => {
  const errorCode = getErrorCode(error);
  return errorCode === 'VALIDATION_ERROR' || error?.status === 400;
};

/**
 * Check if error is an authentication error
 */
export const isAuthError = (error: any): boolean => {
  return error?.status === 401 || error?.response?.status === 401;
};

/**
 * Check if error is a permission error
 */
export const isPermissionError = (error: any): boolean => {
  return error?.status === 403 || error?.response?.status === 403;
};

/**
 * Check if error is a not found error
 */
export const isNotFoundError = (error: any): boolean => {
  return error?.status === 404 || error?.response?.status === 404;
};

/**
 * Check if error is a server error
 */
export const isServerError = (error: any): boolean => {
  const status = error?.status || error?.response?.status;
  return status >= 500 && status < 600;
};

/**
 * Format date for display
 */
export const formatDate = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('vi-VN');
  } catch (error) {
    return '';
  }
};

/**
 * Format datetime for display
 */
export const formatDateTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString('vi-VN');
  } catch (error) {
    return '';
  }
};

/**
 * Format time for display
 */
export const formatTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleTimeString('vi-VN');
  } catch (error) {
    return '';
  }
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMinutes < 1) {
      return 'Vừa xong';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} phút trước`;
    } else if (diffHours < 24) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 7) {
      return `${diffDays} ngày trước`;
    } else {
      return formatDate(dateObj);
    }
  } catch (error) {
    return '';
  }
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format number with thousand separators
 */
export const formatNumber = (num: number | string | null | undefined): string => {
  if (num === null || num === undefined || num === '') return '';
  
  const number = typeof num === 'string' ? parseFloat(num) : num;
  if (isNaN(number)) return '';
  
  return number.toLocaleString('vi-VN');
};

/**
 * Format currency (VND)
 */
export const formatCurrency = (amount: number | string | null | undefined): string => {
  if (amount === null || amount === undefined || amount === '') return '';
  
  const number = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(number)) return '';
  
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(number);
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string | null | undefined, maxLength: number): string => {
  if (!text) return '';
  
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength) + '...';
};

/**
 * Capitalize first letter
 */
export const capitalizeFirst = (text: string | null | undefined): string => {
  if (!text) return '';
  
  return text.charAt(0).toUpperCase() + text.slice(1);
};

/**
 * Convert to title case
 */
export const toTitleCase = (text: string | null | undefined): string => {
  if (!text) return '';
  
  return text.replace(/\w\S*/g, (txt) => 
    txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

/**
 * Remove HTML tags from text
 */
export const stripHtml = (html: string | null | undefined): string => {
  if (!html) return '';
  
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

/**
 * Generate random ID
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Debounce function
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
