import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend } from 'recharts';

// Services
import reportService from '../../services/reportService';

// Types
import { DashboardData, ChartDataPoint } from '../../types/report';

interface DebtSummaryProps {
  refreshTrigger: number;
  onError: (error: string) => void;
}

const DebtSummary: React.FC<DebtSummaryProps> = ({
  refreshTrigger,
  onError
}) => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(false);

  // Load data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await reportService.getDashboardData();
      setDashboardData(data);
    } catch (error: any) {
      onError(error.message || 'Không thể tải dữ liệu tổng hợp');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, [refreshTrigger]);

  const handleRefresh = () => {
    loadDashboardData();
  };

  if (!dashboardData) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <Typography>Đang tải dữ liệu...</Typography>
      </Box>
    );
  }

  // Prepare chart data
  const agingChartData = reportService.createAgingChartData(dashboardData.overall_summary);
  
  const barChartData = [
    {
      name: 'Chưa đến hạn',
      amount: dashboardData.overall_summary.total_current || 0,
      percentage: dashboardData.overall_summary.current_percentage || 0
    },
    {
      name: '1-30 ngày',
      amount: dashboardData.overall_summary.total_1_30 || 0,
      percentage: dashboardData.overall_summary.days_1_30_percentage || 0
    },
    {
      name: '31-60 ngày',
      amount: dashboardData.overall_summary.total_31_60 || 0,
      percentage: dashboardData.overall_summary.days_31_60_percentage || 0
    },
    {
      name: '61-90 ngày',
      amount: dashboardData.overall_summary.total_61_90 || 0,
      percentage: dashboardData.overall_summary.days_61_90_percentage || 0
    },
    {
      name: '>90 ngày',
      amount: dashboardData.overall_summary.total_over_90 || 0,
      percentage: dashboardData.overall_summary.over_90_days_percentage || 0
    }
  ].filter(item => item.amount > 0);

  const COLORS = ['#4caf50', '#ff9800', '#f44336', '#9c27b0', '#607d8b'];

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Tổng hợp Công nợ Tổng thể
          </Typography>
        </Box>
        <Tooltip title="Làm mới">
          <IconButton onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5" color="primary" sx={{ fontWeight: 600 }}>
                    {dashboardData.overall_summary.customers_with_debt}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Khách hàng có nợ
                  </Typography>
                </Box>
                <TrendingUpIcon color="primary" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5" color="error" sx={{ fontWeight: 600 }}>
                    {reportService.formatCurrency(dashboardData.overall_summary.total_outstanding)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tổng công nợ
                  </Typography>
                </Box>
                <TrendingUpIcon color="error" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5" color="warning.main" sx={{ fontWeight: 600 }}>
                    {dashboardData.overall_summary.customers_with_overdue}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    KH có nợ quá hạn
                  </Typography>
                </Box>
                <TrendingDownIcon color="warning" />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h5" color="info.main" sx={{ fontWeight: 600 }}>
                    {dashboardData.summary_stats.avg_overdue_percentage}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    % Quá hạn TB
                  </Typography>
                </Box>
                <AssessmentIcon color="info" />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3}>
        {/* Pie Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Phân bố Tuổi nợ
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={agingChartData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name}: ${percentage.toFixed(1)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {agingChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <RechartsTooltip 
                  formatter={(value: any) => [reportService.formatCurrency(value), 'Số tiền']}
                />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Bar Chart */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, height: 400 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Số tiền theo Tuổi nợ
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={barChartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis 
                  tick={{ fontSize: 12 }}
                  tickFormatter={(value) => `${(value / 1000000).toFixed(0)}M`}
                />
                <RechartsTooltip 
                  formatter={(value: any) => [reportService.formatCurrency(value), 'Số tiền']}
                />
                <Bar dataKey="amount" fill="#8884d8">
                  {barChartData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Top Debtors */}
      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Top 10 Khách hàng có Nợ nhiều nhất
        </Typography>
        <Grid container spacing={2}>
          {dashboardData.top_debtors.slice(0, 10).map((debtor, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={debtor.customer_id}>
              <Card variant="outlined">
                <CardContent sx={{ py: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" color="primary" sx={{ mr: 1 }}>
                      #{index + 1}
                    </Typography>
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {debtor.customer_name}
                    </Typography>
                  </Box>
                  <Typography variant="h6" color="error" sx={{ fontWeight: 600 }}>
                    {reportService.formatCurrency(debtor.total_outstanding)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Quá hạn: {reportService.formatPercentage(debtor.overdue_percentage)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );
};

export default DebtSummary;
