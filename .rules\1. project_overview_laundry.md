# Laundry Management System Project Overview

## Project Structure
The project follows a traditional full-stack architecture with clear separation between frontend and backend:

```
app-giat-la-v2/
├── client/                 - React TypeScript Frontend
│   ├── public/            - Static assets
│   └── src/               - Source code
│       ├── components/    - Reusable React components
│       ├── contexts/      - React Context API
│       ├── pages/         - Page components
│       ├── services/      - API service layer
│       ├── types/         - TypeScript type definitions
│       └── utils/         - Utility functions
├── server/                - Node.js Express Backend
│   └── src/
│       ├── controllers/   - Route controllers (business logic)
│       ├── middleware/    - Express middleware
│       ├── models/        - Database models (PostgreSQL)
│       ├── routes/        - API route definitions
│       └── utils/         - Backend utility functions
├── database/              - Database scripts and migrations
└── docs/                  - Documentation
```

## Key Technologies & Patterns

1. **Frontend Stack**:
   - React 18 with TypeScript for type safety
   - Material-UI (MUI) v7+ for component library
   - Framer Motion for animations
   - Axios for API communication
   - React Router for navigation
   
2. **Backend Stack**:
   - Node.js with Express framework
   - PostgreSQL database with pg-promise
   - JWT authentication with role-based access
   - RESTful API design with versioning (/api/v1)
   - Centralized error handling

3. **Database Design**:
   - PostgreSQL with relational schema
   - Core entities: customers, products, contracts, contract_prices, daily_production
   - Support for receivables and payments management
   - Audit trails with created_by/updated_by tracking

## Core Domain Models

1. **Customer Management**:
   - Customer entity with tax_code, name, short_name, contact details
   - Support for active/inactive status
   
```javascript
// Example: Customer model structure
{
  id: number,
  tax_code: string,
  name: string,
  short_name: string,
  address: string,
  contact_person: string,
  phone: string,
  email: string,
  is_active: boolean
}
```

2. **Product Management**:
   - Product catalog with auto-generated codes (SP001, SP002)
   - Unit types: kg, piece, set, meter
   - Active/inactive status management
   
```javascript
// Example: Product model structure
{
  id: number,
  code: string,        // Auto-generated: SP001, SP002
  name: string,
  description: string,
  unit_type: string,   // kg, piece, set, meter
  is_active: boolean
}
```

3. **Contract Management**:
   - Contract hierarchy: Customer > Contract > Contract Prices > Daily Production
   - Contract lifecycle: active, paused, terminated, expired
   - Date-based validity with start_date and end_date
   
```javascript
// Example: Contract model structure
{
  id: number,
  contract_number: string,  // HD001/2024
  customer_id: number,
  contract_name: string,
  start_date: date,
  end_date: date,
  status: string,          // active, paused, terminated, expired
  short_name: string       // Auto-populated from customer
}
```

4. **Pricing System**:
   - Contract-specific pricing with effective dates
   - Price history tracking
   - Vietnamese currency formatting (1.000.000 VND)
   
```javascript
// Example: Contract Price model structure
{
  id: number,
  contract_id: number,
  product_id: number,
  price: decimal,
  effective_date: date,
  expiry_date: date,
  is_active: boolean
}
```

5. **Production Tracking**:
   - Daily production records with date, contract, product, quantity
   - Real-time pricing from contract_prices
   - Automatic total_amount calculation
   
```javascript
// Example: Daily Production model structure
{
  id: number,
  production_date: date,
  contract_id: number,
  product_id: number,
  quantity: decimal,
  unit_price: decimal,
  total_amount: decimal,
  status: string,
  notes: string
}
```

## Key Features & Workflows

1. **Contract-Centric Workflow**:
   ```
   Customer Creation → Contract Setup → Price Configuration → Production Entry → Reporting
   ```

2. **Quick Production Entry**:
   - Auto-populate products with contract prices
   - Excel-like table interface for quantity input
   - Real-time total calculation with Vietnamese formatting

3. **Pricing Management**:
   - Quick price update forms
   - Contract-based price filtering
   - Historical price tracking

4. **Reporting System**:
   - Production confirmation reports
   - Excel export with Vietnamese formatting
   - Date range and contract filtering
   - Summary statistics (Tổng chưa VAT, VAT, Tổng có VAT)

## UI/UX Patterns

1. **Vietnamese Localization**:
   - Date format: dd/mm/yyyy
   - Number format: 1.000.000 (thousand separators)
   - Currency: VND with proper formatting

2. **Responsive Design**:
   - Optimized for laptop screens
   - Compact layouts to minimize scrolling
   - Consistent button heights and form layouts

3. **Navigation Structure**:
   - Hierarchical sidebar with Contract Management as parent
   - Pricing and Production as child nodes
   - Dashboard with key metrics

4. **Form Patterns**:
   - Autocomplete dropdowns for selections
   - Real-time validation and formatting
   - Loading states and error handling

## Infrastructure Highlights

1. **API Design**:
   - RESTful endpoints with consistent response format
   - Multiple route compatibility (/api/v1, /api, direct routes)
   - Comprehensive error handling with Vietnamese messages

2. **Database Patterns**:
   - Relational integrity with foreign keys
   - Audit trails on all major entities
   - Optimized queries with proper indexing

3. **Security**:
   - JWT-based authentication
   - Role-based access control
   - CORS configuration for multiple environments

4. **Deployment**:
   - Heroku-ready configuration
   - Environment-based settings
   - Production build optimization

## Development Conventions

1. **Code Organization**:
   - Modular structure with clear separation of concerns
   - TypeScript for type safety
   - Consistent naming conventions

2. **API Conventions**:
   - RESTful resource naming
   - Consistent response format with success/error structure
   - Proper HTTP status codes

3. **Database Conventions**:
   - Snake_case for column names
   - Consistent foreign key naming
   - Proper constraints and indexes

4. **UI Conventions**:
   - MUI component library usage
   - Consistent spacing and typography
   - Vietnamese language support

## Getting Started Tips

1. Start with understanding the contract-centric workflow
2. Review the database schema in IMPLEMENTATION_PLAN.md
3. Follow the existing patterns for new features
4. Use Vietnamese formatting for all monetary values
5. Maintain consistency with existing UI patterns
6. Test with real Vietnamese business scenarios

## Important Dependencies
- React + TypeScript + MUI for frontend
- Node.js + Express + PostgreSQL for backend
- JWT for authentication
- Axios for API communication
- Framer Motion for animations

This structure provides a solid foundation for a Vietnamese laundry management system with focus on contract management, production tracking, and financial reporting.
