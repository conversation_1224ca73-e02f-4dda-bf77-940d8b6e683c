const bcrypt = require('bcrypt');
const { pool } = require('./src/db');

async function updatePasswords() {
  const password = '123456';
  const saltRounds = 10;

  try {
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    console.log('Password:', password);
    console.log('Hashed:', hashedPassword);

    // Test the hash
    const isValid = await bcrypt.compare(password, hashedPassword);
    console.log('Validation test:', isValid);

    // Update passwords in database
    const updateQuery = 'UPDATE users SET password = $1 WHERE email IN ($2, $3)';
    const result = await pool.query(updateQuery, [
      hashedPassword,
      '<EMAIL>',
      '<EMAIL>'
    ]);

    console.log('Updated rows:', result.rowCount);

    // Verify update
    const verifyQuery = 'SELECT email, LENGTH(password) as password_length FROM users';
    const verifyResult = await pool.query(verifyQuery);
    console.log('Password lengths after update:');
    verifyResult.rows.forEach(row => {
      console.log(`${row.email}: ${row.password_length} characters`);
    });

    // Test login with updated password
    const testQuery = 'SELECT email, password FROM users WHERE email = $1';
    const testResult = await pool.query(testQuery, ['<EMAIL>']);

    if (testResult.rows.length > 0) {
      const user = testResult.rows[0];
      const loginTest = await bcrypt.compare(password, user.password);
      console.log('Login <NAME_EMAIL>:', loginTest);
    }

    process.exit(0);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

updatePasswords();
