# Customer Management App

Ứng dụng quản lý khách hàng được xây dựng dựa trên codebase IVC Audit App với React, Node.js và PostgreSQL.

## Tính năng

### Quản lý khách hàng
- <PERSON>em danh sách khách hàng (c<PERSON> <PERSON>h<PERSON>rang, t<PERSON><PERSON> kiế<PERSON>, sắ<PERSON> xếp)
- <PERSON>h<PERSON><PERSON> khách hàng mới
- Chỉnh sửa thông tin khách hàng
- Xóa khách hàng
- Xem chi tiết khách hàng

### Thông tin khách hàng
- <PERSON><PERSON> số thuế (MST)
- Tên khách hàng
- Tên viết tắt (Shortname)
- Địa chỉ
- Thông tin người liên hệ

## Công nghệ sử dụng

### Frontend
- React + TypeScript
- Material-UI (MUI)
- React Router
- Axios
- Vite

### Backend
- Node.js + Express
- PostgreSQL
- JWT (JSON Web Tokens)
- Bcrypt

## Cài đặt và chạy

### Yêu cầu
- Node.js (v18+)
- PostgreSQL (v12+)

### Cài đặt cơ sở dữ liệu
1. Tạo cơ sở dữ liệu PostgreSQL mới:
```sql
CREATE DATABASE "tinhtam-hp";
```

2. Chạy script tạo bảng:
```bash
psql -U postgres -d tinhtam-hp -f database/schema.sql
```

3. (Tùy chọn) Thêm dữ liệu mẫu:
```bash
psql -U postgres -d tinhtam-hp -f database/sample-data.sql
```

### Cài đặt dependencies
```bash
npm run install-deps
```

### Cấu hình môi trường
1. Copy file `.env.example` thành `.env` trong thư mục `server/`:
```bash
cp server/.env.example server/.env
```

2. Cập nhật thông tin database trong `server/.env`:
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tinhtam-hp
DB_USER=postgres
DB_PASSWORD=110591
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
PORT=5000
CLIENT_URL=http://localhost:5173
```

3. (Tùy chọn) Tạo file `.env` cho client:
```bash
cp client/.env.example client/.env
```

### Chạy ứng dụng

#### Development mode (chạy cả client và server)
```bash
npm run dev
```

#### Chạy riêng từng phần
```bash
# Chỉ chạy server
npm run server

# Chỉ chạy client
npm run client
```

#### Production mode
```bash
npm run build
npm start
```

## Tài khoản test

Sau khi chạy script `database/sample-data.sql`, bạn có thể đăng nhập với:

- **Email:** <EMAIL>
- **Mật khẩu:** 123456

*Lưu ý: Đây là tài khoản test, hãy thay đổi mật khẩu trong môi trường production.*

## Cấu trúc dự án

```
customer-management-app/
├── client/                 # Frontend React
│   ├── public/             # Tài nguyên tĩnh
│   └── src/                # Mã nguồn
│       ├── components/     # Components React
│       ├── contexts/       # Context API
│       ├── pages/          # Các trang
│       ├── services/       # API services
│       ├── types/          # TypeScript types
│       └── utils/          # Utility functions
├── server/                 # Backend Node.js
│   └── src/
│       ├── controllers/    # Route controllers
│       ├── middleware/     # Express middleware
│       ├── models/         # Database models
│       ├── routes/         # API routes
│       └── utils/          # Utility functions
├── database/               # Database scripts
└── docs/                   # Documentation
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - Đăng nhập
- `POST /api/v1/auth/logout` - Đăng xuất

### Customers
- `GET /api/v1/customers` - Lấy danh sách khách hàng
- `GET /api/v1/customers/:id` - Lấy thông tin khách hàng theo ID
- `POST /api/v1/customers` - Tạo khách hàng mới
- `PUT /api/v1/customers/:id` - Cập nhật khách hàng
- `DELETE /api/v1/customers/:id` - Xóa khách hàng

## License

ISC
