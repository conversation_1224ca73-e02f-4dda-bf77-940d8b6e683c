/**
 * Daily Production Type Definitions
 */

/**
 * Daily Production Interface
 */
export interface DailyProduction {
  id: number;
  production_date: string;
  contract_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_amount: number;
  status: ProductionStatus;
  notes?: string | null;
  created_by?: number;
  updated_by?: number;
  created_at: string;
  updated_at: string;
  created_by_name?: string;
  // Joined fields
  contract_number?: string;
  contract_name?: string;
  customer_name?: string;
  customer_short_name?: string;
  product_code?: string;
  product_name?: string;
  product_unit_type?: string;
}

// Production Status Types
export type ProductionStatus = 'Mới tạo' | 'Đã xác nhận' | 'Đã ghi nhận công nợ';

export interface ProductionStatusConfig {
  label: string;
  color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  bgColor: string;
  textColor: string;
  icon?: string;
}

export const PRODUCTION_STATUS_CONFIG: Record<ProductionStatus, ProductionStatusConfig> = {
  'Mới tạo': {
    label: 'Mới tạo',
    color: 'default',
    bgColor: '#f5f5f5',
    textColor: '#666666',
    icon: 'draft'
  },
  'Đã xác nhận': {
    label: 'Đã xác nhận',
    color: 'success',
    bgColor: '#e8f5e8',
    textColor: '#2e7d32',
    icon: 'check_circle'
  },
  'Đã ghi nhận công nợ': {
    label: 'Đã ghi nhận công nợ',
    color: 'info',
    bgColor: '#e3f2fd',
    textColor: '#1976d2',
    icon: 'receipt'
  }
};

/**
 * Daily Production Create Request Interface
 */
export interface DailyProductionCreateRequest {
  production_date: string;
  contract_id: number;
  product_id: number;
  quantity: number;
  unit_price?: number;
  notes?: string;
  auto_get_price?: boolean;
}

/**
 * Daily Production Update Request Interface
 */
export interface DailyProductionUpdateRequest {
  production_date: string;
  contract_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  notes?: string;
}

/**
 * Bulk Production Item Interface
 */
export interface BulkProductionItem {
  product_id: number;
  quantity: number;
  unit_price?: number;
  notes?: string;
}

/**
 * Bulk Production Create Request Interface
 */
export interface BulkProductionCreateRequest {
  production_date: string;
  contract_id: number;
  items: BulkProductionItem[];
  auto_get_price?: boolean;
}

/**
 * Bulk Production Result Interface
 */
export interface BulkProductionResult {
  created_count: number;
  total_amount: number;
  items: DailyProduction[];
}

/**
 * Daily Production Search/Filter Options Interface
 */
export interface DailyProductionFilterOptions {
  page?: number;
  limit?: number;
  production_date?: string;
  contract_id?: number;
  product_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  status?: string;
  sortBy?: DailyProductionSortField;
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Daily Production Sort Fields
 */
export type DailyProductionSortField =
  | 'id'
  | 'production_date'
  | 'quantity'
  | 'unit_price'
  | 'total_amount'
  | 'created_at';

/**
 * Daily Production Sort Options for UI
 */
export const DAILY_PRODUCTION_SORT_OPTIONS: Array<{
  value: DailyProductionSortField;
  label: string;
}> = [
  { value: 'production_date', label: 'Ngày sản xuất' },
  { value: 'quantity', label: 'Số lượng' },
  { value: 'unit_price', label: 'Đơn giá' },
  { value: 'total_amount', label: 'Thành tiền' },
  { value: 'created_at', label: 'Ngày tạo' },
];

/**
 * Daily Production Form Data Interface (for forms)
 */
export interface DailyProductionFormData {
  production_date: string;
  contract_id: number | '';
  product_id: number | '';
  quantity: number | '';
  unit_price: number | '';
  notes: string;
  auto_get_price: boolean;
}

/**
 * Bulk Production Form Data Interface
 */
export interface BulkProductionFormData {
  production_date: string;
  contract_id: number | '';
  auto_get_price: boolean;
  items: Array<{
    product_id: number | '';
    quantity: number | '';
    unit_price: number | '';
    notes: string;
  }>;
}

/**
 * Daily Production Validation Error Interface
 */
export interface DailyProductionValidationError {
  field: string;
  message: string;
}

/**
 * Daily Production Validation Result Interface
 */
export interface DailyProductionValidationResult {
  isValid: boolean;
  errors: DailyProductionValidationError[];
}

/**
 * Daily Production Table Column Interface
 */
export interface DailyProductionTableColumn {
  id: keyof DailyProduction | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

/**
 * Daily Production Table Columns Configuration
 */
export const DAILY_PRODUCTION_TABLE_COLUMNS: DailyProductionTableColumn[] = [
  {
    id: 'production_date',
    label: 'Ngày sản xuất',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'contract_number',
    label: 'Số hợp đồng',
    minWidth: 140,
    sortable: false,
  },
  {
    id: 'customer_short_name',
    label: 'Khách hàng',
    minWidth: 120,
    sortable: false,
  },
  {
    id: 'product_name',
    label: 'Sản phẩm',
    minWidth: 180,
    sortable: false,
  },
  {
    id: 'quantity',
    label: 'Số lượng',
    minWidth: 100,
    align: 'right',
    sortable: true,
    format: (value: number) => new Intl.NumberFormat('vi-VN', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 3
    }).format(value),
  },
  {
    id: 'unit_price',
    label: 'Đơn giá',
    minWidth: 120,
    align: 'right',
    sortable: true,
    format: (value: number) => new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value),
  },
  {
    id: 'total_amount',
    label: 'Thành tiền',
    minWidth: 140,
    align: 'right',
    sortable: true,
    format: (value: number) => new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(value),
  },
  {
    id: 'created_at',
    label: 'Ngày tạo',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'actions',
    label: 'Thao tác',
    minWidth: 120,
    align: 'center',
    sortable: false,
  },
];

/**
 * Production Summary Interface
 */
export interface ProductionSummary {
  total_quantity: number;
  total_amount: number;
}

/**
 * Production Report Summary Interface
 */
export interface ProductionReportSummary {
  total_records: number;
  total_contracts: number;
  total_products: number;
  total_quantity: number;
  total_amount: number;
  avg_amount_per_day: number;
}

/**
 * Production Report by Product Interface
 */
export interface ProductionReportByProduct {
  product_code: string;
  product_name: string;
  unit_type: string;
  total_quantity: number;
  total_amount: number;
  avg_unit_price: number;
  production_days: number;
}

/**
 * Production Report by Contract Interface
 */
export interface ProductionReportByContract {
  contract_number: string;
  contract_name: string;
  customer_name: string;
  total_quantity: number;
  total_amount: number;
  total_products: number;
  production_days: number;
}

/**
 * Production Report by Date Interface
 */
export interface ProductionReportByDate {
  production_date: string;
  total_records: number;
  total_quantity: number;
  total_amount: number;
}

/**
 * Production Report Interface
 */
export interface ProductionReport {
  summary: ProductionReportSummary;
  by_product: ProductionReportByProduct[];
  by_contract: ProductionReportByContract[];
  by_date: ProductionReportByDate[];
}

/**
 * Production Report Request Interface
 */
export interface ProductionReportRequest {
  date_from: string;
  date_to: string;
  contract_id?: number;
}

/**
 * Daily Production Error Types
 */
export type DailyProductionErrorType =
  | 'PRODUCTION_NOT_FOUND'
  | 'PRODUCTION_EXISTS'
  | 'CONTRACT_NOT_FOUND'
  | 'PRODUCT_NOT_FOUND'
  | 'PRICE_NOT_FOUND'
  | 'INVALID_QUANTITY'
  | 'INVALID_PRICE'
  | 'INVALID_DATE'
  | 'VALIDATION_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Daily Production Error Interface
 */
export interface DailyProductionError {
  type: DailyProductionErrorType;
  message: string;
  details?: string[];
}

/**
 * Daily Production Action Types for State Management
 */
export type DailyProductionActionType =
  | 'FETCH_PRODUCTIONS_START'
  | 'FETCH_PRODUCTIONS_SUCCESS'
  | 'FETCH_PRODUCTIONS_FAILURE'
  | 'FETCH_PRODUCTION_REPORT_START'
  | 'FETCH_PRODUCTION_REPORT_SUCCESS'
  | 'FETCH_PRODUCTION_REPORT_FAILURE'
  | 'CREATE_PRODUCTION_START'
  | 'CREATE_PRODUCTION_SUCCESS'
  | 'CREATE_PRODUCTION_FAILURE'
  | 'CREATE_BULK_PRODUCTION_START'
  | 'CREATE_BULK_PRODUCTION_SUCCESS'
  | 'CREATE_BULK_PRODUCTION_FAILURE'
  | 'UPDATE_PRODUCTION_START'
  | 'UPDATE_PRODUCTION_SUCCESS'
  | 'UPDATE_PRODUCTION_FAILURE'
  | 'DELETE_PRODUCTION_START'
  | 'DELETE_PRODUCTION_SUCCESS'
  | 'DELETE_PRODUCTION_FAILURE'
  | 'SET_SELECTED_PRODUCTION'
  | 'CLEAR_SELECTED_PRODUCTION'
  | 'CLEAR_ERROR';

/**
 * Daily Production State Interface for Context/Redux
 */
export interface DailyProductionState {
  productions: DailyProduction[];
  selectedProduction: DailyProduction | null;
  report: ProductionReport | null;
  loading: boolean;
  error: DailyProductionError | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  summary: ProductionSummary | null;
}

/**
 * Daily Production Context Type
 */
export interface DailyProductionContextType {
  state: DailyProductionState;
  fetchProductions: (options?: DailyProductionFilterOptions) => Promise<void>;
  fetchProductionReport: (request: ProductionReportRequest) => Promise<void>;
  fetchProductionByDate: (date: string, contractId?: number) => Promise<DailyProduction[]>;
  createProduction: (data: DailyProductionCreateRequest) => Promise<DailyProduction>;
  createBulkProduction: (data: BulkProductionCreateRequest) => Promise<BulkProductionResult>;
  updateProduction: (id: number, data: DailyProductionUpdateRequest) => Promise<DailyProduction>;
  deleteProduction: (id: number) => Promise<void>;
  setSelectedProduction: (production: DailyProduction | null) => void;
  clearError: () => void;
}

/**
 * Daily Production Hook Return Type
 */
export interface UseDailyProductionReturn extends DailyProductionContextType {
  // Additional computed properties
  todayProductions: DailyProduction[];
  thisWeekProductions: DailyProduction[];
  thisMonthProductions: DailyProduction[];
}

/**
 * Daily Production Export Data Interface
 */
export interface DailyProductionExportData {
  production_date: string;
  contract_number: string;
  customer_name: string;
  product_code: string;
  product_name: string;
  quantity: string;
  unit_price: string;
  total_amount: string;
  notes: string;
  created_date: string;
}

/**
 * Production Calendar Event Interface
 */
export interface ProductionCalendarEvent {
  date: string;
  total_records: number;
  total_amount: number;
  contracts: string[];
  products: string[];
}

/**
 * Bulk Status Update Request Interface
 */
export interface BulkStatusUpdateRequest {
  production_ids: number[];
  new_status: ProductionStatus;
}

/**
 * Bulk Status Update Result Interface
 */
export interface BulkStatusUpdateResult {
  success: boolean;
  updated_count: number;
  updated_records: DailyProduction[];
  errors: string[];
  skipped_count: number;
}

/**
 * Production Confirmation Filter Interface
 */
export interface ProductionConfirmationFilter {
  year?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  contract_id?: number;
  status?: ProductionStatus;
}

/**
 * Production Status Statistics Interface
 */
export interface ProductionStatusStats {
  status: ProductionStatus;
  count: number;
  total_amount: number;
}

/**
 * Monthly Production Summary Interface
 */
export interface MonthlyProductionSummary {
  year: number;
  month: number;
  contract_id?: number;
  stats: ProductionStatusStats[];
  total_records: number;
  total_amount: number;
}

/**
 * Grouped Production Interface (for new grouped view)
 */
export interface GroupedProduction {
  production_date: string;
  contract_id: number;
  contract_number: string;
  contract_name: string;
  customer_id: number;
  customer_name: string;
  customer_short_name?: string;
  product_count: number;
  total_amount: number;
}

/**
 * Production Status Change Log Interface
 */
export interface ProductionStatusLog {
  id: number;
  production_id: number;
  old_status: ProductionStatus | null;
  new_status: ProductionStatus;
  changed_by: number;
  changed_at: string;
  notes?: string;
}

/**
 * Production Workflow Validation Interface
 */
export interface ProductionWorkflowValidation {
  can_confirm: boolean;
  can_create_receivable: boolean;
  can_edit: boolean;
  can_delete: boolean;
  warnings: string[];
  errors: string[];
}
