/**
 * Product Dialog Component
 * Dialog để tạo mới hoặc chỉnh sửa sản phẩm
 */

import React, { useEffect, useState, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Divider,
  Alert,
  Chip,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { FormTextField } from '../common/FormFields';
import {
  Product,
  ProductFormData,
  ProductCreateRequest,
  ProductUpdateRequest,
  PRODUCT_UNIT_TYPE_OPTIONS
} from '../../types/product';
import { productService } from '../../services/api';

// Simple debounce function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

interface ProductDialogProps {
  open: boolean;
  product?: Product | null;
  loading?: boolean;
  error?: string | null;
  onClose: () => void;
  onSubmit: (data: ProductCreateRequest | ProductUpdateRequest) => Promise<void>;
}

// Validation schema (loại bỏ code vì sẽ auto-generate)
const validationSchema = yup.object({
  name: yup
    .string()
    .required('Tên sản phẩm là bắt buộc')
    .min(2, 'Tên sản phẩm phải có ít nhất 2 ký tự')
    .max(200, 'Tên sản phẩm không được vượt quá 200 ký tự'),
  description: yup
    .string()
    .max(1000, 'Mô tả không được vượt quá 1000 ký tự'),
  unit_type: yup
    .string()
    .required('Đơn vị tính là bắt buộc')
    .oneOf(['kg', 'piece', 'meter', 'liter', 'box', 'set'], 'Đơn vị tính không hợp lệ'),
  is_active: yup.boolean(),
});

const ProductDialog: React.FC<ProductDialogProps> = ({
  open,
  product,
  loading = false,
  error,
  onClose,
  onSubmit,
}) => {
  const isEdit = Boolean(product);

  // State cho auto-generated code preview
  const [nextCode, setNextCode] = useState<string>('');
  const [codeLoading, setCodeLoading] = useState<boolean>(false);

  // State cho name validation
  const [nameValidation, setNameValidation] = useState<{
    checking: boolean;
    exists: boolean;
    exactMatch: any;
    suggestions: any[];
  }>({
    checking: false,
    exists: false,
    exactMatch: null,
    suggestions: [],
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<ProductFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      name: '',
      description: '',
      unit_type: 'piece', // Đổi mặc định thành 'cái/chiếc'
      is_active: true,
    },
  });

  const watchedName = watch('name');

  // Debounced name validation
  const debouncedCheckName = useCallback(
    debounce(async (name: string) => {
      if (!name || name.length < 2) {
        setNameValidation({
          checking: false,
          exists: false,
          exactMatch: null,
          suggestions: [],
        });
        return;
      }

      setNameValidation(prev => ({ ...prev, checking: true }));

      try {
        const response = await productService.checkName(name, product?.id);
        if (response.success) {
          setNameValidation({
            checking: false,
            exists: response.data.exists,
            exactMatch: response.data.exactMatch,
            suggestions: response.data.suggestions,
          });
        }
      } catch (error) {
        console.error('Error checking name:', error);
        setNameValidation(prev => ({ ...prev, checking: false }));
      }
    }, 500),
    [product?.id]
  );

  // Load next code khi mở dialog tạo mới
  useEffect(() => {
    if (open && !isEdit) {
      setCodeLoading(true);
      productService.getNextCode()
        .then(response => {
          if (response.success) {
            setNextCode(response.data.nextCode);
          }
        })
        .catch(error => {
          console.error('Error getting next code:', error);
        })
        .finally(() => {
          setCodeLoading(false);
        });
    }
  }, [open, isEdit]);

  // Watch name changes để trigger validation
  useEffect(() => {
    if (open && !isEdit && watchedName) {
      debouncedCheckName(watchedName);
    }
  }, [watchedName, open, isEdit, debouncedCheckName]);

  // Reset form khi dialog mở/đóng hoặc product thay đổi
  useEffect(() => {
    if (open) {
      if (product) {
        reset({
          name: product.name,
          description: product.description || '',
          unit_type: product.unit_type,
          is_active: product.is_active,
        });
      } else {
        reset({
          name: '',
          description: '',
          unit_type: 'piece', // Đổi mặc định thành 'cái/chiếc'
          is_active: true,
        });
      }

      // Reset validation state
      setNameValidation({
        checking: false,
        exists: false,
        exactMatch: null,
        suggestions: [],
      });
    }
  }, [open, product, reset]);

  const handleFormSubmit = async (data: ProductFormData) => {
    try {
      if (isEdit) {
        // Khi edit, không gửi code vì không được phép thay đổi
        const updateData: ProductUpdateRequest = {
          name: data.name,
          description: data.description || undefined,
          unit_type: data.unit_type,
          is_active: data.is_active,
        };
        await onSubmit(updateData);
      } else {
        // Khi tạo mới (code sẽ được auto-generate bởi backend)
        const createData: ProductCreateRequest = {
          name: data.name,
          description: data.description || undefined,
          unit_type: data.unit_type,
          is_active: data.is_active,
        };
        await onSubmit(createData);
      }
    } catch (error) {
      // Error sẽ được handle bởi parent component
      console.error('Error submitting product:', error);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: 400 }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          {isEdit ? 'Chỉnh sửa sản phẩm' : 'Thêm sản phẩm mới'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {isEdit
            ? 'Cập nhật thông tin sản phẩm'
            : 'Nhập thông tin sản phẩm mới'
          }
        </Typography>
      </DialogTitle>

      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <DialogContent sx={{ borderBottom: 'none' }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={2}>
            {/* Hàng 1: Tên sản phẩm - Đơn vị tính - Mã sản phẩm tự động */}
            <Grid item xs={4}>
              <FormTextField
                name="name"
                control={control}
                label="Tên sản phẩm"
                required
                autoFocus={!isEdit}
                InputProps={{
                  endAdornment: nameValidation.checking && (
                    <CircularProgress size={20} />
                  ),
                }}
              />
            </Grid>

            <Grid item xs={4}>
              <Controller
                name="unit_type"
                control={control}
                render={({ field, fieldState }) => (
                  <FormControl fullWidth error={!!fieldState.error}>
                    <InputLabel required>Đơn vị tính</InputLabel>
                    <Select
                      {...field}
                      label="Đơn vị tính"
                      size="medium"
                    >
                      {PRODUCT_UNIT_TYPE_OPTIONS.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </Select>
                    {fieldState.error && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                        {fieldState.error.message}
                      </Typography>
                    )}
                  </FormControl>
                )}
              />
            </Grid>

            <Grid item xs={4}>
              <Box sx={{
                p: 2,
                bgcolor: 'grey.50',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'grey.300',
                height: '56px', // Cùng chiều cao với TextField
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                {!isEdit ? (
                  codeLoading ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CircularProgress size={16} />
                      <Typography variant="body2" color="text.secondary">
                        Đang tạo...
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body1" fontWeight={500} color="primary.main">
                      {nextCode || 'SP001'}
                    </Typography>
                  )
                ) : (
                  <Typography variant="body1" fontWeight={500} color="text.primary">
                    {product?.code}
                  </Typography>
                )}
              </Box>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block', textAlign: 'center' }}>
                Mã sản phẩm tự động
              </Typography>
            </Grid>

            {/* Hàng 2: Ghi chú - Đang hoạt động */}
            <Grid item xs={8}>
              <FormTextField
                name="description"
                control={control}
                label="Ghi chú"
                multiline
                rows={3}
              />
            </Grid>

            <Grid item xs={4}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                minHeight: '80px'
              }}>
                <Controller
                  name="is_active"
                  control={control}
                  render={({ field }) => (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={field.value}
                          onChange={field.onChange}
                          color="primary"
                          size="medium"
                        />
                      }
                      label={
                        <Typography variant="body1" fontWeight={500}>
                          Đang hoạt động
                        </Typography>
                      }
                    />
                  )}
                />
              </Box>
            </Grid>

            {/* Name validation warnings - hiển thị dưới form */}
            {!isEdit && nameValidation.exists && (
              <Grid item xs={12}>
                <Alert severity="warning">
                  <Typography variant="body2" fontWeight={500}>
                    ⚠️ Tên sản phẩm đã tồn tại
                  </Typography>
                  <Typography variant="body2">
                    Sản phẩm "{nameValidation.exactMatch?.name}" (Mã: {nameValidation.exactMatch?.code}) đã có tên tương tự.
                  </Typography>
                </Alert>
              </Grid>
            )}

            {/* Suggestions */}
            {!isEdit && nameValidation.suggestions.length > 0 && !nameValidation.exists && (
              <Grid item xs={12}>
                <Box>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    💡 Các sản phẩm tương tự:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {nameValidation.suggestions.slice(0, 3).map((suggestion) => (
                      <Chip
                        key={suggestion.id}
                        label={`${suggestion.name} (${suggestion.code})`}
                        size="small"
                        variant="outlined"
                        color="info"
                      />
                    ))}
                  </Box>
                </Box>
              </Grid>
            )}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2, borderTop: 'none' }}>
          <Button
            onClick={handleClose}
            disabled={isSubmitting}
            color="inherit"
            size="large"
            sx={{ minWidth: 100, height: 40 }}
          >
            Hủy
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting || loading}
            size="large"
            sx={{ minWidth: 100, height: 40 }}
          >
            {isSubmitting ? 'Đang xử lý...' : (isEdit ? 'Cập nhật' : 'Thêm mới')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ProductDialog;
