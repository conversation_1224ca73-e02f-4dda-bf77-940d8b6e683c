# Database Schema Documentation Updater
# Cập nhật tự động file .rules/database-schema.md

param(
    [switch]$Detailed,
    [switch]$Help
)

if ($Help) {
    Write-Host "Database Schema Documentation Updater" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\scripts\update-schema-docs.ps1           # Cập nhật file schema chính"
    Write-Host "  .\scripts\update-schema-docs.ps1 -Detailed # Tạo báo cáo chi tiết"
    Write-Host "  .\scripts\update-schema-docs.ps1 -Help     # Hiển thị hướng dẫn"
    Write-Host ""
    Write-Host "Files được cập nhật:" -ForegroundColor Green
    Write-Host "  - .rules/database-schema.md (file chính)"
    Write-Host "  - .rules/database-schema-detailed.md (báo cáo chi tiết)"
    Write-Host ""
    exit 0
}

Write-Host "🔧 Database Schema Documentation Updater" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

# Kiểm tra Node.js
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Node.js not found"
    }
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js không được tìm thấy. Vui lòng cài đặt Node.js." -ForegroundColor Red
    exit 1
}

# Kiểm tra file .env
$envFile = "server\.env"
if (-not (Test-Path $envFile)) {
    Write-Host "❌ Không tìm thấy file $envFile" -ForegroundColor Red
    Write-Host "Vui lòng tạo file .env từ .env.example" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Tìm thấy file cấu hình database" -ForegroundColor Green

# Đọc thông tin database từ .env
try {
    $envContent = Get-Content $envFile
    $dbName = ($envContent | Where-Object { $_ -match "^DB_NAME=" }) -replace "DB_NAME=", ""
    $dbHost = ($envContent | Where-Object { $_ -match "^DB_HOST=" }) -replace "DB_HOST=", ""
    $dbPort = ($envContent | Where-Object { $_ -match "^DB_PORT=" }) -replace "DB_PORT=", ""
    $dbUser = ($envContent | Where-Object { $_ -match "^DB_USER=" }) -replace "DB_USER=", ""
    
    if (-not $dbName) { $dbName = "tinhtam-hp" }
    if (-not $dbHost) { $dbHost = "localhost" }
    if (-not $dbPort) { $dbPort = "5432" }
    if (-not $dbUser) { $dbUser = "postgres" }
    
    Write-Host "📊 Database Info:" -ForegroundColor Cyan
    Write-Host "   Host: $dbHost:$dbPort" -ForegroundColor Gray
    Write-Host "   Database: $dbName" -ForegroundColor Gray
    Write-Host "   User: $dbUser" -ForegroundColor Gray
} catch {
    Write-Host "❌ Lỗi đọc file .env: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Kiểm tra kết nối database
Write-Host "🔍 Kiểm tra kết nối database..." -ForegroundColor Cyan
try {
    # Set password từ environment variable để tránh prompt
    $env:PGPASSWORD = ($envContent | Where-Object { $_ -match "^DB_PASSWORD=" }) -replace "DB_PASSWORD=", ""
    
    $testResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "SELECT 1;" 2>&1
    if ($LASTEXITCODE -ne 0) {
        throw "Connection failed: $testResult"
    }
    Write-Host "✅ Kết nối database thành công!" -ForegroundColor Green
} catch {
    Write-Host "❌ Không thể kết nối database: $_" -ForegroundColor Red
    Write-Host "Vui lòng kiểm tra:" -ForegroundColor Yellow
    Write-Host "  - PostgreSQL đang chạy" -ForegroundColor Yellow
    Write-Host "  - Thông tin kết nối trong file .env" -ForegroundColor Yellow
    Write-Host "  - Database '$dbName' đã được tạo" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Chạy script cập nhật
Write-Host "📝 Đang cập nhật documentation..." -ForegroundColor Cyan

try {
    if ($Detailed) {
        Write-Host "Tạo báo cáo chi tiết..." -ForegroundColor Yellow
        node scripts/update-schema-docs.js --detailed
    } else {
        Write-Host "Cập nhật file schema chính..." -ForegroundColor Yellow
        node scripts/update-schema-docs.js
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Script execution failed"
    }
    
    Write-Host ""
    Write-Host "🎉 HOÀN THÀNH!" -ForegroundColor Green
    Write-Host ""
    
    if ($Detailed) {
        Write-Host "Files đã được cập nhật:" -ForegroundColor Cyan
        Write-Host "  ✅ .rules/database-schema.md" -ForegroundColor Green
        Write-Host "  ✅ .rules/database-schema-detailed.md" -ForegroundColor Green
    } else {
        Write-Host "File đã được cập nhật:" -ForegroundColor Cyan
        Write-Host "  ✅ .rules/database-schema.md" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "Lưu ý:" -ForegroundColor Yellow
    Write-Host "  - File được cập nhật tự động với timestamp hiện tại"
    Write-Host "  - Chạy lại script này mỗi khi có thay đổi database schema"
    Write-Host "  - Sử dụng -Detailed để tạo báo cáo chi tiết"
    
} catch {
    Write-Host "❌ Lỗi cập nhật documentation: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
