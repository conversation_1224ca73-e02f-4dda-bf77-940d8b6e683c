const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const customerController = require('../controllers/customerController');
const { authenticateToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

// Middleware để kiểm tra validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'D<PERSON> liệu không hợp lệ',
        details: errors.array().map(error => `${error.param}: ${error.msg}`),
        statusCode: 400
      }
    });
  }
  next();
};

const router = express.Router();

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// Middleware xác thực cho tất cả routes
// router.use(authenticateToken);

/**
 * @route   GET /api/v1/customers
 * @desc    Lấy danh sách khách hàng với phân trang và tìm kiếm
 * @access  Private
 * @query   page - Số trang (default: 1)
 * @query   limit - Số lượng items per page (default: 10, max: 1000)
 * @query   search - Từ khóa tìm kiếm
 * @query   sort - Cột để sắp xếp (default: created_at)
 * @query   order - Thứ tự sắp xếp ASC/DESC (default: DESC)
 */
router.get('/', [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Số trang phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Số lượng items phải từ 1 đến 1000'),
  query('sort')
    .optional()
    .isIn(['id', 'name', 'short_name', 'tax_code', 'created_at', 'updated_at'])
    .withMessage('Cột sắp xếp không hợp lệ'),
  query('order')
    .optional()
    .isIn(['ASC', 'DESC', 'asc', 'desc'])
    .withMessage('Thứ tự sắp xếp phải là ASC hoặc DESC'),
  query('search')
    .optional()
    .isLength({ max: 255 })
    .withMessage('Từ khóa tìm kiếm không được quá 255 ký tự'),
  handleValidationErrors,
  // NOTE: Tạm thời comment out permission check
  // checkPermission('customer', 'view')
], customerController.getAllCustomers);

/**
 * @route   GET /api/v1/customers/:id
 * @desc    Lấy thông tin khách hàng theo ID
 * @access  Private
 */
router.get('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương'),
  handleValidationErrors,
  // NOTE: Tạm thời comment out permission check
  // checkPermission('customer', 'view')
], customerController.getCustomerById);

/**
 * @route   POST /api/v1/customers
 * @desc    Tạo khách hàng mới
 * @access  Private
 */
router.post('/', [
  body('name')
    .notEmpty()
    .withMessage('Tên khách hàng không được để trống')
    .isLength({ min: 1, max: 200 })
    .withMessage('Tên khách hàng phải từ 1 đến 200 ký tự')
    .trim(),
  body('tax_code')
    .trim()
    .notEmpty()
    .withMessage('Mã số thuế không được để trống')
    .isLength({ min: 1, max: 20 })
    .withMessage('Mã số thuế phải từ 1 đến 20 ký tự'),
  body('short_name')
    .trim()
    .notEmpty()
    .withMessage('Tên viết tắt không được để trống')
    .isLength({ min: 1, max: 100 })
    .withMessage('Tên viết tắt phải từ 1 đến 100 ký tự'),
  body('address')
    .trim()
    .notEmpty()
    .withMessage('Địa chỉ không được để trống')
    .isLength({ min: 1, max: 1000 })
    .withMessage('Địa chỉ phải từ 1 đến 1000 ký tự'),
  body('contact_person')
    .optional({ nullable: true, checkFalsy: true })
    .isLength({ max: 100 })
    .withMessage('Tên người liên hệ không được quá 100 ký tự')
    .trim(),
  body('phone')
    .optional({ nullable: true, checkFalsy: true })
    .custom((value) => {
      if (!value) return true; // Cho phép empty
      const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
      return phoneRegex.test(value.replace(/\s/g, ''));
    })
    .withMessage('Số điện thoại không đúng định dạng (VD: 0987654321)')
    .isLength({ max: 20 })
    .withMessage('Số điện thoại không được quá 20 ký tự')
    .trim(),
  body('email')
    .optional({ nullable: true, checkFalsy: true })
    .isEmail()
    .withMessage('Email không đúng định dạng')
    .isLength({ max: 100 })
    .withMessage('Email không được quá 100 ký tự')
    .normalizeEmail(),
  handleValidationErrors,
  // NOTE: Tạm thời comment out permission check
  // checkPermission('customer', 'create')
], customerController.createCustomer);

/**
 * @route   PUT /api/v1/customers/:id
 * @desc    Cập nhật thông tin khách hàng
 * @access  Private
 */
router.put('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương'),
  body('name')
    .notEmpty()
    .withMessage('Tên khách hàng không được để trống')
    .isLength({ min: 1, max: 200 })
    .withMessage('Tên khách hàng phải từ 1 đến 200 ký tự')
    .trim(),
  body('tax_code')
    .optional({ nullable: true })
    .isLength({ max: 20 })
    .withMessage('Mã số thuế không được quá 20 ký tự')
    .trim(),
  body('short_name')
    .optional({ nullable: true })
    .isLength({ max: 100 })
    .withMessage('Tên viết tắt không được quá 100 ký tự')
    .trim(),
  body('address')
    .optional({ nullable: true })
    .isLength({ max: 1000 })
    .withMessage('Địa chỉ không được quá 1000 ký tự')
    .trim(),
  body('contact_person')
    .optional({ nullable: true })
    .isLength({ max: 100 })
    .withMessage('Tên người liên hệ không được quá 100 ký tự')
    .trim(),
  body('phone')
    .optional({ nullable: true })
    .isLength({ max: 20 })
    .withMessage('Số điện thoại không được quá 20 ký tự')
    .trim(),
  body('email')
    .optional({ nullable: true })
    .isEmail()
    .withMessage('Email không đúng định dạng')
    .isLength({ max: 100 })
    .withMessage('Email không được quá 100 ký tự')
    .normalizeEmail(),
  handleValidationErrors,
  // NOTE: Tạm thời comment out permission check
  // checkPermission('customer', 'edit')
], customerController.updateCustomer);

/**
 * @route   DELETE /api/v1/customers/:id
 * @desc    Xóa khách hàng (soft delete)
 * @access  Private
 */
router.delete('/:id', [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID khách hàng phải là số nguyên dương'),
  handleValidationErrors,
  // NOTE: Tạm thời comment out permission check
  // checkPermission('customer', 'delete')
], customerController.deleteCustomer);

module.exports = router;
