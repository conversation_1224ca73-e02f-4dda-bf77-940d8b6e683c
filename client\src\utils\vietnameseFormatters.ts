/**
 * Vietnamese Formatters
 * Utilities for formatting dates, numbers, and currency in Vietnamese format
 */

/**
 * Format date to Vietnamese format (dd/mm/yyyy)
 * @param date - Date object, string, or null
 * @returns Formatted date string or empty string
 */
export const formatDateVN = (date: Date | string | null): string => {
  if (!date) return '';
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) return '';
  
  const day = dateObj.getDate().toString().padStart(2, '0');
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const year = dateObj.getFullYear();
  
  return `${day}/${month}/${year}`;
};

/**
 * Parse Vietnamese date format (dd/mm/yyyy) to Date object
 * @param dateStr - Date string in dd/mm/yyyy format
 * @returns Date object or null if invalid
 */
export const parseDateVN = (dateStr: string): Date | null => {
  if (!dateStr || typeof dateStr !== 'string') return null;
  
  const parts = dateStr.trim().split('/');
  if (parts.length !== 3) return null;
  
  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);
  
  if (isNaN(day) || isNaN(month) || isNaN(year)) return null;
  if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900) return null;
  
  const date = new Date(year, month - 1, day);
  
  // Validate the date is correct (handles invalid dates like 31/02/2025)
  if (date.getDate() !== day || date.getMonth() !== month - 1 || date.getFullYear() !== year) {
    return null;
  }
  
  return date;
};

/**
 * Convert Vietnamese date format to ISO format (yyyy-mm-dd)
 * @param dateStr - Date string in dd/mm/yyyy format
 * @returns ISO date string or empty string if invalid
 */
export const convertVNDateToISO = (dateStr: string): string => {
  const date = parseDateVN(dateStr);
  if (!date) return '';

  return convertDateToISOString(date);
};

/**
 * Convert Date object to ISO date string without timezone issues
 * @param date - Date object
 * @returns ISO date string (yyyy-mm-dd) or empty string if invalid
 */
export const convertDateToISOString = (date: Date | null): string => {
  if (!date || isNaN(date.getTime())) return '';

  // Use local date components to avoid timezone conversion issues
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Convert ISO date string to Date object for DatePicker
 * @param isoDateStr - ISO date string (yyyy-mm-dd)
 * @returns Date object or null if invalid
 */
export const convertISOStringToDate = (isoDateStr: string | null | undefined): Date | null => {
  if (!isoDateStr || typeof isoDateStr !== 'string') return null;

  // Parse ISO date string as local date to avoid timezone issues
  const parts = isoDateStr.split('-');
  if (parts.length !== 3) return null;

  const year = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
  const day = parseInt(parts[2], 10);

  if (isNaN(year) || isNaN(month) || isNaN(day)) return null;

  const date = new Date(year, month, day);

  // Validate the date is correct (handles invalid dates like 31/02/2025)
  if (date.getDate() !== day || date.getMonth() !== month || date.getFullYear() !== year) {
    return null;
  }

  return date;
};

/**
 * Convert ISO date format to Vietnamese format
 * @param isoDateStr - Date string in yyyy-mm-dd format
 * @returns Vietnamese date string or empty string if invalid
 */
export const convertISODateToVN = (isoDateStr: string): string => {
  if (!isoDateStr) return '';
  
  const date = new Date(isoDateStr);
  return formatDateVN(date);
};

/**
 * Format number to Vietnamese currency format (1.000.000)
 * @param value - Number to format
 * @param showCurrency - Whether to show "VND" suffix
 * @returns Formatted number string
 */
export const formatCurrencyVN = (value: number | string, showCurrency: boolean = false): string => {
  if (value === null || value === undefined || value === '') return '0';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) return '0';
  
  // Format with Vietnamese thousand separator (.)
  const formatted = numValue.toLocaleString('vi-VN');
  
  return showCurrency ? `${formatted} VND` : formatted;
};

/**
 * Parse Vietnamese currency format to number
 * @param currencyStr - Currency string in Vietnamese format (1.000.000)
 * @returns Number value or 0 if invalid
 */
export const parseCurrencyVN = (currencyStr: string): number => {
  if (!currencyStr || typeof currencyStr !== 'string') return 0;
  
  // Remove VND suffix and spaces
  let cleanStr = currencyStr.replace(/\s*VND\s*/gi, '').trim();
  
  // Replace Vietnamese thousand separators (.) with empty string
  // But keep decimal separator (,) if exists
  const parts = cleanStr.split(',');
  if (parts.length > 2) return 0; // Invalid format
  
  const integerPart = parts[0].replace(/\./g, '');
  const decimalPart = parts[1] || '';
  
  const numberStr = decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
  const result = parseFloat(numberStr);
  
  return isNaN(result) ? 0 : result;
};

/**
 * Format number input with Vietnamese thousand separators
 * @param value - Input value
 * @returns Object with display value and numeric value
 */
export const handleCurrencyInputVN = (value: string): { displayValue: string; numericValue: number } => {
  // Remove all non-digit characters except comma for decimal
  const cleanValue = value.replace(/[^\d,]/g, '');
  
  // Split by comma to handle decimal
  const parts = cleanValue.split(',');
  if (parts.length > 2) {
    // Invalid format, return previous valid state
    return { displayValue: value, numericValue: parseCurrencyVN(value) };
  }
  
  // Format integer part with thousand separators
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  const decimalPart = parts[1];
  
  const displayValue = decimalPart !== undefined ? `${integerPart},${decimalPart}` : integerPart;
  const numericValue = parseCurrencyVN(displayValue);
  
  return { displayValue, numericValue };
};

/**
 * Validate Vietnamese date format
 * @param dateStr - Date string to validate
 * @returns True if valid Vietnamese date format
 */
export const isValidVNDate = (dateStr: string): boolean => {
  return parseDateVN(dateStr) !== null;
};

/**
 * Get current date in Vietnamese format
 * @returns Current date in dd/mm/yyyy format
 */
export const getCurrentDateVN = (): string => {
  return formatDateVN(new Date());
};

/**
 * Format quantity with Vietnamese number format
 * @param quantity - Quantity to format
 * @returns Formatted quantity string
 */
export const formatQuantityVN = (quantity: number | string): string => {
  if (quantity === null || quantity === undefined || quantity === '') return '0';
  
  const numValue = typeof quantity === 'string' ? parseFloat(quantity) : quantity;
  
  if (isNaN(numValue)) return '0';
  
  // For quantities, we might want to show decimals
  return numValue.toLocaleString('vi-VN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 3
  });
};

/**
 * Format file size in Vietnamese
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 */
export const formatFileSizeVN = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)).toLocaleString('vi-VN') + ' ' + sizes[i];
};

/**
 * Format percentage in Vietnamese
 * @param value - Percentage value (0-100)
 * @returns Formatted percentage string
 */
export const formatPercentageVN = (value: number): string => {
  return `${value.toLocaleString('vi-VN', { minimumFractionDigits: 1, maximumFractionDigits: 2 })}%`;
};
