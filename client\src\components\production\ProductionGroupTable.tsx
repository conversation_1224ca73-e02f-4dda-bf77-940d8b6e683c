/**
 * Production Group Table Component
 * Hiển thị sản lượng đã nhóm theo hợp đồng và ngày
 */

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TableSortLabel,
  Typography,
  IconButton,
  Chip,
  Skeleton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { formatCurrencyVN, formatDateVN } from '../../utils/vietnameseFormatters';
import { GroupedProduction } from '../../types/dailyProduction';

interface ProductionGroupTableProps {
  productions: GroupedProduction[];
  loading?: boolean;
  totalCount?: number;
  page?: number;
  rowsPerPage?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onSort?: (orderBy: string, order: 'asc' | 'desc') => void;
  onView?: (production: GroupedProduction) => void;
  onEdit?: (production: GroupedProduction) => void;
  onDelete?: (production: GroupedProduction) => void;
  onRefresh?: () => void;
}

const ProductionGroupTable: React.FC<ProductionGroupTableProps> = ({
  productions,
  loading = false,
  totalCount = 0,
  page = 0,
  rowsPerPage = 10,
  orderBy = 'production_date',
  order = 'desc',
  onPageChange,
  onRowsPerPageChange,
  onSort,
  onView,
  onEdit,
  onDelete,
  onRefresh,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedProduction, setSelectedProduction] = useState<GroupedProduction | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, production: GroupedProduction) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedProduction(production);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProduction(null);
  };

  const handleSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    onSort?.(property, isAsc ? 'desc' : 'asc');
  };

  const formatCurrency = (amount: number) => {
    return formatCurrencyVN(amount, true); // true để hiển thị VND
  };

  const formatDate = (dateString: string) => {
    return formatDateVN(dateString);
  };

  const renderSkeletonRows = () => {
    return Array.from({ length: rowsPerPage }).map((_, index) => (
      <TableRow key={index}>
        <TableCell><Skeleton variant="text" /></TableCell>
        <TableCell><Skeleton variant="text" /></TableCell>
        <TableCell><Skeleton variant="text" /></TableCell>
        <TableCell><Skeleton variant="text" /></TableCell>
        <TableCell><Skeleton variant="text" /></TableCell>
      </TableRow>
    ));
  };

  const headCells = [
    { id: 'production_date', label: 'Ngày sản xuất', sortable: true },
    { id: 'customer_name', label: 'Khách hàng', sortable: false },
    { id: 'short_name', label: 'Tên viết tắt', sortable: false },
    { id: 'contract_number', label: 'Hợp đồng', sortable: false },
    { id: 'total_amount', label: 'Số tiền', sortable: true },
    { id: 'actions', label: 'Thao tác', sortable: false },
  ];

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {headCells.map((headCell) => (
                <TableCell
                  key={headCell.id}
                  align={headCell.id === 'total_amount' ? 'right' : 'left'}
                  sx={{ 
                    fontWeight: 600,
                    backgroundColor: 'grey.50',
                    width: headCell.id === 'actions' ? 120 : 'auto'
                  }}
                >
                  {headCell.sortable ? (
                    <TableSortLabel
                      active={orderBy === headCell.id}
                      direction={orderBy === headCell.id ? order : 'asc'}
                      onClick={() => handleSort(headCell.id)}
                    >
                      {headCell.label}
                    </TableSortLabel>
                  ) : (
                    headCell.label
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              renderSkeletonRows()
            ) : productions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={headCells.length} align="center" sx={{ py: 3 }}>
                  <Typography variant="body2" color="textSecondary">
                    Không có dữ liệu sản lượng
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              productions.map((production) => (
                <TableRow
                  hover
                  key={`${production.contract_id}-${production.production_date}`}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => onView?.(production)}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      {formatDate(production.production_date)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {production.customer_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {production.customer_short_name || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        {production.contract_number}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {production.contract_name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Box>
                      <Typography variant="body2" fontWeight={600} color="primary.main">
                        {formatCurrency(production.total_amount)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {production.product_count} sản phẩm
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="Thao tác">
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, production)}
                      >
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={(_, newPage) => onPageChange?.(newPage)}
        onRowsPerPageChange={(event) => onRowsPerPageChange?.(parseInt(event.target.value, 10))}
        labelRowsPerPage="Số dòng mỗi trang:"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} của ${count}`}
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 160 }
        }}
      >
        <MenuItem onClick={() => {
          onView?.(selectedProduction!);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xem chi tiết</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          onEdit?.(selectedProduction!);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Chỉnh sửa</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          onDelete?.(selectedProduction!);
          handleMenuClose();
        }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xóa</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ProductionGroupTable;
