/**
 * Authentication Type Definitions
 */

/**
 * User Interface
 */
export interface User {
  id: number;
  name: string;
  email: string;
  position?: string | null;
  is_active: boolean;
  last_login?: string | null;
  created_at?: string;
  updated_at?: string;
}

/**
 * Login Request Interface
 */
export interface LoginRequest {
  email: string;
  password: string;
}

/**
 * Login Response Interface
 */
export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: string;
}

/**
 * Refresh Token Request Interface
 */
export interface RefreshTokenRequest {
  refreshToken: string;
}

/**
 * Refresh Token Response Interface
 */
export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresIn: string;
}

/**
 * Register Request Interface
 */
export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  position?: string;
}

/**
 * Register Response Interface
 */
export interface RegisterResponse {
  user: User;
}

/**
 * Auth State Interface
 */
export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

/**
 * Permission Interface
 */
export interface Permission {
  resourceType: string;
  action: string;
}

/**
 * User Role Interface
 */
export interface UserRole {
  id: number;
  name: string;
  description?: string;
  permissions: Permission[];
}

/**
 * Login Form Data Interface
 */
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Register Form Data Interface
 */
export interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  position?: string;
}

/**
 * Change Password Form Data Interface
 */
export interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Update Profile Form Data Interface
 */
export interface UpdateProfileFormData {
  name: string;
  email: string;
  position?: string;
}

/**
 * Auth Validation Error Interface
 */
export interface AuthValidationError {
  field: string;
  message: string;
}

/**
 * Auth Validation Result Interface
 */
export interface AuthValidationResult {
  isValid: boolean;
  errors: AuthValidationError[];
}

/**
 * JWT Token Payload Interface
 */
export interface JWTPayload {
  userId: number;
  email: string;
  name: string;
  iat: number;
  exp: number;
  iss: string;
}

/**
 * Auth Error Types
 */
export type AuthErrorType = 
  | 'INVALID_CREDENTIALS'
  | 'TOKEN_EXPIRED'
  | 'UNAUTHORIZED'
  | 'FORBIDDEN'
  | 'NETWORK_ERROR'
  | 'VALIDATION_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Auth Error Interface
 */
export interface AuthError {
  type: AuthErrorType;
  message: string;
  details?: string[];
}

/**
 * Auth Action Types
 */
export type AuthActionType =
  | 'AUTH_START'
  | 'AUTH_SUCCESS'
  | 'AUTH_FAILURE'
  | 'LOGOUT'
  | 'CLEAR_ERROR'
  | 'SET_USER'
  | 'TOKEN_REFRESH';

/**
 * Auth Context Interface
 */
export interface AuthContextType {
  state: AuthState;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  clearError: () => void;
  updateUser: (user: User) => void;
}

/**
 * Password Strength Interface
 */
export interface PasswordStrength {
  score: number; // 0-4
  feedback: string[];
  isValid: boolean;
}

/**
 * Session Info Interface
 */
export interface SessionInfo {
  user: User;
  loginTime: string;
  lastActivity: string;
  expiresAt: string;
  isActive: boolean;
}

/**
 * Auth Configuration Interface
 */
export interface AuthConfig {
  tokenKey: string;
  refreshTokenKey: string;
  tokenExpiration: number;
  refreshTokenExpiration: number;
  autoRefresh: boolean;
  rememberMeDuration: number;
}

/**
 * Default Auth Configuration
 */
export const DEFAULT_AUTH_CONFIG: AuthConfig = {
  tokenKey: 'token',
  refreshTokenKey: 'refreshToken',
  tokenExpiration: 24 * 60 * 60 * 1000, // 24 hours
  refreshTokenExpiration: 7 * 24 * 60 * 60 * 1000, // 7 days
  autoRefresh: true,
  rememberMeDuration: 30 * 24 * 60 * 60 * 1000, // 30 days
};

/**
 * User Position Types
 */
export type UserPosition = 
  | 'Administrator'
  | 'Manager'
  | 'Staff'
  | 'Intern'
  | string;

/**
 * User Status Types
 */
export type UserStatus = 'active' | 'inactive' | 'suspended';

/**
 * Auth Event Types
 */
export type AuthEventType = 
  | 'login'
  | 'logout'
  | 'token_refresh'
  | 'session_expired'
  | 'unauthorized_access';

/**
 * Auth Event Interface
 */
export interface AuthEvent {
  type: AuthEventType;
  timestamp: string;
  userId?: number;
  details?: Record<string, any>;
}

/**
 * Login Form Field Configuration Interface
 */
export interface LoginFormFieldConfig {
  name: keyof LoginFormData;
  label: string;
  type: 'email' | 'password' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  helperText?: string;
  validation?: (value: any) => string | null;
}

/**
 * Default Login Form Field Configurations
 */
export const LOGIN_FORM_FIELDS: LoginFormFieldConfig[] = [
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    required: true,
    placeholder: 'Nhập email của bạn',
    validation: (value: string) => {
      if (!value) return 'Email là bắt buộc';
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      if (!emailRegex.test(value)) return 'Email không đúng định dạng';
      return null;
    },
  },
  {
    name: 'password',
    label: 'Mật khẩu',
    type: 'password',
    required: true,
    placeholder: 'Nhập mật khẩu của bạn',
    validation: (value: string) => {
      if (!value) return 'Mật khẩu là bắt buộc';
      if (value.length < 6) return 'Mật khẩu phải có ít nhất 6 ký tự';
      return null;
    },
  },
  {
    name: 'rememberMe',
    label: 'Ghi nhớ đăng nhập',
    type: 'checkbox',
  },
];
