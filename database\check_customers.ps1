# PowerShell script kiem tra danh sach khach hang

Write-Host "=== KIEM TRA DANH SACH KHACH HANG ===" -ForegroundColor Green
Write-Host ""

# Thiet lap bien moi truong
$env:PGPASSWORD = "110591"
$env:PGCLIENTENCODING = "UTF8"

# Duong dan database
$dbHost = "localhost"
$dbPort = "5432"
$dbName = "tinhtam-hp"
$dbUser = "postgres"

Write-Host "Ket noi database: $dbName" -ForegroundColor Yellow
Write-Host ""

# Kiem tra tong so khach hang
Write-Host "1. Tong so khach hang:" -ForegroundColor Cyan
try {
    $totalResult = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -t -c "SELECT COUNT(*) FROM customers;" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   Tong cong: $($totalResult.Trim()) khach hang" -ForegroundColor Green
    } else {
        Write-Host "   Loi kiem tra: $totalResult" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "   Khong the kiem tra: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Hien thi danh sach khach hang
Write-Host "2. Danh sach khach hang:" -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
    SELECT 
        ROW_NUMBER() OVER (ORDER BY id) as stt,
        tax_code as ma_so_thue,
        short_name as ten_viet_tat,
        name as ten_day_du
    FROM customers 
    ORDER BY id;
    "
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Hien thi thanh cong!" -ForegroundColor Green
    } else {
        Write-Host "Loi hien thi danh sach!" -ForegroundColor Red
    }
} catch {
    Write-Host "Khong the hien thi danh sach: $_" -ForegroundColor Red
}

Write-Host ""

# Kiem tra khach hang theo loai hinh
Write-Host "3. Phan loai khach hang:" -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "
    SELECT 
        CASE 
            WHEN name LIKE '%KHÁCH SẠN%' OR short_name LIKE 'KS %' THEN 'Khach san'
            WHEN name LIKE '%CÔNG TY%' THEN 'Cong ty'
            WHEN name LIKE '%HỘ KINH DOANH%' THEN 'Ho kinh doanh'
            WHEN name LIKE '%CHI NHÁNH%' THEN 'Chi nhanh'
            ELSE 'Khac'
        END as loai_hinh,
        COUNT(*) as so_luong
    FROM customers 
    GROUP BY 
        CASE 
            WHEN name LIKE '%KHÁCH SẠN%' OR short_name LIKE 'KS %' THEN 'Khach san'
            WHEN name LIKE '%CÔNG TY%' THEN 'Cong ty'
            WHEN name LIKE '%HỘ KINH DOANH%' THEN 'Ho kinh doanh'
            WHEN name LIKE '%CHI NHÁNH%' THEN 'Chi nhanh'
            ELSE 'Khac'
        END
    ORDER BY so_luong DESC;
    "
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Phan loai thanh cong!" -ForegroundColor Green
    }
} catch {
    Write-Host "Khong the phan loai: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== HOAN THANH KIEM TRA ===" -ForegroundColor Green
Write-Host ""
Write-Host "Ghi chu:" -ForegroundColor Yellow
Write-Host "- Tat ca khach hang deu co dia chi tai Hai Phong"
Write-Host "- Co the cap nhat them thong tin lien he sau"
Write-Host "- San sang tao hop dong cho cac khach hang nay"
