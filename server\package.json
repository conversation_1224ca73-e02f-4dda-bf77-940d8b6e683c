{"name": "customer-management-server", "version": "1.0.0", "description": "Backend for Customer Management Application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.2.1", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "pg": "^8.9.0"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}, "keywords": ["customer", "management", "api", "nodejs", "express", "postgresql"], "author": "IVC", "license": "ISC"}