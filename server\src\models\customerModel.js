const { pool } = require('../db');

/**
 * Customer Model
 * Xử lý các thao tác CRUD cho bảng customers
 */

/**
 * L<PERSON><PERSON> tất cả khách hàng với phân trang và tìm kiếm
 * @param {Object} options - Tùy chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllCustomers = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    sortBy = 'created_at',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'name', 'short_name', 'tax_code', 'created_at', 'updated_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE c.is_active = true';
    const queryParams = [];
    let paramIndex = 1;

    if (search && search.trim()) {
      whereClause += ` AND (
        c.name ILIKE $${paramIndex} OR 
        c.short_name ILIKE $${paramIndex} OR 
        c.tax_code ILIKE $${paramIndex} OR 
        c.contact_person ILIKE $${paramIndex} OR
        c.address ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search.trim()}%`);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM customers c
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT 
        c.id,
        c.tax_code,
        c.name,
        c.short_name,
        c.address,
        c.contact_person,
        c.phone,
        c.email,
        c.is_active,
        c.created_at,
        c.updated_at,
        u.name as created_by_name
      FROM customers c
      LEFT JOIN users u ON c.created_by = u.id
      ${whereClause}
      ORDER BY c.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      customers: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllCustomers:', error);
    throw error;
  }
};

/**
 * Lấy khách hàng theo ID
 * @param {number} id - ID của khách hàng
 * @returns {Object|null} Thông tin khách hàng hoặc null nếu không tìm thấy
 */
const getCustomerById = async (id) => {
  try {
    const query = `
      SELECT 
        c.id,
        c.tax_code,
        c.name,
        c.short_name,
        c.address,
        c.contact_person,
        c.phone,
        c.email,
        c.is_active,
        c.created_at,
        c.updated_at,
        u.name as created_by_name
      FROM customers c
      LEFT JOIN users u ON c.created_by = u.id
      WHERE c.id = $1 AND c.is_active = true
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getCustomerById:', error);
    throw error;
  }
};

/**
 * Tạo khách hàng mới
 * @param {Object} customerData - Dữ liệu khách hàng
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin khách hàng vừa tạo
 */
const createCustomer = async (customerData, createdBy) => {
  const {
    tax_code,
    name,
    short_name,
    address,
    contact_person,
    phone,
    email
  } = customerData;

  try {
    const query = `
      INSERT INTO customers (
        tax_code, name, short_name, address, contact_person, phone, email, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      tax_code || null,
      name,
      short_name || null,
      address || null,
      contact_person || null,
      phone || null,
      email || null,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createCustomer:', error);
    throw error;
  }
};

/**
 * Cập nhật thông tin khách hàng
 * @param {number} id - ID của khách hàng
 * @param {Object} customerData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin khách hàng sau khi cập nhật
 */
const updateCustomer = async (id, customerData) => {
  const {
    tax_code,
    name,
    short_name,
    address,
    contact_person,
    phone,
    email
  } = customerData;

  try {
    const query = `
      UPDATE customers 
      SET 
        tax_code = $1,
        name = $2,
        short_name = $3,
        address = $4,
        contact_person = $5,
        phone = $6,
        email = $7,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $8 AND is_active = true
      RETURNING *
    `;

    const values = [
      tax_code || null,
      name,
      short_name || null,
      address || null,
      contact_person || null,
      phone || null,
      email || null,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateCustomer:', error);
    throw error;
  }
};

/**
 * Xóa khách hàng (soft delete)
 * @param {number} id - ID của khách hàng
 * @returns {boolean} True nếu xóa thành công
 */
const deleteCustomer = async (id) => {
  try {
    const query = `
      UPDATE customers 
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteCustomer:', error);
    throw error;
  }
};

/**
 * Kiểm tra mã số thuế đã tồn tại
 * @param {string} taxCode - Mã số thuế
 * @param {number} excludeId - ID khách hàng cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu mã số thuế đã tồn tại
 */
const checkTaxCodeExists = async (taxCode, excludeId = null) => {
  try {
    if (!taxCode) return false;

    let query = 'SELECT id FROM customers WHERE tax_code = $1 AND is_active = true';
    const params = [taxCode];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkTaxCodeExists:', error);
    throw error;
  }
};

module.exports = {
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  checkTaxCodeExists
};
