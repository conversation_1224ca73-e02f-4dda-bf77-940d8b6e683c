import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  MenuItem,
  IconButton,
  Tooltip,
  Card,
  CardContent
} from '@mui/material';
import {
  FileDownload as ExportIcon,
  Refresh as RefreshIcon,
  Assessment as ReportIcon
} from '@mui/icons-material';
import { DataGrid, GridColDef } from '@mui/x-data-grid';

// Services
import reportService from '../../services/reportService';
import customerService from '../../services/customerService';

// Types
import { AgingAnalysisItem, AgingAnalysisFilters } from '../../types/report';
import { Customer } from '../../types/customer';

interface AgingReportProps {
  refreshTrigger: number;
  onError: (error: string) => void;
}

const AgingReport: React.FC<AgingReportProps> = ({
  refreshTrigger,
  onError
}) => {
  const [agingData, setAgingData] = useState<AgingAnalysisItem[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);

  // Filters
  const [filters, setFilters] = useState<AgingAnalysisFilters>({
    customerId: undefined,
    asOfDate: new Date().toISOString().split('T')[0],
    includeZeroBalance: false
  });

  // Summary stats
  const [summary, setSummary] = useState({
    totalCustomers: 0,
    totalOutstanding: 0,
    totalOverdue: 0,
    avgOverduePercentage: 0
  });

  // Load data
  const loadAgingAnalysis = async () => {
    try {
      setLoading(true);
      const data = await reportService.getAgingAnalysis(filters);
      setAgingData(data);
      
      // Calculate summary (server already converts to numbers)
      const totalCustomers = data.length;
      const totalOutstanding = data.reduce((sum, item) => sum + (item.total_outstanding || 0), 0);
      const totalOverdue = data.reduce((sum, item) =>
        sum + (item.days_1_30 || 0) + (item.days_31_60 || 0) + (item.days_61_90 || 0) + (item.over_90_days || 0), 0
      );
      const avgOverduePercentage = totalCustomers > 0 ?
        data.reduce((sum, item) => sum + (item.overdue_percentage || 0), 0) / totalCustomers : 0;

      setSummary({
        totalCustomers,
        totalOutstanding,
        totalOverdue,
        avgOverduePercentage
      });
    } catch (error: any) {
      onError(error.message || 'Không thể tải báo cáo aging analysis');
    } finally {
      setLoading(false);
    }
  };

  const loadCustomers = async () => {
    try {
      const response = await customerService.getCustomers({ limit: 1000 });
      // Ensure we're setting an array, handle both response formats
      const customersArray = response.customers || response.data || [];
      setCustomers(Array.isArray(customersArray) ? customersArray : []);
    } catch (error: any) {
      console.error('Error loading customers:', error);
      setCustomers([]); // Set empty array on error to prevent map error
    }
  };

  useEffect(() => {
    loadAgingAnalysis();
  }, [filters, refreshTrigger]);

  useEffect(() => {
    loadCustomers();
  }, []);

  // Handlers
  const handleFilterChange = (field: keyof AgingAnalysisFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleExport = async () => {
    try {
      setExporting(true);
      const blob = await reportService.exportAgingAnalysisCSV(filters);
      const filename = `aging-analysis-${filters.asOfDate || new Date().toISOString().split('T')[0]}.csv`;
      reportService.downloadCSV(blob, filename);
    } catch (error: any) {
      onError(error.message || 'Không thể export báo cáo');
    } finally {
      setExporting(false);
    }
  };

  const handleRefresh = () => {
    loadAgingAnalysis();
  };

  // DataGrid columns
  const columns: GridColDef[] = [
    {
      field: 'customer_name',
      headerName: 'Khách hàng',
      width: 200,
      renderCell: (params) => (
        <Box>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            {params.value}
          </Typography>
          {params.row.tax_code && (
            <Typography variant="caption" color="text.secondary">
              MST: {params.row.tax_code}
            </Typography>
          )}
        </Box>
      )
    },
    {
      field: 'total_outstanding',
      headerName: 'Tổng nợ',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'current_amount',
      headerName: 'Chưa đến hạn',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" color="success.main">
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'days_1_30',
      headerName: '1-30 ngày',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" color="warning.main">
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'days_31_60',
      headerName: '31-60 ngày',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" color="error.main">
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'days_61_90',
      headerName: '61-90 ngày',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" color="error.dark">
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'over_90_days',
      headerName: '>90 ngày',
      width: 120,
      align: 'right',
      renderCell: (params) => (
        <Typography variant="body2" sx={{ color: '#d32f2f', fontWeight: 500 }}>
          {reportService.formatCurrency(params.value)}
        </Typography>
      )
    },
    {
      field: 'overdue_percentage',
      headerName: '% Quá hạn',
      width: 100,
      align: 'center',
      renderCell: (params) => (
        <Typography 
          variant="body2" 
          sx={{ 
            fontWeight: 500,
            color: params.value > 50 ? 'error.main' : params.value > 20 ? 'warning.main' : 'text.primary'
          }}
        >
          {reportService.formatPercentage(params.value)}
        </Typography>
      )
    },
    {
      field: 'max_days_overdue',
      headerName: 'Quá hạn tối đa',
      width: 120,
      align: 'center',
      renderCell: (params) => (
        params.value > 0 ? (
          <Typography variant="body2" color="error.main">
            {params.value} ngày
          </Typography>
        ) : (
          <Typography variant="body2" color="text.secondary">
            -
          </Typography>
        )
      )
    }
  ];

  return (
    <Box>
      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Typography variant="h6" color="primary" sx={{ fontSize: '1rem', fontWeight: 600 }}>
                {summary.totalCustomers}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tổng khách hàng có nợ
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Typography variant="h6" color="error" sx={{ fontSize: '1rem', fontWeight: 600 }}>
                {reportService.formatCurrency(summary.totalOutstanding)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tổng công nợ
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Typography variant="h6" color="warning.main" sx={{ fontSize: '1rem', fontWeight: 600 }}>
                {reportService.formatCurrency(summary.totalOverdue)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tổng quá hạn
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ py: 2 }}>
              <Typography variant="h6" color="info.main" sx={{ fontSize: '1rem', fontWeight: 600 }}>
                {reportService.formatPercentage(summary.avgOverduePercentage)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                % Quá hạn trung bình
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              select
              label="Khách hàng"
              value={filters.customerId || ''}
              onChange={(e) => handleFilterChange('customerId', e.target.value ? parseInt(e.target.value) : undefined)}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="">Tất cả khách hàng</MenuItem>
              {Array.isArray(customers) && customers.map((customer) => (
                <MenuItem key={customer.id} value={customer.id}>
                  {customer.name}
                </MenuItem>
              ))}
            </TextField>
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              type="date"
              label="Ngày tính aging"
              value={filters.asOfDate}
              onChange={(e) => handleFilterChange('asOfDate', e.target.value)}
              InputLabelProps={{ shrink: true }}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            />
          </Grid>
          <Grid item xs={12} sm={3}>
            <TextField
              fullWidth
              size="small"
              select
              label="Bao gồm balance = 0"
              value={filters.includeZeroBalance ? 'true' : 'false'}
              onChange={(e) => handleFilterChange('includeZeroBalance', e.target.value === 'true')}
              sx={{ '& .MuiInputBase-input': { fontSize: '0.85rem' } }}
            >
              <MenuItem value="false">Không</MenuItem>
              <MenuItem value="true">Có</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<ExportIcon />}
                onClick={handleExport}
                disabled={exporting}
                sx={{ fontSize: '0.85rem', height: 40 }}
              >
                {exporting ? 'Đang export...' : 'Export CSV'}
              </Button>
              <Tooltip title="Làm mới">
                <IconButton onClick={handleRefresh} sx={{ height: 40, width: 40 }}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Data Grid */}
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={agingData}
          columns={columns}
          loading={loading}
          getRowId={(row) => row.customer_id}
          pagination
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25 }
            }
          }}
          pageSizeOptions={[25, 50, 100]}
          disableRowSelectionOnClick
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.85rem'
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontSize: '0.85rem',
              fontWeight: 600
            }
          }}
        />
      </Paper>
    </Box>
  );
};

export default AgingReport;
