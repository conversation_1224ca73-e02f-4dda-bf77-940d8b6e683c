const customerModel = require('../models/customerModel');
const { 
  createResponse, 
  createListResponse,
  processQueryParams,
  validateRequiredFields,
  validateEmail,
  validatePhone,
  sanitizeString
} = require('../utils/responseUtils');
const { 
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * L<PERSON>y tất cả khách hàng
 * @route GET /api/v1/customers
 * @access Private
 */
const getAllCustomers = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);
  
  const options = {
    page: pagination.page,
    limit: pagination.limit,
    search: filters.search,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await customerModel.getAllCustomers(options);
  
  const response = createListResponse(
    result.customers,
    result.pagination,
    req
  );

  res.status(200).json(response);
});

/**
 * <PERSON><PERSON><PERSON> khách hàng theo ID
 * @route GET /api/v1/customers/:id
 * @access Private
 */
const getCustomerById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID khách hàng không hợp lệ', ['ID phải là một số nguyên']);
  }

  const customer = await customerModel.getCustomerById(parseInt(id));

  if (!customer) {
    throw new NotFoundError('Không tìm thấy khách hàng', [`Không tìm thấy khách hàng với ID ${id}`]);
  }

  res.status(200).json(createResponse(customer));
});

/**
 * Tạo khách hàng mới
 * @route POST /api/v1/customers
 * @access Private
 */
const createCustomer = asyncHandler(async (req, res) => {
  const {
    tax_code,
    name,
    short_name,
    address,
    contact_person,
    phone,
    email
  } = req.body;

  // Validate required fields
  const requiredFields = ['name', 'tax_code', 'short_name', 'address'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate email format (chỉ khi có dữ liệu)
  if (email && email.trim() && !validateEmail(email)) {
    validationErrors.push({
      field: 'email',
      message: 'Email không đúng định dạng'
    });
  }

  // Validate phone format (chỉ khi có dữ liệu)
  if (phone && phone.trim() && !validatePhone(phone)) {
    validationErrors.push({
      field: 'phone',
      message: 'Số điện thoại không đúng định dạng'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra mã số thuế đã tồn tại
  if (tax_code) {
    const taxCodeExists = await customerModel.checkTaxCodeExists(tax_code);
    if (taxCodeExists) {
      throw new ConflictError(
        'Mã số thuế đã tồn tại',
        ['Mã số thuế này đã được sử dụng bởi khách hàng khác']
      );
    }
  }

  // Sanitize input data
  const customerData = {
    tax_code: sanitizeString(tax_code),
    name: sanitizeString(name),
    short_name: sanitizeString(short_name),
    address: sanitizeString(address),
    contact_person: sanitizeString(contact_person),
    phone: sanitizeString(phone),
    email: sanitizeString(email)
  };

  // TODO: Sử dụng req.user.id khi có authentication
  const createdBy = req.user?.id || 1; // Tạm thời sử dụng user ID = 1
  const newCustomer = await customerModel.createCustomer(customerData, createdBy);

  res.status(201).json(createResponse(newCustomer));
});

/**
 * Cập nhật khách hàng
 * @route PUT /api/v1/customers/:id
 * @access Private
 */
const updateCustomer = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    tax_code,
    name,
    short_name,
    address,
    contact_person,
    phone,
    email
  } = req.body;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID khách hàng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Validate required fields
  const requiredFields = ['name'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate email format
  if (email && !validateEmail(email)) {
    validationErrors.push({
      field: 'email',
      message: 'Email không đúng định dạng'
    });
  }

  // Validate phone format
  if (phone && !validatePhone(phone)) {
    validationErrors.push({
      field: 'phone',
      message: 'Số điện thoại không đúng định dạng'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra khách hàng tồn tại
  const existingCustomer = await customerModel.getCustomerById(parseInt(id));
  if (!existingCustomer) {
    throw new NotFoundError('Không tìm thấy khách hàng', [`Không tìm thấy khách hàng với ID ${id}`]);
  }

  // Kiểm tra mã số thuế đã tồn tại (loại trừ khách hàng hiện tại)
  if (tax_code) {
    const taxCodeExists = await customerModel.checkTaxCodeExists(tax_code, parseInt(id));
    if (taxCodeExists) {
      throw new ConflictError(
        'Mã số thuế đã tồn tại',
        ['Mã số thuế này đã được sử dụng bởi khách hàng khác']
      );
    }
  }

  // Sanitize input data
  const customerData = {
    tax_code: sanitizeString(tax_code),
    name: sanitizeString(name),
    short_name: sanitizeString(short_name),
    address: sanitizeString(address),
    contact_person: sanitizeString(contact_person),
    phone: sanitizeString(phone),
    email: sanitizeString(email)
  };

  const updatedCustomer = await customerModel.updateCustomer(parseInt(id), customerData);

  if (!updatedCustomer) {
    throw new NotFoundError('Không thể cập nhật khách hàng', ['Khách hàng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(updatedCustomer));
});

/**
 * Xóa khách hàng
 * @route DELETE /api/v1/customers/:id
 * @access Private
 */
const deleteCustomer = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID khách hàng không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Kiểm tra khách hàng tồn tại
  const existingCustomer = await customerModel.getCustomerById(parseInt(id));
  if (!existingCustomer) {
    throw new NotFoundError('Không tìm thấy khách hàng', [`Không tìm thấy khách hàng với ID ${id}`]);
  }

  const deleted = await customerModel.deleteCustomer(parseInt(id));

  if (!deleted) {
    throw new NotFoundError('Không thể xóa khách hàng', ['Khách hàng có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(
    { message: 'Xóa khách hàng thành công', id: parseInt(id) }
  ));
});

module.exports = {
  getAllCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer
};
