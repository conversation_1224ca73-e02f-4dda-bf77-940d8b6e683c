# Laundry Management System - Development Rules

This directory contains the development rules, guidelines, and documentation for the Laundry Management System project.

## Files Overview

### Core Development Rules
1. **`1. project_overview_laundry.md`** - Project structure, technologies, and domain models
2. **`2. technical_design_documentation_rule.md`** - Rules for creating technical design documents
3. **`3. break_down_rule.md`** - Rules for breaking down features into actionable tasks
4. **`4. implementation_rule.md`** - Coding standards and implementation guidelines

### Reference Documentation
5. **`api-endpoints.md`** - Complete API endpoints reference
6. **`database-schema.md`** - Database schema overview (auto-updated)

## Usage

### For AI Assistant
When working on this project, the AI assistant should:
1. Read all files in this directory first
2. Follow the coding standards and patterns defined here
3. Use the API endpoints reference for understanding existing APIs
4. Refer to database schema for understanding data relationships

### For Developers
1. **Before starting new features:** Review the technical design documentation rule
2. **During implementation:** Follow the implementation rule and coding standards
3. **When adding new APIs:** Update `api-endpoints.md`
4. **After database changes:** Run `npm run update-schema-docs` to update schema documentation

## Commands

### Update Database Schema Documentation
```bash
# Update main schema file
npm run update-schema-docs

# Generate detailed schema report
npm run update-schema-docs:detailed

# Using PowerShell (Windows)
.\scripts\update-schema-docs.ps1
.\scripts\update-schema-docs.ps1 -Detailed
```

### Development Workflow
```bash
# Start development
npm run dev

# Install dependencies
npm run install-deps

# Build for production
npm run build
```

## Key Principles

### 1. Vietnamese Business Focus
- All user-facing text in Vietnamese
- Date format: dd/mm/yyyy
- Currency format: 1.000.000 VND
- Business rules aligned with Vietnamese laundry management practices

### 2. Contract-Centric Workflow
- Customers → Contracts → Contract Prices → Daily Production
- All major operations revolve around contracts
- Maintain contract validity and pricing history

### 3. Technology Stack
- **Backend:** Node.js + Express + PostgreSQL
- **Frontend:** React + TypeScript + MUI
- **Database:** PostgreSQL with proper indexing and constraints
- **Authentication:** JWT (currently disabled for development)

### 4. Code Quality
- TypeScript for type safety
- Comprehensive error handling
- Vietnamese error messages
- Proper database relationships and constraints
- Responsive design optimized for laptops

## File Maintenance

### Auto-Updated Files
- `database-schema.md` - Updated via `npm run update-schema-docs`

### Manually Maintained Files
- `api-endpoints.md` - Update when adding new API endpoints
- All rule files - Update when changing development processes
- This README - Update when adding new files or changing processes

## Development Environment

### Database
- **Name:** tinhtam-hp
- **Type:** PostgreSQL
- **Connection:** See `server/.env.example`

### Ports
- **Server:** 8500 (production) / 5000 (development)
- **Client:** 5373 (primary) / 5374 (fallback)

### Environment Variables
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tinhtam-hp
DB_USER=postgres
DB_PASSWORD=110591
JWT_SECRET=your_secret_key
PORT=8500
CLIENT_URL=http://localhost:5373
```

## Contributing

1. **Read the rules** in this directory before making changes
2. **Follow the workflow** defined in the implementation rule
3. **Update documentation** when making significant changes
4. **Test thoroughly** with Vietnamese business scenarios
5. **Maintain consistency** with existing patterns and conventions

## Support

For questions about the development rules or project structure:
1. Review the relevant rule files in this directory
2. Check the API endpoints reference
3. Examine the database schema documentation
4. Follow the established patterns in the codebase

---

**Note:** This directory is essential for maintaining consistency and quality in the Laundry Management System development. All team members and AI assistants should familiarize themselves with these rules before contributing to the project.
