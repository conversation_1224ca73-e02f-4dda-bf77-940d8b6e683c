# Database Schema Overview - Laundry Management System

> **Auto-generated:** This file is automatically updated by running `npm run update-schema-docs`
> **Last Updated:** 10:34:27 12/06/2025

## Database Information
- **Database Name:** tinhtam-hp
- **Database Type:** PostgreSQL
- **Encoding:** UTF-8
- **Connection Pool:** Max 20 connections

## Core Tables

### 1. users (Authentication & User Management)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | User ID |
| name | VARCHAR(100) | NOT NULL | Full name |
| email | VARCHAR(100) | NOT NULL, UNIQUE | Email address |
| password | VARCHAR(255) | NOT NULL | Hashed password |
| position | VARCHAR(100) | | Job position |
| is_active | BOOLEAN | DEFAULT TRUE | Active status |
| last_login | TIMESTAMP | | Last login time |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_users_email` ON (email)
- `idx_users_is_active` ON (is_active)

### 2. customers (Customer Management)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Customer ID |
| tax_code | VARCHAR(20) | UNIQUE | Tax code (MST) |
| name | VARCHAR(200) | NOT NULL | Customer name |
| short_name | VARCHAR(100) | | Short name |
| address | TEXT | | Address |
| contact_person | VARCHAR(100) | | Contact person |
| phone | VARCHAR(20) | | Phone number |
| email | VARCHAR(100) | | Email address |
| is_active | BOOLEAN | DEFAULT TRUE | Active status |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_customers_name` ON (name)
- `idx_customers_tax_code` ON (tax_code)
- `idx_customers_short_name` ON (short_name)
- `idx_customers_is_active` ON (is_active)
- `idx_customers_created_at` ON (created_at)

**Constraints:**
- `chk_customers_name_not_empty`: name cannot be empty
- `chk_customers_tax_code_format`: tax_code format validation
- `chk_customers_email_format`: email format validation

### 3. products (Product Catalog)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Product ID |
| code | VARCHAR(50) | UNIQUE, NOT NULL | Product code (SP001, SP002) |
| name | VARCHAR(200) | NOT NULL | Product name |
| description | TEXT | | Product description |
| unit_type | VARCHAR(50) | NOT NULL | Unit type (kg, piece, set, meter) |
| is_active | BOOLEAN | DEFAULT TRUE | Active status |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_products_code` ON (code)
- `idx_products_name` ON (name)
- `idx_products_unit_type` ON (unit_type)
- `idx_products_is_active` ON (is_active)

### 4. contracts (Contract Management)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Contract ID |
| contract_number | VARCHAR(100) | UNIQUE, NOT NULL | Contract number (HD001/2024) |
| customer_id | INTEGER | NOT NULL, REFERENCES customers(id) | Customer reference |
| contract_name | VARCHAR(200) | NOT NULL | Contract name |
| start_date | DATE | NOT NULL | Start date |
| end_date | DATE | | End date |
| status | VARCHAR(20) | DEFAULT 'active' | Status (active, paused, terminated, expired) |
| short_name | VARCHAR(100) | | Short name (auto-populated) |
| notes | TEXT | | Notes |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_contracts_contract_number` ON (contract_number)
- `idx_contracts_customer` ON (customer_id)
- `idx_contracts_status` ON (status)
- `idx_contracts_dates` ON (start_date, end_date)

### 5. contract_prices (Contract Pricing)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Price ID |
| contract_id | INTEGER | NOT NULL, REFERENCES contracts(id) | Contract reference |
| product_id | INTEGER | NOT NULL, REFERENCES products(id) | Product reference |
| price | DECIMAL(15,2) | NOT NULL, CHECK (price > 0) | Unit price (VND) |
| effective_date | DATE | NOT NULL | Effective date |
| expiry_date | DATE | | Expiry date |
| is_active | BOOLEAN | DEFAULT TRUE | Active status |
| notes | TEXT | | Notes |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_contract_prices_contract` ON (contract_id)
- `idx_contract_prices_product` ON (product_id)
- `idx_contract_prices_active` ON (is_active)
- `idx_contract_prices_dates` ON (effective_date, expiry_date)

**Constraints:**
- `unique_contract_product_date`: UNIQUE(contract_id, product_id, effective_date)
- `check_price_positive`: price > 0
- `check_date_order`: expiry_date >= effective_date OR expiry_date IS NULL

### 6. daily_production (Production Tracking)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Production ID |
| production_date | DATE | NOT NULL | Production date |
| contract_id | INTEGER | NOT NULL, REFERENCES contracts(id) | Contract reference |
| product_id | INTEGER | NOT NULL, REFERENCES products(id) | Product reference |
| quantity | DECIMAL(10,3) | NOT NULL | Quantity (3 decimal places) |
| unit_price | DECIMAL(15,2) | NOT NULL | Unit price at time of entry |
| total_amount | DECIMAL(15,2) | NOT NULL | Total amount (quantity * unit_price) |
| status | VARCHAR(20) | DEFAULT 'pending' | Status (pending, confirmed, cancelled) |
| notes | TEXT | | Notes |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| updated_by | INTEGER | REFERENCES users(id) | Last updater |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_daily_production_date` ON (production_date)
- `idx_daily_production_contract` ON (contract_id)
- `idx_daily_production_product` ON (product_id)
- `idx_daily_production_date_contract` ON (production_date, contract_id)
- `idx_daily_production_status` ON (status)

**Constraints:**
- `check_quantity_positive`: quantity > 0
- `check_unit_price_positive`: unit_price > 0
- `check_total_amount_positive`: total_amount > 0

### 7. receivables (Debt Management)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Receivable ID |
| customer_id | INTEGER | NOT NULL, REFERENCES customers(id) | Customer reference |
| contract_id | INTEGER | NOT NULL, REFERENCES contracts(id) | Contract reference |
| invoice_number | VARCHAR(50) | UNIQUE, NOT NULL | Invoice number |
| transaction_date | DATE | NOT NULL | Transaction date |
| due_date | DATE | NOT NULL | Due date |
| description | TEXT | NOT NULL | Description |
| original_amount | DECIMAL(15,2) | NOT NULL, CHECK (original_amount > 0) | Original amount |
| currency | VARCHAR(3) | DEFAULT 'VND' | Currency |
| status | VARCHAR(20) | DEFAULT 'active' | Status (active, paid, cancelled, overdue) |
| payment_terms | INTEGER | DEFAULT 30 | Payment terms (days) |
| notes | TEXT | | Notes |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_receivables_customer` ON (customer_id)
- `idx_receivables_contract` ON (contract_id)
- `idx_receivables_invoice` ON (invoice_number)
- `idx_receivables_status` ON (status)
- `idx_receivables_dates` ON (transaction_date, due_date)

### 8. payments (Payment Tracking)
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | SERIAL | PRIMARY KEY | Payment ID |
| receivable_id | INTEGER | NOT NULL, REFERENCES receivables(id) | Receivable reference |
| customer_id | INTEGER | NOT NULL, REFERENCES customers(id) | Customer reference |
| payment_date | DATE | NOT NULL | Payment date |
| amount | DECIMAL(15,2) | NOT NULL, CHECK (amount > 0) | Payment amount |
| payment_method | VARCHAR(50) | DEFAULT 'cash' | Payment method |
| reference_number | VARCHAR(100) | | Reference number |
| notes | TEXT | | Notes |
| created_by | INTEGER | REFERENCES users(id) | Creator |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Creation time |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | Last update time |

**Indexes:**
- `idx_payments_receivable` ON (receivable_id)
- `idx_payments_customer` ON (customer_id)
- `idx_payments_date` ON (payment_date)
- `idx_payments_method` ON (payment_method)

## Database Functions & Triggers

### 1. update_updated_at_column()
- **Type:** Function
- **Purpose:** Automatically update updated_at column on row updates
- **Language:** PL/pgSQL

### 2. Auto-update Triggers
- `update_customers_updated_at` ON customers
- `update_users_updated_at` ON users
- `update_products_updated_at` ON products
- `update_contracts_updated_at` ON contracts
- `update_contract_prices_updated_at` ON contract_prices
- `update_daily_production_updated_at` ON daily_production
- `update_receivables_updated_at` ON receivables
- `update_payments_updated_at` ON payments

## Entity Relationships

```
users (1) -----> (N) customers [created_by]
users (1) -----> (N) products [created_by]
users (1) -----> (N) contracts [created_by]
users (1) -----> (N) contract_prices [created_by]
users (1) -----> (N) daily_production [created_by, updated_by]
users (1) -----> (N) receivables [created_by]
users (1) -----> (N) payments [created_by]

customers (1) -----> (N) contracts [customer_id]
customers (1) -----> (N) receivables [customer_id]
customers (1) -----> (N) payments [customer_id]

contracts (1) -----> (N) contract_prices [contract_id]
contracts (1) -----> (N) daily_production [contract_id]
contracts (1) -----> (N) receivables [contract_id]

products (1) -----> (N) contract_prices [product_id]
products (1) -----> (N) daily_production [product_id]

receivables (1) -----> (N) payments [receivable_id]
```

## Business Rules

1. **Product Codes:** Auto-generated in format SP001, SP002, etc.
2. **Contract Numbers:** Format HD001/2024, HD002/2024, etc.
3. **Price Validation:** All prices must be positive
4. **Date Validation:** End dates must be >= start dates
5. **Currency:** Default currency is VND
6. **Audit Trail:** All tables have created_by, created_at, updated_at
7. **Soft Delete:** Uses is_active flag instead of hard delete
8. **Vietnamese Formatting:** Dates in dd/mm/yyyy, numbers with thousand separators

## Performance Considerations

1. **Indexing:** Strategic indexes on frequently queried columns
2. **Connection Pooling:** Max 20 connections in pool
3. **Query Optimization:** Proper JOIN strategies for related data
4. **Date Range Queries:** Optimized with composite indexes
5. **Text Search:** Indexes on name fields for fast searching

## Security Features

1. **Password Hashing:** User passwords are hashed
2. **Input Validation:** Database constraints prevent invalid data
3. **Foreign Key Constraints:** Maintain referential integrity
4. **Check Constraints:** Validate business rules at database level
5. **UTF-8 Encoding:** Support for Vietnamese characters
