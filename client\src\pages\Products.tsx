/**
 * Products Page
 * Trang quản lý sản phẩm
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Snackbar,
} from '@mui/material';
import { useSearchParams } from 'react-router-dom';
import ProductTable from '../components/products/ProductTable';
import ProductDialog from '../components/products/ProductDialog';
import ProductDetail from '../components/products/ProductDetail';
import ConfirmDialog from '../components/common/ConfirmDialog';
import { productService } from '../services/api';
import {
  Product,
  ProductFilterOptions,
  ProductCreateRequest,
  ProductUpdateRequest
} from '../types/product';

const Products: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Pagination & Filtering
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState('created_at');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ProductFilterOptions>({});

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dialogError, setDialogError] = useState<string | null>(null);

  // Toast
  const [toast, setToast] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load products
  const loadProducts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const options: ProductFilterOptions = {
        page: page + 1, // API sử dụng 1-based pagination
        limit: rowsPerPage,
        search: searchQuery || undefined,
        sortBy: orderBy as any,
        sortOrder: order.toUpperCase() as 'ASC' | 'DESC',
        ...filters,
      };

      const response = await productService.getAll(options);

      if (response.success) {
        setProducts(response.data);
        setTotalCount(response.pagination?.total || 0);
      } else {
        throw new Error(response.message || 'Không thể tải danh sách sản phẩm');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi tải dữ liệu');
      setProducts([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, orderBy, order, searchQuery, filters]);

  // Load products when dependencies change
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  // URL sync
  useEffect(() => {
    const params = new URLSearchParams();
    if (page > 0) params.set('page', (page + 1).toString());
    if (rowsPerPage !== 10) params.set('limit', rowsPerPage.toString());
    if (searchQuery) params.set('search', searchQuery);
    if (orderBy !== 'created_at') params.set('sortBy', orderBy);
    if (order !== 'desc') params.set('order', order);

    setSearchParams(params);
  }, [page, rowsPerPage, searchQuery, orderBy, order, setSearchParams]);

  // Event handlers
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  }, []);

  const handleSort = useCallback((property: string, direction: 'asc' | 'desc') => {
    setOrderBy(property);
    setOrder(direction);
    setPage(0);
  }, []);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(0);
  }, []);

  const handleFilter = useCallback((newFilters: ProductFilterOptions) => {
    setFilters(newFilters);
    setPage(0);
  }, []);

  const handleAdd = useCallback(() => {
    setSelectedProduct(null);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleEdit = useCallback((product: Product) => {
    setSelectedProduct(product);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleView = useCallback((product: Product) => {
    setSelectedProduct(product);
    setDetailOpen(true);
  }, []);

  const handleDelete = useCallback((product: Product) => {
    setSelectedProduct(product);
    setDeleteDialogOpen(true);
  }, []);

  const handleDialogClose = useCallback(() => {
    setDialogOpen(false);
    setSelectedProduct(null);
    setDialogError(null);
  }, []);

  const handleDetailClose = useCallback(() => {
    setDetailOpen(false);
    setSelectedProduct(null);
  }, []);

  const handleDialogSubmit = useCallback(async (data: ProductCreateRequest | ProductUpdateRequest) => {
    setDialogLoading(true);
    setDialogError(null);

    try {
      if (selectedProduct) {
        // Update
        const response = await productService.update(selectedProduct.id, data as ProductUpdateRequest);
        if (response.success) {
          setToast({
            open: true,
            message: 'Cập nhật sản phẩm thành công',
            severity: 'success',
          });
          handleDialogClose();
          loadProducts();
        } else {
          throw new Error(response.message || 'Không thể cập nhật sản phẩm');
        }
      } else {
        // Create
        const response = await productService.create(data as ProductCreateRequest);
        if (response.success) {
          setToast({
            open: true,
            message: 'Thêm sản phẩm thành công',
            severity: 'success',
          });
          handleDialogClose();
          loadProducts();
        } else {
          throw new Error(response.message || 'Không thể thêm sản phẩm');
        }
      }
    } catch (err: any) {
      setDialogError(err.message || 'Có lỗi xảy ra');
    } finally {
      setDialogLoading(false);
    }
  }, [selectedProduct, handleDialogClose, loadProducts]);

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedProduct) return;

    try {
      const response = await productService.delete(selectedProduct.id);
      if (response.success) {
        setToast({
          open: true,
          message: 'Xóa sản phẩm thành công',
          severity: 'success',
        });
        setDeleteDialogOpen(false);
        setSelectedProduct(null);
        loadProducts();
      } else {
        throw new Error(response.message || 'Không thể xóa sản phẩm');
      }
    } catch (err: any) {
      setToast({
        open: true,
        message: err.message || 'Có lỗi xảy ra khi xóa sản phẩm',
        severity: 'error',
      });
    }
  }, [selectedProduct, loadProducts]);

  const handleExport = useCallback(async () => {
    try {
      const blob = await productService.exportToExcel(filters);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `products_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setToast({
        open: true,
        message: 'Xuất file Excel thành công',
        severity: 'success',
      });
    } catch (err: any) {
      setToast({
        open: true,
        message: 'Có lỗi xảy ra khi xuất file',
        severity: 'error',
      });
    }
  }, [filters]);

  const handleToastClose = useCallback(() => {
    setToast(prev => ({ ...prev, open: false }));
  }, []);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Quản lý sản phẩm
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quản lý thông tin sản phẩm trong hệ thống
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <ProductTable
        products={products}
        loading={loading}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        orderBy={orderBy}
        order={order}
        searchQuery={searchQuery}
        filters={filters}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSort={handleSort}
        onSearch={handleSearch}
        onFilter={handleFilter}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        onRefresh={loadProducts}
        onExport={handleExport}
      />

      {/* Product Dialog */}
      <ProductDialog
        open={dialogOpen}
        product={selectedProduct}
        loading={dialogLoading}
        error={dialogError}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
      />

      {/* Product Detail */}
      <ProductDetail
        open={detailOpen}
        product={selectedProduct}
        onClose={handleDetailClose}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Delete Confirmation */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa sản phẩm"
        message={`Bạn có chắc chắn muốn xóa sản phẩm "${selectedProduct?.name}"? Hành động này không thể hoàn tác.`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
        confirmText="Xóa"
        cancelText="Hủy"
        severity="error"
      />

      {/* Toast */}
      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleToastClose} severity={toast.severity}>
          {toast.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Products;
