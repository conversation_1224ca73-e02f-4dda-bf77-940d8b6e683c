/**
 * Product Detail Component
 * Hiển thị thông tin chi tiết của sản phẩm
 */

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  Category as CategoryIcon,
  Description as DescriptionIcon,
  ToggleOn as ActiveIcon,
  ToggleOff as InactiveIcon,
} from '@mui/icons-material';
import { Product, PRODUCT_UNIT_TYPE_OPTIONS, PRODUCT_STATUS_OPTIONS } from '../../types/product';

interface ProductDetailProps {
  open: boolean;
  product: Product | null;
  onClose: () => void;
  onEdit?: (product: Product) => void;
  onDelete?: (product: Product) => void;
  canEdit?: boolean;
  canDelete?: boolean;
}

const ProductDetail: React.FC<ProductDetailProps> = ({
  open,
  product,
  onClose,
  onEdit,
  onDelete,
  canEdit = true,
  canDelete = true,
}) => {
  if (!product) return null;

  const getUnitTypeLabel = (unitType: string) => {
    const option = PRODUCT_UNIT_TYPE_OPTIONS.find(opt => opt.value === unitType);
    return option?.label || unitType;
  };

  const getStatusInfo = (isActive: boolean) => {
    return PRODUCT_STATUS_OPTIONS.find(opt => opt.value === isActive);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const statusInfo = getStatusInfo(product.is_active);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: 500 }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" component="div">
              Chi tiết sản phẩm
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Thông tin đầy đủ về sản phẩm {product.code}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {canEdit && (
              <Tooltip title="Chỉnh sửa">
                <IconButton onClick={() => onEdit?.(product)} color="primary">
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}
            {canDelete && (
              <Tooltip title="Xóa">
                <IconButton onClick={() => onDelete?.(product)} color="error">
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Đóng">
              <IconButton onClick={onClose}>
                <CloseIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Grid container spacing={3}>
          {/* Thông tin cơ bản */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CategoryIcon color="primary" />
                  Thông tin cơ bản
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Mã sản phẩm
                      </Typography>
                      <Typography variant="body1" fontWeight={600}>
                        {product.code}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Trạng thái
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        {product.is_active ? (
                          <ActiveIcon color="success" />
                        ) : (
                          <InactiveIcon color="disabled" />
                        )}
                        <Chip
                          label={statusInfo?.label || 'Không xác định'}
                          color={statusInfo?.color || 'default'}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Tên sản phẩm
                      </Typography>
                      <Typography variant="h6" color="primary">
                        {product.name}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Đơn vị tính
                      </Typography>
                      <Typography variant="body1">
                        {getUnitTypeLabel(product.unit_type)}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Mô tả */}
          {product.description && (
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DescriptionIcon color="primary" />
                    Mô tả
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'pre-wrap' }}>
                    {product.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* Thông tin hệ thống */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon color="primary" />
                  Thông tin hệ thống
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Ngày tạo
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(product.created_at)}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        Ngày cập nhật
                      </Typography>
                      <Typography variant="body2">
                        {formatDate(product.updated_at)}
                      </Typography>
                    </Box>
                  </Grid>

                  {product.created_by_name && (
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="caption" color="text.secondary" display="block">
                          Người tạo
                        </Typography>
                        <Typography variant="body2">
                          {product.created_by_name}
                        </Typography>
                      </Box>
                    </Grid>
                  )}

                  <Grid item xs={12} sm={6}>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary" display="block">
                        ID hệ thống
                      </Typography>
                      <Typography variant="body2" fontFamily="monospace">
                        #{product.id}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
          {canEdit && (
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => onEdit?.(product)}
            >
              Chỉnh sửa
            </Button>
          )}
          <Button
            variant="contained"
            onClick={onClose}
          >
            Đóng
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default ProductDetail;
