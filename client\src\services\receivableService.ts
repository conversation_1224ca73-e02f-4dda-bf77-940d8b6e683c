import api from './api';
import {
  Receivable,
  CreateReceivableData,
  UpdateReceivableData,
  ReceivableFilters,
  ReceivablesResponse,
  BulkCreateReceivablesData,
  BulkCreateReceivablesResult
} from '../types/receivable';

/**
 * Receivable Service
 * Xử lý các API calls liên quan đến receivables (Công nợ phải thu)
 */

const BASE_URL = '/receivables';

export const receivableService = {
  /**
   * Lấy danh sách receivables với filtering và pagination
   */
  async getReceivables(filters: ReceivableFilters = {}): Promise<ReceivablesResponse> {
    try {
      const params = new URLSearchParams();
      
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.customerId) params.append('customerId', filters.customerId.toString());
      if (filters.contractId) params.append('contractId', filters.contractId.toString());
      if (filters.status) params.append('status', filters.status);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

      const response = await api.get(`${BASE_URL}?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching receivables:', error);
      throw error;
    }
  },

  /**
   * Lấy receivable theo ID
   */
  async getReceivableById(id: number): Promise<Receivable> {
    try {
      const response = await api.get(`${BASE_URL}/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching receivable:', error);
      throw error;
    }
  },

  /**
   * Tạo receivable mới
   */
  async createReceivable(data: CreateReceivableData): Promise<Receivable> {
    try {
      const response = await api.post(BASE_URL, data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating receivable:', error);
      throw error;
    }
  },

  /**
   * Cập nhật receivable
   */
  async updateReceivable(id: number, data: UpdateReceivableData): Promise<Receivable> {
    try {
      const response = await api.put(`${BASE_URL}/${id}`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error updating receivable:', error);
      throw error;
    }
  },

  /**
   * Xóa receivable
   */
  async deleteReceivable(id: number): Promise<void> {
    try {
      await api.delete(`${BASE_URL}/${id}`);
    } catch (error) {
      console.error('Error deleting receivable:', error);
      throw error;
    }
  },

  /**
   * Tạo receivables từ dữ liệu sản xuất
   */
  async bulkCreateReceivables(data: BulkCreateReceivablesData): Promise<BulkCreateReceivablesResult> {
    try {
      const response = await api.post(`${BASE_URL}/bulk-create`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error bulk creating receivables:', error);
      throw error;
    }
  },

  /**
   * Tạo receivable từ sản lượng đã chọn
   */
  async createFromProduction(data: any): Promise<Receivable> {
    try {
      const response = await api.post(`${BASE_URL}/from-production`, data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating receivable from production:', error);
      throw error;
    }
  },

  /**
   * Validate receivable data trước khi submit
   */
  validateReceivableData(data: CreateReceivableData | UpdateReceivableData): string[] {
    const errors: string[] = [];

    if (!data.customer_id) {
      errors.push('Khách hàng là bắt buộc');
    }

    if (!data.contract_id) {
      errors.push('Hợp đồng là bắt buộc');
    }

    if (!data.invoice_number?.trim()) {
      errors.push('Số hóa đơn là bắt buộc');
    }

    if (!data.transaction_date) {
      errors.push('Ngày giao dịch là bắt buộc');
    }

    if (!data.due_date) {
      errors.push('Ngày đến hạn là bắt buộc');
    }

    if (!data.description?.trim()) {
      errors.push('Mô tả là bắt buộc');
    }

    if (!data.original_amount || data.original_amount <= 0) {
      errors.push('Số tiền phải lớn hơn 0');
    }

    // Validate dates
    if (data.transaction_date && data.due_date) {
      const transactionDate = new Date(data.transaction_date);
      const dueDate = new Date(data.due_date);
      
      if (dueDate < transactionDate) {
        errors.push('Ngày đến hạn không thể trước ngày giao dịch');
      }
    }

    return errors;
  },

  /**
   * Format receivable data cho display
   */
  formatReceivableForDisplay(receivable: Receivable) {
    return {
      ...receivable,
      original_amount_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(receivable.original_amount),
      total_paid_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(receivable.total_paid),
      remaining_balance_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(receivable.remaining_balance),
      transaction_date_formatted: new Date(receivable.transaction_date).toLocaleDateString('vi-VN'),
      due_date_formatted: new Date(receivable.due_date).toLocaleDateString('vi-VN'),
      status_label: this.getStatusLabel(receivable.balance_status),
      status_color: this.getStatusColor(receivable.balance_status)
    };
  },

  /**
   * Lấy label cho status
   */
  getStatusLabel(status: string): string {
    const statusLabels: Record<string, string> = {
      'active': 'Hoạt động',
      'overdue': 'Quá hạn',
      'paid': 'Đã thanh toán',
      'cancelled': 'Đã hủy'
    };
    return statusLabels[status] || status;
  },

  /**
   * Lấy color cho status
   */
  getStatusColor(status: string): 'success' | 'warning' | 'error' | 'info' {
    const statusColors: Record<string, 'success' | 'warning' | 'error' | 'info'> = {
      'active': 'info',
      'overdue': 'error',
      'paid': 'success',
      'cancelled': 'warning'
    };
    return statusColors[status] || 'info';
  }
};

export default receivableService;
