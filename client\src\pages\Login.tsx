import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  InputAdornment,
  IconButton,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email as EmailIcon,
  Lock as LockIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';

// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// import { useAuth } from '../contexts/AuthContext';
// import { validateLoginForm } from '../utils/validation';

/**
 * Login Form Data Interface
 */
interface LoginFormData {
  email: string;
  password: string;
}

/**
 * Login Page Component
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 * Trang này sẽ tự động redirect đến dashboard
 */
const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Get redirect path from location state
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  /**
   * Auto redirect to dashboard (since authentication is disabled)
   */
  useEffect(() => {
    // Tự động redirect đến dashboard sau 1 giây
    const timer = setTimeout(() => {
      navigate(from, { replace: true });
    }, 1000);

    return () => clearTimeout(timer);
  }, [navigate, from]);

  // NOTE: Các function này không cần thiết khi authentication bị tắt
  // Nhưng giữ lại để tránh lỗi trong JSX

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <Card
          elevation={8}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
          }}
        >
          <CardContent sx={{ p: 4 }}>
            {/* Header */}
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                mb: 4,
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 64,
                  height: 64,
                  borderRadius: '50%',
                  backgroundColor: 'primary.main',
                  mb: 2,
                }}
              >
                <BusinessIcon sx={{ fontSize: 32, color: 'white' }} />
              </Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  mb: 1,
                  fontSize: '1.75rem',
                }}
              >
                Hệ thống quản lý giặt là
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ textAlign: 'center' }}
              >
                Đang chuyển hướng...
              </Typography>
            </Box>

            {/* Error Alert */}
            {authState.error && (
              <Alert
                severity="error"
                sx={{ mb: 3 }}
                onClose={clearError}
              >
                {authState.error}
              </Alert>
            )}

            {/* Login Form */}
            <Box component="form" onSubmit={handleSubmit} noValidate>
              {/* Email Field */}
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleInputChange('email')}
                onKeyPress={handleKeyPress}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={authState.isLoading}
                size="medium"
                sx={{
                  mb: 3,
                  '& .MuiInputBase-root': {
                    height: '56px', // Tăng chiều cao của input
                    fontSize: '1rem',
                  },
                  '& .MuiInputBase-input': {
                    padding: '16px 14px', // Tăng padding bên trong
                  },
                  '& .MuiFormLabel-root': {
                    fontSize: '1rem',
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon color="action" fontSize="medium" />
                    </InputAdornment>
                  ),
                }}
                placeholder="Nhập email của bạn"
              />

              {/* Password Field */}
              <TextField
                fullWidth
                label="Mật khẩu"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange('password')}
                onKeyPress={handleKeyPress}
                error={!!formErrors.password}
                helperText={formErrors.password}
                disabled={authState.isLoading}
                size="medium"
                sx={{
                  mb: 4,
                  '& .MuiInputBase-root': {
                    height: '56px', // Tăng chiều cao của input
                    fontSize: '1rem',
                  },
                  '& .MuiInputBase-input': {
                    padding: '16px 14px', // Tăng padding bên trong
                  },
                  '& .MuiFormLabel-root': {
                    fontSize: '1rem',
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LockIcon color="action" fontSize="medium" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={handleTogglePassword}
                        edge="end"
                        size="medium"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                placeholder="Nhập mật khẩu của bạn"
              />

              {/* Submit Button */}
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={authState.isLoading}
                sx={{
                  height: '56px', // Đồng bộ chiều cao với input fields
                  py: 2,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: 2,
                  textTransform: 'none',
                  boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)',
                  '&:hover': {
                    boxShadow: '0 6px 16px rgba(99, 102, 241, 0.4)',
                  },
                  '&:disabled': {
                    backgroundColor: 'action.disabledBackground',
                  },
                }}
              >
                {authState.isLoading ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                    Đang đăng nhập...
                  </Box>
                ) : (
                  'Đăng nhập'
                )}
              </Button>
            </Box>

            {/* Footer */}
            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                © 2024 Customer Management App. All rights reserved.
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  );
};

export default Login;
