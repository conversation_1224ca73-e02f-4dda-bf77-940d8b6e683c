const { pool } = require('../db');

/**
 * Report Service
 * Xử lý các báo cáo về công nợ và aging analysis
 */

/**
 * Lấy báo cáo aging analysis
 * @param {Object} options - T<PERSON><PERSON> chọn báo cáo
 * @param {number} options.customerId - L<PERSON><PERSON> theo khách hàng
 * @param {string} options.asOfDate - Ngày tính aging (mặc định hôm nay)
 * @param {boolean} options.includeZeroBalance - <PERSON>o gồm khách hàng có balance = 0
 * @returns {Array} Danh sách aging analysis
 */
const getAgingAnalysis = async (options = {}) => {
  const {
    customerId,
    asOfDate = new Date().toISOString().split('T')[0],
    includeZeroBalance = false
  } = options;

  try {
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    if (customerId) {
      whereClause += ` AND aas.customer_id = $${paramIndex}`;
      queryParams.push(customerId);
      paramIndex++;
    }

    if (!includeZeroBalance) {
      whereClause += ' AND aas.total_outstanding > 0';
    }

    const query = `
      SELECT 
        aas.customer_id,
        aas.customer_name,
        aas.tax_code,
        aas.short_name,
        aas.total_invoices,
        aas.active_invoices,
        aas.overdue_invoices,
        aas.paid_invoices,
        aas.total_invoiced,
        aas.total_payments,
        aas.total_outstanding,
        aas.current_amount,
        aas.days_1_30,
        aas.days_31_60,
        aas.days_61_90,
        aas.over_90_days,
        aas.overdue_percentage,
        aas.max_days_overdue,
        aas.oldest_overdue_date,
        -- Tính phần trăm cho từng bucket
        CASE 
          WHEN aas.total_outstanding > 0 THEN 
            ROUND((aas.current_amount / aas.total_outstanding) * 100, 2)
          ELSE 0 
        END as current_percentage,
        CASE 
          WHEN aas.total_outstanding > 0 THEN 
            ROUND((aas.days_1_30 / aas.total_outstanding) * 100, 2)
          ELSE 0 
        END as days_1_30_percentage,
        CASE 
          WHEN aas.total_outstanding > 0 THEN 
            ROUND((aas.days_31_60 / aas.total_outstanding) * 100, 2)
          ELSE 0 
        END as days_31_60_percentage,
        CASE 
          WHEN aas.total_outstanding > 0 THEN 
            ROUND((aas.days_61_90 / aas.total_outstanding) * 100, 2)
          ELSE 0 
        END as days_61_90_percentage,
        CASE 
          WHEN aas.total_outstanding > 0 THEN 
            ROUND((aas.over_90_days / aas.total_outstanding) * 100, 2)
          ELSE 0 
        END as over_90_days_percentage
      FROM aging_analysis_summary aas
      ${whereClause}
      ORDER BY aas.total_outstanding DESC, aas.customer_name
    `;

    const result = await pool.query(query, queryParams);

    // Convert numeric fields to numbers for JavaScript
    const processedRows = result.rows.map(row => ({
      ...row,
      total_invoiced: parseFloat(row.total_invoiced) || 0,
      total_payments: parseFloat(row.total_payments) || 0,
      total_outstanding: parseFloat(row.total_outstanding) || 0,
      current_amount: parseFloat(row.current_amount) || 0,
      days_1_30: parseFloat(row.days_1_30) || 0,
      days_31_60: parseFloat(row.days_31_60) || 0,
      days_61_90: parseFloat(row.days_61_90) || 0,
      over_90_days: parseFloat(row.over_90_days) || 0,
      overdue_percentage: parseFloat(row.overdue_percentage) || 0,
      current_percentage: parseFloat(row.current_percentage) || 0,
      days_1_30_percentage: parseFloat(row.days_1_30_percentage) || 0,
      days_31_60_percentage: parseFloat(row.days_31_60_percentage) || 0,
      days_61_90_percentage: parseFloat(row.days_61_90_percentage) || 0,
      over_90_days_percentage: parseFloat(row.over_90_days_percentage) || 0
    }));

    return processedRows;
  } catch (error) {
    console.error('Error in reportService.getAgingAnalysis:', error);
    throw new Error('Không thể lấy báo cáo aging analysis');
  }
};

/**
 * Lấy tổng hợp công nợ theo khách hàng
 * @param {number} customerId - ID khách hàng
 * @returns {Object} Thông tin tổng hợp công nợ
 */
const getCustomerDebtSummary = async (customerId) => {
  try {
    if (!customerId || isNaN(customerId)) {
      throw new Error('ID khách hàng không hợp lệ');
    }

    // Sử dụng function đã tạo trong database
    const summaryQuery = 'SELECT * FROM get_customer_debt_summary($1)';
    const summaryResult = await pool.query(summaryQuery, [customerId]);
    
    if (summaryResult.rows.length === 0) {
      throw new Error('Không tìm thấy thông tin khách hàng');
    }

    const summary = summaryResult.rows[0];

    // Lấy chi tiết receivables
    const receivablesQuery = `
      SELECT 
        rb.id,
        rb.invoice_number,
        rb.transaction_date,
        rb.due_date,
        rb.description,
        rb.original_amount,
        rb.total_paid,
        rb.remaining_balance,
        rb.balance_status,
        rb.days_overdue,
        rb.current_amount,
        rb.days_1_30,
        rb.days_31_60,
        rb.days_61_90,
        rb.over_90_days,
        con.contract_number,
        con.contract_name
      FROM receivables_balance_fifo rb
      JOIN contracts con ON rb.contract_id = con.id
      WHERE rb.customer_id = $1
      AND rb.remaining_balance > 0
      ORDER BY rb.transaction_date ASC
    `;

    const receivablesResult = await pool.query(receivablesQuery, [customerId]);

    // Lấy lịch sử thanh toán gần đây
    const paymentsQuery = `
      SELECT 
        p.id,
        p.payment_date,
        p.amount,
        p.payment_method,
        p.reference_number,
        p.description,
        p.status
      FROM payments p
      WHERE p.customer_id = $1
      ORDER BY p.payment_date DESC
      LIMIT 10
    `;

    const paymentsResult = await pool.query(paymentsQuery, [customerId]);

    return {
      summary,
      receivables: receivablesResult.rows,
      recent_payments: paymentsResult.rows
    };
  } catch (error) {
    console.error('Error in reportService.getCustomerDebtSummary:', error);
    throw error;
  }
};

/**
 * Lấy tổng hợp công nợ tổng thể
 * @returns {Object} Thông tin tổng hợp toàn bộ hệ thống
 */
const getOverallDebtSummary = async () => {
  try {
    const query = `
      SELECT 
        COUNT(DISTINCT aas.customer_id) as total_customers,
        SUM(aas.total_invoices) as total_invoices,
        SUM(aas.total_invoiced) as total_invoiced,
        SUM(aas.total_payments) as total_payments,
        SUM(aas.total_outstanding) as total_outstanding,
        SUM(aas.current_amount) as total_current,
        SUM(aas.days_1_30) as total_1_30,
        SUM(aas.days_31_60) as total_31_60,
        SUM(aas.days_61_90) as total_61_90,
        SUM(aas.over_90_days) as total_over_90,
        COUNT(CASE WHEN aas.total_outstanding > 0 THEN 1 END) as customers_with_debt,
        COUNT(CASE WHEN aas.overdue_invoices > 0 THEN 1 END) as customers_with_overdue,
        AVG(CASE WHEN aas.total_outstanding > 0 THEN aas.overdue_percentage ELSE 0 END) as avg_overdue_percentage,
        MAX(aas.max_days_overdue) as max_days_overdue_system
      FROM aging_analysis_summary aas
    `;

    const result = await pool.query(query);
    const summary = result.rows[0];

    // Convert numeric fields to numbers
    const processedSummary = {
      ...summary,
      total_invoiced: parseFloat(summary.total_invoiced) || 0,
      total_payments: parseFloat(summary.total_payments) || 0,
      total_outstanding: parseFloat(summary.total_outstanding) || 0,
      total_current: parseFloat(summary.total_current) || 0,
      total_1_30: parseFloat(summary.total_1_30) || 0,
      total_31_60: parseFloat(summary.total_31_60) || 0,
      total_61_90: parseFloat(summary.total_61_90) || 0,
      total_over_90: parseFloat(summary.total_over_90) || 0,
      avg_overdue_percentage: parseFloat(summary.avg_overdue_percentage) || 0
    };

    // Tính phần trăm cho từng bucket
    const totalOutstanding = processedSummary.total_outstanding;
    
    if (totalOutstanding > 0) {
      processedSummary.current_percentage = parseFloat(((processedSummary.total_current / totalOutstanding) * 100).toFixed(2));
      processedSummary.days_1_30_percentage = parseFloat(((processedSummary.total_1_30 / totalOutstanding) * 100).toFixed(2));
      processedSummary.days_31_60_percentage = parseFloat(((processedSummary.total_31_60 / totalOutstanding) * 100).toFixed(2));
      processedSummary.days_61_90_percentage = parseFloat(((processedSummary.total_61_90 / totalOutstanding) * 100).toFixed(2));
      processedSummary.over_90_days_percentage = parseFloat(((processedSummary.total_over_90 / totalOutstanding) * 100).toFixed(2));
    } else {
      processedSummary.current_percentage = 0;
      processedSummary.days_1_30_percentage = 0;
      processedSummary.days_31_60_percentage = 0;
      processedSummary.days_61_90_percentage = 0;
      processedSummary.over_90_days_percentage = 0;
    }

    return processedSummary;
  } catch (error) {
    console.error('Error in reportService.getOverallDebtSummary:', error);
    throw new Error('Không thể lấy tổng hợp công nợ tổng thể');
  }
};

/**
 * Lấy báo cáo công nợ theo hợp đồng
 * @param {Object} options - Tùy chọn báo cáo
 * @param {number} options.contractId - Lọc theo hợp đồng
 * @param {number} options.customerId - Lọc theo khách hàng
 * @returns {Array} Danh sách công nợ theo hợp đồng
 */
const getContractDebtSummary = async (options = {}) => {
  const { contractId, customerId } = options;

  try {
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    if (contractId) {
      whereClause += ` AND cds.contract_id = $${paramIndex}`;
      queryParams.push(contractId);
      paramIndex++;
    }

    if (customerId) {
      whereClause += ` AND cds.customer_id = $${paramIndex}`;
      queryParams.push(customerId);
      paramIndex++;
    }

    const query = `
      SELECT 
        cds.*,
        -- Tính phần trăm overdue
        CASE 
          WHEN cds.total_outstanding > 0 THEN 
            ROUND(((cds.days_1_30 + cds.days_31_60 + cds.days_61_90 + cds.over_90_days) / cds.total_outstanding) * 100, 2)
          ELSE 0 
        END as overdue_percentage
      FROM contract_debt_summary cds
      ${whereClause}
      ORDER BY cds.total_outstanding DESC, cds.contract_number
    `;

    const result = await pool.query(query, queryParams);
    return result.rows;
  } catch (error) {
    console.error('Error in reportService.getContractDebtSummary:', error);
    throw new Error('Không thể lấy báo cáo công nợ theo hợp đồng');
  }
};

/**
 * Lấy lịch sử thanh toán chi tiết
 * @param {Object} options - Tùy chọn truy vấn
 * @param {number} options.customerId - Lọc theo khách hàng
 * @param {number} options.paymentId - Lọc theo payment
 * @param {string} options.startDate - Ngày bắt đầu
 * @param {string} options.endDate - Ngày kết thúc
 * @param {number} options.limit - Giới hạn số bản ghi
 * @returns {Array} Lịch sử thanh toán chi tiết
 */
const getPaymentHistoryDetail = async (options = {}) => {
  const {
    customerId,
    paymentId,
    startDate,
    endDate,
    limit = 100
  } = options;

  try {
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    if (customerId) {
      whereClause += ` AND phd.customer_id = $${paramIndex}`;
      queryParams.push(customerId);
      paramIndex++;
    }

    if (paymentId) {
      whereClause += ` AND phd.payment_id = $${paramIndex}`;
      queryParams.push(paymentId);
      paramIndex++;
    }

    if (startDate) {
      whereClause += ` AND phd.payment_date >= $${paramIndex}`;
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      whereClause += ` AND phd.payment_date <= $${paramIndex}`;
      queryParams.push(endDate);
      paramIndex++;
    }

    const query = `
      SELECT 
        phd.*
      FROM payment_history_detail phd
      ${whereClause}
      ORDER BY phd.payment_date DESC, phd.payment_id DESC, phd.allocation_order ASC
      LIMIT $${paramIndex}
    `;

    queryParams.push(limit);

    const result = await pool.query(query, queryParams);
    return result.rows;
  } catch (error) {
    console.error('Error in reportService.getPaymentHistoryDetail:', error);
    throw new Error('Không thể lấy lịch sử thanh toán chi tiết');
  }
};

/**
 * Export aging analysis to CSV format
 * @param {Object} options - Tùy chọn export
 * @returns {string} CSV content
 */
const exportAgingAnalysisCSV = async (options = {}) => {
  try {
    const data = await getAgingAnalysis(options);
    
    // CSV headers
    const headers = [
      'Khách hàng',
      'Mã số thuế',
      'Tên viết tắt',
      'Tổng hóa đơn',
      'Tổng phát sinh',
      'Đã thanh toán',
      'Còn nợ',
      'Chưa đến hạn',
      '1-30 ngày',
      '31-60 ngày',
      '61-90 ngày',
      'Trên 90 ngày',
      '% Quá hạn'
    ];

    // Convert data to CSV rows
    const rows = data.map(row => [
      row.customer_name,
      row.tax_code || '',
      row.short_name || '',
      row.total_invoices,
      parseFloat(row.total_invoiced).toLocaleString('vi-VN'),
      parseFloat(row.total_payments).toLocaleString('vi-VN'),
      parseFloat(row.total_outstanding).toLocaleString('vi-VN'),
      parseFloat(row.current_amount).toLocaleString('vi-VN'),
      parseFloat(row.days_1_30).toLocaleString('vi-VN'),
      parseFloat(row.days_31_60).toLocaleString('vi-VN'),
      parseFloat(row.days_61_90).toLocaleString('vi-VN'),
      parseFloat(row.over_90_days).toLocaleString('vi-VN'),
      `${row.overdue_percentage}%`
    ]);

    // Combine headers and rows
    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    return csvContent;
  } catch (error) {
    console.error('Error in reportService.exportAgingAnalysisCSV:', error);
    throw new Error('Không thể export báo cáo aging analysis');
  }
};

module.exports = {
  getAgingAnalysis,
  getCustomerDebtSummary,
  getOverallDebtSummary,
  getContractDebtSummary,
  getPaymentHistoryDetail,
  exportAgingAnalysisCSV
};
