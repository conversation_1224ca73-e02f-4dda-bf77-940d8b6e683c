/**
 * Receivable Types
 * Định nghĩa types cho receivables (Công nợ phải thu)
 */

export interface Receivable {
  id: number;
  customer_id: number;
  contract_id: number;
  invoice_number: string;
  transaction_date: string;
  due_date: string;
  description: string;
  original_amount: number;
  total_paid: number;
  remaining_balance: number;
  balance_status: 'active' | 'overdue' | 'paid' | 'cancelled';
  days_overdue: number;
  currency: string;
  payment_terms: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  
  // Joined fields
  customer_name: string;
  customer_tax_code?: string;
  contract_number: string;
  contract_name: string;
  created_by_name?: string;
  
  // Aging buckets
  current_amount: number;
  days_1_30: number;
  days_31_60: number;
  days_61_90: number;
  over_90_days: number;
}

export interface CreateReceivableData {
  customer_id: number;
  contract_id: number;
  invoice_number: string;
  transaction_date: string;
  due_date: string;
  description: string;
  original_amount: number;
  currency?: string;
  payment_terms?: number;
  notes?: string;
}

export interface UpdateReceivableData extends CreateReceivableData {
  status?: 'active' | 'overdue' | 'paid' | 'cancelled';
}

export interface ReceivableFilters {
  page?: number;
  limit?: number;
  search?: string;
  customerId?: number;
  contractId?: number;
  status?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface ReceivablesResponse {
  receivables: Receivable[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface BulkCreateReceivablesData {
  contractId: number;
  startDate: string;
  endDate: string;
  invoicePrefix?: string;
}

export interface BulkCreateReceivablesResult {
  success: number;
  errors: number;
  createdReceivables: Receivable[];
  errorMessages: string[];
}
