const contractPriceModel = require('../models/contractPriceModel');
const {
  createResponse,
  createListResponse,
  processQueryParams,
  validateRequiredFields,
  sanitizeString
} = require('../utils/responseUtils');
const {
  asyncHandler,
  ValidationError,
  NotFoundError,
  ConflictError
} = require('../middleware/errorHandler');

/**
 * Lấy tất cả đơn giá
 * @route GET /api/v1/contract-prices
 * @access Private
 */
const getAllContractPrices = asyncHandler(async (req, res) => {
  const { pagination, sorting, filters } = processQueryParams(req);

  const options = {
    page: pagination.page,
    limit: pagination.limit,
    contract_id: filters.contract_id,
    product_id: filters.product_id,
    is_active: filters.is_active !== undefined ? filters.is_active === 'true' : true,
    sortBy: sorting.sortBy,
    sortOrder: sorting.sortOrder
  };

  const result = await contractPriceModel.getAllContractPrices(options);

  const response = createListResponse(
    result.prices,
    result.pagination,
    req
  );

  res.status(200).json(response);
});

/**
 * Lấy đơn giá theo ID
 * @route GET /api/v1/contract-prices/:id
 * @access Private
 */
const getContractPriceById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID đơn giá không hợp lệ', ['ID phải là một số nguyên']);
  }

  const price = await contractPriceModel.getContractPriceById(parseInt(id));

  if (!price) {
    throw new NotFoundError('Không tìm thấy đơn giá', [`Không tìm thấy đơn giá với ID ${id}`]);
  }

  res.status(200).json(createResponse(price));
});

/**
 * Tạo đơn giá mới
 * @route POST /api/v1/contract-prices
 * @access Private (Manager+)
 */
const createContractPrice = asyncHandler(async (req, res) => {
  const {
    contract_id,
    product_id,
    price,
    effective_date,
    expiry_date,
    notes
  } = req.body;

  // Validate required fields
  const requiredFields = ['contract_id', 'product_id', 'price', 'effective_date'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_id
  if (contract_id && isNaN(parseInt(contract_id))) {
    validationErrors.push({
      field: 'contract_id',
      message: 'ID hợp đồng phải là một số nguyên'
    });
  }

  // Validate product_id
  if (product_id && isNaN(parseInt(product_id))) {
    validationErrors.push({
      field: 'product_id',
      message: 'ID sản phẩm phải là một số nguyên'
    });
  }

  // Validate price
  if (price && (isNaN(parseFloat(price)) || parseFloat(price) <= 0)) {
    validationErrors.push({
      field: 'price',
      message: 'Đơn giá phải là số dương'
    });
  }

  // Validate dates
  if (effective_date && isNaN(Date.parse(effective_date))) {
    validationErrors.push({
      field: 'effective_date',
      message: 'Ngày hiệu lực không hợp lệ'
    });
  }

  if (expiry_date && isNaN(Date.parse(expiry_date))) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn không hợp lệ'
    });
  }

  if (effective_date && expiry_date && new Date(expiry_date) <= new Date(effective_date)) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn phải sau ngày hiệu lực'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra xung đột đơn giá
  const hasConflict = await contractPriceModel.checkPriceConflict(
    parseInt(contract_id),
    parseInt(product_id),
    effective_date,
    expiry_date
  );

  if (hasConflict) {
    throw new ConflictError(
      'Xung đột đơn giá',
      ['Đã có đơn giá hiệu lực trong khoảng thời gian này']
    );
  }

  // Sanitize input data
  const priceData = {
    contract_id: parseInt(contract_id),
    product_id: parseInt(product_id),
    price: parseFloat(price),
    effective_date: effective_date,
    expiry_date: expiry_date || null,
    notes: sanitizeString(notes)
  };

  const newPrice = await contractPriceModel.createContractPrice(priceData, req.user?.id || 1);

  res.status(201).json(createResponse(newPrice, 'Tạo đơn giá thành công'));
});

/**
 * Cập nhật đơn giá
 * @route PUT /api/v1/contract-prices/:id
 * @access Private (Manager+)
 */
const updateContractPrice = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const {
    price,
    effective_date,
    expiry_date,
    is_active,
    notes
  } = req.body;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID đơn giá không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Validate required fields
  const requiredFields = ['price', 'effective_date'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate price
  if (price && (isNaN(parseFloat(price)) || parseFloat(price) <= 0)) {
    validationErrors.push({
      field: 'price',
      message: 'Đơn giá phải là số dương'
    });
  }

  // Validate dates
  if (effective_date && isNaN(Date.parse(effective_date))) {
    validationErrors.push({
      field: 'effective_date',
      message: 'Ngày hiệu lực không hợp lệ'
    });
  }

  if (expiry_date && isNaN(Date.parse(expiry_date))) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn không hợp lệ'
    });
  }

  if (effective_date && expiry_date && new Date(expiry_date) <= new Date(effective_date)) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn phải sau ngày hiệu lực'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Kiểm tra đơn giá tồn tại
  const existingPrice = await contractPriceModel.getContractPriceById(parseInt(id));
  if (!existingPrice) {
    throw new NotFoundError('Không tìm thấy đơn giá', [`Không tìm thấy đơn giá với ID ${id}`]);
  }

  // Sanitize input data
  const priceData = {
    price: parseFloat(price),
    effective_date: effective_date,
    expiry_date: expiry_date || null,
    is_active: is_active !== undefined ? is_active : true,
    notes: sanitizeString(notes)
  };

  const updatedPrice = await contractPriceModel.updateContractPrice(parseInt(id), priceData);

  if (!updatedPrice) {
    throw new NotFoundError('Không thể cập nhật đơn giá', ['Đơn giá có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(updatedPrice, 'Cập nhật đơn giá thành công'));
});

/**
 * Xóa đơn giá
 * @route DELETE /api/v1/contract-prices/:id
 * @access Private (Admin only)
 */
const deleteContractPrice = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Validate ID
  if (!id || isNaN(parseInt(id))) {
    throw new ValidationError('ID đơn giá không hợp lệ', ['ID phải là một số nguyên']);
  }

  // Kiểm tra đơn giá tồn tại
  const existingPrice = await contractPriceModel.getContractPriceById(parseInt(id));
  if (!existingPrice) {
    throw new NotFoundError('Không tìm thấy đơn giá', [`Không tìm thấy đơn giá với ID ${id}`]);
  }

  const deleted = await contractPriceModel.deleteContractPrice(parseInt(id));

  if (!deleted) {
    throw new NotFoundError('Không thể xóa đơn giá', ['Đơn giá có thể đã bị xóa']);
  }

  res.status(200).json(createResponse(
    { message: 'Xóa đơn giá thành công', id: parseInt(id) }
  ));
});

/**
 * Lấy đơn giá hiện tại
 * @route GET /api/v1/contract-prices/current
 * @access Private
 */
const getCurrentPrice = asyncHandler(async (req, res) => {
  const { contract_id, product_id, date } = req.query;

  // Validate required parameters
  if (!contract_id || isNaN(parseInt(contract_id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là một số nguyên']);
  }

  if (!product_id || isNaN(parseInt(product_id))) {
    throw new ValidationError('ID sản phẩm không hợp lệ', ['product_id phải là một số nguyên']);
  }

  // Validate date if provided
  if (date && isNaN(Date.parse(date))) {
    throw new ValidationError('Ngày không hợp lệ', ['date phải có định dạng YYYY-MM-DD']);
  }

  const currentPrice = await contractPriceModel.getCurrentPrice(
    parseInt(contract_id),
    parseInt(product_id),
    date
  );

  if (!currentPrice) {
    throw new NotFoundError(
      'Không tìm thấy đơn giá hiện tại',
      ['Không có đơn giá hiệu lực cho sản phẩm này trong hợp đồng']
    );
  }

  res.status(200).json(createResponse(currentPrice));
});

/**
 * Lấy lịch sử giá
 * @route GET /api/v1/contract-prices/history
 * @access Private
 */
const getPriceHistory = asyncHandler(async (req, res) => {
  const { contract_id, product_id } = req.query;

  // Validate required parameters
  if (!contract_id || isNaN(parseInt(contract_id))) {
    throw new ValidationError('ID hợp đồng không hợp lệ', ['contract_id phải là một số nguyên']);
  }

  if (!product_id || isNaN(parseInt(product_id))) {
    throw new ValidationError('ID sản phẩm không hợp lệ', ['product_id phải là một số nguyên']);
  }

  const history = await contractPriceModel.getPriceHistory(
    parseInt(contract_id),
    parseInt(product_id)
  );

  res.status(200).json(createResponse(history));
});

/**
 * Thiết lập đơn giá với tự động vô hiệu hóa giá cũ
 * @route POST /api/v1/contract-prices/set-price
 * @access Private (Manager+)
 */
const setPrice = asyncHandler(async (req, res) => {
  const {
    contract_id,
    product_id,
    price,
    effective_date,
    expiry_date,
    notes,
    auto_deactivate_old = true
  } = req.body;

  // Validate required fields
  const requiredFields = ['contract_id', 'product_id', 'price', 'effective_date'];
  const validationErrors = validateRequiredFields(req.body, requiredFields);

  // Validate contract_id
  if (contract_id && isNaN(parseInt(contract_id))) {
    validationErrors.push({
      field: 'contract_id',
      message: 'ID hợp đồng phải là một số nguyên'
    });
  }

  // Validate product_id
  if (product_id && isNaN(parseInt(product_id))) {
    validationErrors.push({
      field: 'product_id',
      message: 'ID sản phẩm phải là một số nguyên'
    });
  }

  // Validate price
  if (price && (isNaN(parseFloat(price)) || parseFloat(price) <= 0)) {
    validationErrors.push({
      field: 'price',
      message: 'Đơn giá phải là số dương'
    });
  }

  // Validate dates
  if (effective_date && isNaN(Date.parse(effective_date))) {
    validationErrors.push({
      field: 'effective_date',
      message: 'Ngày hiệu lực không hợp lệ'
    });
  }

  if (expiry_date && isNaN(Date.parse(expiry_date))) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn không hợp lệ'
    });
  }

  if (effective_date && expiry_date && new Date(expiry_date) <= new Date(effective_date)) {
    validationErrors.push({
      field: 'expiry_date',
      message: 'Ngày hết hạn phải sau ngày hiệu lực'
    });
  }

  if (validationErrors.length > 0) {
    throw new ValidationError(
      'Dữ liệu không hợp lệ',
      validationErrors.map(err => `${err.field}: ${err.message}`)
    );
  }

  // Tự động vô hiệu hóa giá cũ nếu được yêu cầu
  if (auto_deactivate_old) {
    await contractPriceModel.deactivateOldPrices(
      parseInt(contract_id),
      parseInt(product_id),
      effective_date
    );
  }

  // Sanitize input data
  const priceData = {
    contract_id: parseInt(contract_id),
    product_id: parseInt(product_id),
    price: parseFloat(price),
    effective_date: effective_date,
    expiry_date: expiry_date || null,
    notes: sanitizeString(notes)
  };

  const newPrice = await contractPriceModel.createContractPrice(priceData, req.user?.id || 1);

  res.status(201).json(createResponse(newPrice, 'Thiết lập đơn giá thành công'));
});

module.exports = {
  getAllContractPrices,
  getContractPriceById,
  createContractPrice,
  updateContractPrice,
  deleteContractPrice,
  getCurrentPrice,
  getPriceHistory,
  setPrice
};
