const { Pool } = require('pg');
require('dotenv').config();

// Kiểm tra môi trường
const isProduction = process.env.NODE_ENV === 'production';

let dbConfig;

// Kiểm tra môi trường và cấu hình kết nối database
if (isProduction) {
  console.log('Running in production mode, using DATABASE_URL');

  // Sử dụng URL kết nối từ production environment
  if (process.env.DATABASE_URL) {
    console.log('Using DATABASE_URL from environment');
    dbConfig = {
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false // Cần thiết cho một số cloud providers
      },
      // Thêm các tùy chọn kết nối để cải thiện độ tin cậy
      connectionTimeoutMillis: 10000, // Tăng thời gian timeout kết nối: 10 giây
      idleTimeoutMillis: 30000, // Thời gian timeout khi không hoạt động: 30 giây
      max: 20, // Số lượng kết nối tối đa trong pool
      // Thêm cấu hình encoding để hỗ trợ tiếng Việt
      client_encoding: 'UTF8'
    };
  } else {
    console.error('DATABASE_URL not found in production environment');
    process.exit(1);
  }
} else {
  // Sử dụng cấu hình local
  dbConfig = {
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'tinhtam-hp',
    password: process.env.DB_PASSWORD || '110591',
    port: parseInt(process.env.DB_PORT || '5432'),
    // Thêm các tùy chọn kết nối để cải thiện độ tin cậy
    connectionTimeoutMillis: 5000, // Thời gian timeout kết nối: 5 giây
    idleTimeoutMillis: 30000, // Thời gian timeout khi không hoạt động: 30 giây
    max: 20, // Số lượng kết nối tối đa trong pool
    // Thêm cấu hình encoding để hỗ trợ tiếng Việt
    client_encoding: 'UTF8'
  };
}

// Tạo pool kết nối
const pool = new Pool(dbConfig);

// Xử lý lỗi kết nối
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

// Kiểm tra kết nối database
const testConnection = async () => {
  try {
    const client = await pool.connect();
    console.log('✅ Kết nối database thành công');
    
    // Kiểm tra database name
    const result = await client.query('SELECT current_database()');
    console.log(`📊 Database hiện tại: ${result.rows[0].current_database}`);
    
    client.release();
    return true;
  } catch (err) {
    console.error('❌ Lỗi kết nối database:', err.message);
    
    // Log chi tiết cấu hình (ẩn password)
    const safeConfig = { ...dbConfig };
    if (safeConfig.password) safeConfig.password = '***';
    if (safeConfig.connectionString) {
      safeConfig.connectionString = safeConfig.connectionString.replace(/:([^:@]+)@/, ':***@');
    }
    console.log('Database config:', safeConfig);
    
    return false;
  }
};

// Hàm thực hiện query với error handling
const query = async (text, params) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    
    // Log query trong development mode
    if (!isProduction) {
      console.log('Executed query', { text, duration, rows: res.rowCount });
    }
    
    return res;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

// Hàm lấy client từ pool
const getClient = async () => {
  try {
    return await pool.connect();
  } catch (error) {
    console.error('Error getting client from pool:', error);
    throw error;
  }
};

// Test kết nối khi khởi động
testConnection();

module.exports = {
  pool,
  query,
  getClient,
  testConnection
};
