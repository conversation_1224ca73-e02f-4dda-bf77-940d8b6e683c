/**
 * Contract Price Service
 * API service for contract price management
 */

import { get, post, put, del, patch } from './api';
import { ApiResponse } from './api';
import {
  ContractPrice,
  ContractPriceCreateRequest,
  ContractPriceUpdateRequest,
  ContractPriceFilterOptions,
  ContractPriceFormData,
  SetPriceRequest,
  CurrentPriceResponse,
  PriceHistoryItem,
  ContractPriceSummary,
  BulkPriceUpdateRequest,
} from '../types/contractPrice';

/**
 * Contract Price API Service
 */
export const contractPriceService = {
  /**
   * L<PERSON>y tất cả đơn giá với filter và pagination
   */
  getAll: async (options?: ContractPriceFilterOptions) => {
    const params = new URLSearchParams();

    if (options?.page) params.append('page', options.page.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.contract_id) params.append('contract_id', options.contract_id.toString());
    if (options?.product_id) params.append('product_id', options.product_id.toString());
    if (options?.is_active !== undefined) params.append('is_active', options.is_active.toString());
    if (options?.effective_date_from) params.append('effective_date_from', options.effective_date_from);
    if (options?.effective_date_to) params.append('effective_date_to', options.effective_date_to);
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const url = queryString ? `/contract-prices?${queryString}` : '/contract-prices';

    return get<ContractPrice[]>(url);
  },

  /**
   * Lấy đơn giá theo ID
   */
  getById: async (id: number) => {
    return get<ContractPrice>(`/contract-prices/${id}`);
  },

  /**
   * Tạo đơn giá mới
   */
  create: async (data: ContractPriceFormData) => {
    return post<ContractPrice>('/contract-prices', data);
  },

  /**
   * Cập nhật đơn giá
   */
  update: async (id: number, data: ContractPriceFormData) => {
    return put<ContractPrice>(`/contract-prices/${id}`, data);
  },

  /**
   * Xóa đơn giá
   */
  delete: async (id: number) => {
    return del<{ message: string; id: number }>(`/contract-prices/${id}`);
  },

  /**
   * Lấy đơn giá hiện tại cho sản phẩm trong hợp đồng
   */
  getCurrentPrice: async (contractId: number, productId: number, date?: string) => {
    const params = new URLSearchParams({
      contract_id: contractId.toString(),
      product_id: productId.toString(),
    });

    if (date) params.append('date', date);

    return get<CurrentPriceResponse>(`/contract-prices/current?${params.toString()}`);
  },

  /**
   * Lấy lịch sử giá của sản phẩm trong hợp đồng
   */
  getHistory: async (contractId: number, productId: number) => {
    const params = new URLSearchParams({
      contract_id: contractId.toString(),
      product_id: productId.toString(),
    });
    return get<PriceHistoryItem[]>(`/contract-prices/history?${params.toString()}`);
  },

  /**
   * Thiết lập đơn giá với tự động vô hiệu hóa giá cũ
   */
  setPrice: async (data: SetPriceRequest) => {
    return post<ContractPrice>('/contract-prices/set-price', data);
  },

  /**
   * Kiểm tra xung đột đơn giá
   */
  checkConflict: async (
    contractId: number,
    productId: number,
    effectiveDate: string,
    expiryDate?: string,
    excludeId?: number
  ) => {
    const params = new URLSearchParams({
      contract_id: contractId.toString(),
      product_id: productId.toString(),
      effective_date: effectiveDate,
    });

    if (expiryDate) params.append('expiry_date', expiryDate);
    if (excludeId) params.append('exclude_id', excludeId.toString());

    return get<{
      has_conflict: boolean;
      conflicting_prices?: ContractPrice[];
      message?: string;
    }>(`/contract-prices/check-conflict?${params.toString()}`);
  },

  /**
   * Export đơn giá ra Excel
   */
  exportToExcel: async (options?: ContractPriceFilterOptions): Promise<Blob> => {
    const params = new URLSearchParams();

    if (options?.contract_id) params.append('contract_id', options.contract_id.toString());
    if (options?.product_id) params.append('product_id', options.product_id.toString());
    if (options?.is_active !== undefined) params.append('is_active', options.is_active.toString());
    if (options?.effective_date_from) params.append('effective_date_from', options.effective_date_from);
    if (options?.effective_date_to) params.append('effective_date_to', options.effective_date_to);
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const queryString = params.toString();
    const url = queryString ? `/contract-prices/export?${queryString}` : '/contract-prices/export';

    const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000/api/v1'}${url}`);
    return response.blob();
  },
};

export default contractPriceService;
