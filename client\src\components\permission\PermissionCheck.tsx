import React, { ReactNode } from 'react';
// NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
// import { useAuth } from '../../contexts/AuthContext';

/**
 * Permission Interface
 */
interface Permission {
  resourceType: string;
  action: string;
}

/**
 * Permission Check Props
 */
interface PermissionCheckProps {
  children: ReactNode;
  resourceType: string;
  action: string;
  fallback?: ReactNode;
}

/**
 * Check if user has required permission
 */
const hasPermission = (user: any, resourceType: string, action: string): boolean => {
  if (!user) {
    return false;
  }

  // Admin has all permissions
  if (user.position && user.position.toLowerCase().includes('admin')) {
    return true;
  }

  // Basic permissions for regular users
  const basicPermissions = {
    customer: ['view', 'create', 'edit', 'delete'],
    user: ['view']
  };

  if (basicPermissions[resourceType as keyof typeof basicPermissions]) {
    return basicPermissions[resourceType as keyof typeof basicPermissions].includes(action);
  }

  // Default: no permission
  return false;
};

/**
 * Permission Check Component
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 * Hiện tại component này luôn render children mà không kiểm tra authentication
 */
const PermissionCheck: React.FC<PermissionCheckProps> = ({
  children,
  resourceType,
  action,
  fallback = null
}) => {
  // NOTE: Tạm thời bỏ qua authentication check
  // const { state } = useAuth();

  // Mock user for permission check (nếu cần)
  const mockUser = {
    position: 'admin',
    name: 'Admin'
  };

  // Check if user has required permission (vẫn giữ logic permission check)
  if (!hasPermission(mockUser, resourceType, action)) {
    return <>{fallback}</>;
  }

  // User has permission, render children
  return <>{children}</>;
};

export default PermissionCheck;
