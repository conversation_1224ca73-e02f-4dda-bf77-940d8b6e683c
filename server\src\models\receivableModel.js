const { pool } = require('../db');

/**
 * Receivable Model
 * Xử lý các thao tác CRUD cho bảng receivables (Công nợ phải thu)
 */

/**
 * L<PERSON><PERSON> tất cả receivables với phân trang và tìm kiếm
 * @param {Object} options - T<PERSON>y chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {number} options.customerId - Lọc theo khách hàng
 * @param {number} options.contractId - Lọc theo hợp đồng
 * @param {string} options.status - Lọc theo trạng thái
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllReceivables = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    customerId,
    contractId,
    status,
    sortBy = 'transaction_date',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'invoice_number', 'transaction_date', 'due_date', 'original_amount', 'remaining_balance', 'status'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'transaction_date';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE rb.balance_status != \'cancelled\'';
    const queryParams = [];
    let paramIndex = 1;

    if (search && search.trim()) {
      whereClause += ` AND (
        rb.invoice_number ILIKE $${paramIndex} OR 
        rb.description ILIKE $${paramIndex} OR
        c.name ILIKE $${paramIndex} OR
        con.contract_number ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search.trim()}%`);
      paramIndex++;
    }

    if (customerId) {
      whereClause += ` AND rb.customer_id = $${paramIndex}`;
      queryParams.push(customerId);
      paramIndex++;
    }

    if (contractId) {
      whereClause += ` AND rb.contract_id = $${paramIndex}`;
      queryParams.push(contractId);
      paramIndex++;
    }

    if (status) {
      whereClause += ` AND rb.balance_status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM receivables_balance_fifo rb
      JOIN customers c ON rb.customer_id = c.id
      JOIN contracts con ON rb.contract_id = con.id
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT 
        rb.id,
        rb.customer_id,
        rb.contract_id,
        rb.invoice_number,
        rb.transaction_date,
        rb.due_date,
        rb.description,
        rb.original_amount,
        rb.total_paid,
        rb.remaining_balance,
        rb.balance_status,
        rb.days_overdue,
        rb.currency,
        rb.payment_terms,
        rb.notes,
        rb.created_at,
        rb.updated_at,
        c.name as customer_name,
        c.tax_code as customer_tax_code,
        con.contract_number,
        con.contract_name,
        u.name as created_by_name
      FROM receivables_balance_fifo rb
      JOIN customers c ON rb.customer_id = c.id
      JOIN contracts con ON rb.contract_id = con.id
      LEFT JOIN users u ON rb.created_by = u.id
      ${whereClause}
      ORDER BY rb.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      receivables: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllReceivables:', error);
    throw error;
  }
};

/**
 * Lấy receivable theo ID
 * @param {number} id - ID của receivable
 * @returns {Object|null} Thông tin receivable hoặc null nếu không tìm thấy
 */
const getReceivableById = async (id) => {
  try {
    const query = `
      SELECT 
        rb.*,
        c.name as customer_name,
        c.tax_code as customer_tax_code,
        con.contract_number,
        con.contract_name,
        u.name as created_by_name
      FROM receivables_balance_fifo rb
      JOIN customers c ON rb.customer_id = c.id
      JOIN contracts con ON rb.contract_id = con.id
      LEFT JOIN users u ON rb.created_by = u.id
      WHERE rb.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getReceivableById:', error);
    throw error;
  }
};

/**
 * Tạo receivable mới
 * @param {Object} receivableData - Dữ liệu receivable
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin receivable vừa tạo
 */
const createReceivable = async (receivableData, createdBy) => {
  const {
    customer_id,
    contract_id,
    invoice_number,
    transaction_date,
    due_date,
    description,
    original_amount,
    currency = 'VND',
    payment_terms = 30,
    notes
  } = receivableData;

  try {
    const query = `
      INSERT INTO receivables (
        customer_id, contract_id, invoice_number, transaction_date, due_date,
        description, original_amount, currency, payment_terms, notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      RETURNING *
    `;

    const values = [
      customer_id,
      contract_id,
      invoice_number,
      transaction_date,
      due_date,
      description,
      original_amount,
      currency,
      payment_terms,
      notes || null,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createReceivable:', error);
    throw error;
  }
};

/**
 * Cập nhật thông tin receivable
 * @param {number} id - ID của receivable
 * @param {Object} receivableData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin receivable sau khi cập nhật
 */
const updateReceivable = async (id, receivableData) => {
  const {
    customer_id,
    contract_id,
    invoice_number,
    transaction_date,
    due_date,
    description,
    original_amount,
    currency,
    payment_terms,
    notes,
    status
  } = receivableData;

  try {
    const query = `
      UPDATE receivables 
      SET 
        customer_id = $1,
        contract_id = $2,
        invoice_number = $3,
        transaction_date = $4,
        due_date = $5,
        description = $6,
        original_amount = $7,
        currency = $8,
        payment_terms = $9,
        notes = $10,
        status = $11,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $12
      RETURNING *
    `;

    const values = [
      customer_id,
      contract_id,
      invoice_number,
      transaction_date,
      due_date,
      description,
      original_amount,
      currency,
      payment_terms,
      notes,
      status,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateReceivable:', error);
    throw error;
  }
};

/**
 * Xóa receivable (soft delete)
 * @param {number} id - ID của receivable
 * @returns {boolean} True nếu xóa thành công
 */
const deleteReceivable = async (id) => {
  try {
    const query = `
      UPDATE receivables 
      SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteReceivable:', error);
    throw error;
  }
};

/**
 * Kiểm tra invoice number đã tồn tại
 * @param {string} invoiceNumber - Số hóa đơn
 * @param {number} excludeId - ID receivable cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu invoice number đã tồn tại
 */
const checkInvoiceNumberExists = async (invoiceNumber, excludeId = null) => {
  try {
    let query = 'SELECT id FROM receivables WHERE invoice_number = $1 AND status != \'cancelled\'';
    const params = [invoiceNumber];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkInvoiceNumberExists:', error);
    throw error;
  }
};

module.exports = {
  getAllReceivables,
  getReceivableById,
  createReceivable,
  updateReceivable,
  deleteReceivable,
  checkInvoiceNumberExists
};
