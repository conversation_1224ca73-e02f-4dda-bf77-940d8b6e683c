const express = require('express');
const router = express.Router();
const dashboardController = require('../controllers/dashboardController');

/**
 * Dashboard Routes
 * Định nghĩa các API endpoints cho dashboard
 */

/**
 * @route   GET /api/dashboard/stats
 * @desc    Lấy thống kê tổng quan hệ thống
 * @access  Private
 * @returns {Object} Thống kê tổng quan (kh<PERSON>ch hàng, sản phẩm, hợp đồng, sản lượng hôm nay)
 */
router.get('/stats', 
  dashboardController.validateCommonParams,
  dashboardController.getGeneralStats
);

/**
 * @route   GET /api/dashboard/production-today
 * @desc    Lấy sản lượng hôm nay chi tiết
 * @access  Private
 * @returns {Object} Thông tin sản lượng hôm nay (số lượ<PERSON>, gi<PERSON> tr<PERSON>, loại sản phẩm)
 */
router.get('/production-today', 
  dashboardController.validate<PERSON>ommonPara<PERSON>,
  dashboardController.getTodayProduction
);

/**
 * @route   GET /api/dashboard/top-products
 * @desc    Lấy top 5 sản phẩm có sản lượng cao nhất
 * @access  Private
 * @query   {string} period - Khoảng thời gian (today, week, month) - default: today
 * @returns {Object} Danh sách top 5 sản phẩm với thông tin sản lượng và giá trị
 */
router.get('/top-products', 
  dashboardController.validateCommonParams,
  dashboardController.getTopProducts
);

/**
 * @route   GET /api/dashboard/debt-summary
 * @desc    Lấy tóm tắt tình hình công nợ
 * @access  Private
 * @returns {Object} Thông tin tổng hợp công nợ (tổng phải thu, quá hạn, số khách hàng)
 */
router.get('/debt-summary', 
  dashboardController.validateCommonParams,
  dashboardController.getDebtSummary
);

/**
 * @route   GET /api/dashboard/top-overdue-customers
 * @desc    Lấy top 5 khách hàng có công nợ quá hạn cao nhất
 * @access  Private
 * @returns {Array} Danh sách top 5 khách hàng nợ quá hạn với thông tin chi tiết
 */
router.get('/top-overdue-customers', 
  dashboardController.validateCommonParams,
  dashboardController.getTopOverdueCustomers
);

/**
 * @route   GET /api/dashboard/monthly-production
 * @desc    Lấy thống kê sản lượng theo tháng
 * @access  Private
 * @query   {number} month - Tháng (1-12) - default: tháng hiện tại
 * @query   {number} year - Năm (YYYY) - default: năm hiện tại
 * @returns {Object} Thống kê sản lượng tháng với so sánh tháng trước và breakdown theo ngày
 */
router.get('/monthly-production',
  dashboardController.validateCommonParams,
  dashboardController.getMonthlyProductionStats
);

/**
 * @route   GET /api/dashboard/all
 * @desc    Lấy tất cả dữ liệu dashboard trong một lần gọi
 * @access  Private
 * @returns {Object} Tất cả dữ liệu dashboard (stats, production, products, debt, monthly)
 */
router.get('/all',
  dashboardController.validateCommonParams,
  dashboardController.getAllDashboardData
);

module.exports = router;
