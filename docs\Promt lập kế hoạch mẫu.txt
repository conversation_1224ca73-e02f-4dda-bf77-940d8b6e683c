Tạo một file markdown (.md) có tên "QUICK_UPDATE_IMPLEMENTATION_PLAN.md" chứa kế hoạch chi tiết để tích hợp tính năng cập nhật nhanh giá và sản lượng theo từng hợp đồng vào dự án hiện tại.

File markdown cần bao gồm:

1. **Cấu trúc và format**:
   - Sử dụng markdown syntax với headers, checkboxes, và code blocks
   - Tổ chức theo phases/giai đoạn rõ ràng
   - Mỗi task có checkbox [ ] để đánh dấu hoàn thành [x]

2. **Nội dung chi tiết**:
   - Tổng quan tình hình hiện tại (backend APIs đã có, frontend issues cần fix)
   - <PERSON>ụ<PERSON> tiêu cụ thể của từng tính năng (Quick Price Update, Quick Production Input)
   - Danh sách công việc đượ<PERSON> chia thành các phases theo thứ tự ưu tiên
   - Mỗi task ghi rõ: file cần tạo/sửa, mục tiêu, acceptance criteria

3. **Workflow thực hiện**:
   - Sau khi hoàn thành một task, đánh dấu [x] vào checkbox
   - Chuyển sang task tiếp theo theo thứ tự trong plan
   - Cập nhật progress và notes nếu cần

4. **Tham khảo từ thư mục Mau**:
   - Sử dụng các file mẫu trong thư mục "Mau" làm reference
   - Adapt code từ QuickPriceForm.tsx và QuickProductionForm.tsx
   - Tích hợp vào cấu trúc dự án hiện tại

File này sẽ serve như một roadmap thực tế để implement tính năng quick update một cách có hệ thống và trackable.