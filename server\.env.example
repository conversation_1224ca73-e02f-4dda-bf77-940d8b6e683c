# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tinhtam-hp
DB_USER=postgres
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=5000
NODE_ENV=development

# Client URL (for CORS)
CLIENT_URL=http://localhost:5173

# Production Database URL (for deployment)
# DATABASE_URL=postgresql://username:password@hostname:port/database_name
