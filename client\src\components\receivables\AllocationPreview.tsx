import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import { 
  TrendingUp as FifoIcon,
  Schedule as ClockIcon,
  AccountBalance as BalanceIcon
} from '@mui/icons-material';
import { paymentService } from '../../services/paymentService';
import { AllocationPreviewResult, AllocationPreviewData } from '../../types/payment';

interface AllocationPreviewProps {
  previewData: AllocationPreviewData | null;
  onError: (error: string) => void;
}

const AllocationPreview: React.FC<AllocationPreviewProps> = ({
  previewData,
  onError
}) => {
  const [preview, setPreview] = useState<AllocationPreviewResult | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (previewData && previewData.customerId && previewData.amount > 0) {
      loadPreview();
    } else {
      setPreview(null);
    }
  }, [previewData]);

  const loadPreview = async () => {
    if (!previewData) return;

    try {
      setLoading(true);
      const result = await paymentService.getPaymentAllocationPreview(previewData);
      setPreview(result);
    } catch (error: any) {
      onError(error.message || 'Không thể tải preview phân bổ');
      setPreview(null);
    } finally {
      setLoading(false);
    }
  };

  if (!previewData || !previewData.customerId || previewData.amount <= 0) {
    return (
      <Alert severity="info" sx={{ fontSize: '0.85rem' }}>
        <Typography variant="body2">
          Nhập đầy đủ thông tin khách hàng và số tiền để xem preview phân bổ FIFO
        </Typography>
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={24} />
        <Typography variant="body2" sx={{ ml: 1 }}>
          Đang tính toán phân bổ FIFO...
        </Typography>
      </Box>
    );
  }

  if (!preview) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <FifoIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
          Preview Phân bổ FIFO
        </Typography>
        <Chip 
          label="Tự động" 
          size="small" 
          color="primary" 
          sx={{ ml: 1, fontSize: '0.75rem' }} 
        />
      </Box>

      {/* Summary */}
      <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Tổng thanh toán:
          </Typography>
          <Typography variant="body2" sx={{ fontWeight: 600 }}>
            {formatCurrency(previewData.amount)}
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Sẽ phân bổ:
          </Typography>
          <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
            {formatCurrency(preview.total_allocated)}
          </Typography>
        </Box>
        {preview.unallocated_amount > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Typography variant="body2" color="text.secondary">
              Dư thừa:
            </Typography>
            <Typography variant="body2" sx={{ fontWeight: 600, color: 'warning.main' }}>
              {formatCurrency(preview.unallocated_amount)}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Allocation Table */}
      {preview.allocations.length > 0 ? (
        <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
          <Table size="small" stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
                  Hóa đơn
                </TableCell>
                <TableCell sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
                  Ngày GD
                </TableCell>
                <TableCell align="right" sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
                  Còn nợ
                </TableCell>
                <TableCell align="right" sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
                  Phân bổ
                </TableCell>
                <TableCell align="right" sx={{ fontSize: '0.8rem', fontWeight: 600 }}>
                  Còn lại
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {preview.allocations.map((allocation, index) => (
                <TableRow key={allocation.receivable_id}>
                  <TableCell sx={{ fontSize: '0.8rem' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {allocation.invoice_number}
                      </Typography>
                      {index === 0 && (
                        <Chip 
                          label="Cũ nhất" 
                          size="small" 
                          color="primary" 
                          variant="outlined"
                          sx={{ ml: 1, fontSize: '0.7rem' }} 
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontSize: '0.8rem' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ClockIcon sx={{ fontSize: '0.9rem', mr: 0.5, color: 'text.secondary' }} />
                      {formatDate(allocation.transaction_date)}
                    </Box>
                  </TableCell>
                  <TableCell align="right" sx={{ fontSize: '0.8rem' }}>
                    {formatCurrency(allocation.remaining_balance)}
                  </TableCell>
                  <TableCell align="right" sx={{ fontSize: '0.8rem', fontWeight: 600, color: 'success.main' }}>
                    {formatCurrency(allocation.allocated_amount)}
                  </TableCell>
                  <TableCell align="right" sx={{ fontSize: '0.8rem' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      <BalanceIcon sx={{ fontSize: '0.9rem', mr: 0.5, color: 'text.secondary' }} />
                      {formatCurrency(allocation.remaining_after_allocation)}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      ) : (
        <Alert severity="warning" sx={{ fontSize: '0.85rem' }}>
          <Typography variant="body2">
            Không có công nợ phù hợp để phân bổ cho khách hàng này
          </Typography>
        </Alert>
      )}

      {/* FIFO Explanation */}
      <Box sx={{ mt: 2, p: 1.5, bgcolor: 'info.50', borderRadius: 1, border: '1px solid', borderColor: 'info.200' }}>
        <Typography variant="caption" color="info.main" sx={{ fontWeight: 600 }}>
          💡 Nguyên tắc FIFO: Thanh toán sẽ được phân bổ vào các công nợ theo thứ tự thời gian (cũ nhất trước)
        </Typography>
      </Box>
    </Box>
  );
};

export default AllocationPreview;
