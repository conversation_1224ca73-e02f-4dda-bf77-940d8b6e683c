const express = require('express');
const router = express.Router();
const reportController = require('../controllers/reportController');

/**
 * Report Routes
 * Định nghĩa các route cho báo cáo công nợ và aging analysis
 */

/**
 * @route   GET /api/reports/aging-analysis
 * @desc    Lấy báo cáo aging analysis
 * @access  Private
 * @query   {number} customerId - Lọc theo khách hàng
 * @query   {string} asOfDate - Ngày tính aging (default: hôm nay)
 * @query   {boolean} includeZeroBalance - Bao gồm khách hàng có balance = 0
 */
router.get('/aging-analysis', 
  reportController.validateDateParams,
  reportController.getAgingAnalysis
);

/**
 * @route   GET /api/reports/aging-analysis/export
 * @desc    Export báo cáo aging analysis ra CSV
 * @access  Private
 * @query   {number} customerId - Lọc theo khách hàng
 * @query   {string} asOfDate - Ngày tính aging (default: hôm nay)
 * @query   {boolean} includeZeroBalance - <PERSON><PERSON> gồm khách hàng có balance = 0
 */
router.get('/aging-analysis/export', 
  reportController.validateDateParams,
  reportController.exportAgingAnalysisCSV
);

/**
 * @route   GET /api/reports/customer-debt/:customerId
 * @desc    Lấy tổng hợp công nợ theo khách hàng
 * @access  Private
 * @param   {number} customerId - ID khách hàng
 */
router.get('/customer-debt/:customerId', 
  reportController.validateCustomerIdParam,
  reportController.getCustomerDebtSummary
);

/**
 * @route   GET /api/reports/debt-summary
 * @desc    Lấy tổng hợp công nợ tổng thể
 * @access  Private
 */
router.get('/debt-summary', reportController.getOverallDebtSummary);

/**
 * @route   GET /api/reports/contract-debt
 * @desc    Lấy báo cáo công nợ theo hợp đồng
 * @access  Private
 * @query   {number} contractId - Lọc theo hợp đồng
 * @query   {number} customerId - Lọc theo khách hàng
 */
router.get('/contract-debt', reportController.getContractDebtSummary);

/**
 * @route   GET /api/reports/payment-history
 * @desc    Lấy lịch sử thanh toán chi tiết
 * @access  Private
 * @query   {number} customerId - Lọc theo khách hàng
 * @query   {number} paymentId - Lọc theo payment
 * @query   {string} startDate - Ngày bắt đầu
 * @query   {string} endDate - Ngày kết thúc
 * @query   {number} limit - Giới hạn số bản ghi (default: 100, max: 1000)
 */
router.get('/payment-history', 
  reportController.validateDateParams,
  reportController.validateLimitParam,
  reportController.getPaymentHistoryDetail
);

/**
 * @route   GET /api/reports/dashboard
 * @desc    Lấy dữ liệu dashboard cho debt management
 * @access  Private
 */
router.get('/dashboard', reportController.getDashboardData);

module.exports = router;
