const reportService = require('../services/reportService');
const { successResponse, errorResponse } = require('../utils/responseUtils');

/**
 * Report Controller
 * X<PERSON> lý các request liên quan đến báo cáo công nợ và aging analysis
 */

/**
 * <PERSON><PERSON>y báo cáo aging analysis
 * GET /api/reports/aging-analysis
 */
const getAgingAnalysis = async (req, res) => {
  try {
    const {
      customerId,
      asOfDate,
      includeZeroBalance = false
    } = req.query;

    const options = {
      customerId: customerId ? parseInt(customerId) : undefined,
      asOfDate,
      includeZeroBalance: includeZeroBalance === 'true'
    };

    const result = await reportService.getAgingAnalysis(options);
    
    return successResponse(res, result, 'Lấy báo cáo aging analysis thành công');
  } catch (error) {
    console.error('Error in reportController.getAgingAnalysis:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * <PERSON><PERSON><PERSON> tổng hợp công nợ theo khách hàng
 * GET /api/reports/customer-debt/:customerId
 */
const getCustomerDebtSummary = async (req, res) => {
  try {
    const { customerId } = req.params;
    const result = await reportService.getCustomerDebtSummary(parseInt(customerId));
    
    return successResponse(res, result, 'Lấy tổng hợp công nợ khách hàng thành công');
  } catch (error) {
    console.error('Error in reportController.getCustomerDebtSummary:', error);
    const statusCode = error.message.includes('không tìm thấy') ? 404 : 500;
    return errorResponse(res, error.message, statusCode);
  }
};

/**
 * Lấy tổng hợp công nợ tổng thể
 * GET /api/reports/debt-summary
 */
const getOverallDebtSummary = async (req, res) => {
  try {
    const result = await reportService.getOverallDebtSummary();
    
    return successResponse(res, result, 'Lấy tổng hợp công nợ tổng thể thành công');
  } catch (error) {
    console.error('Error in reportController.getOverallDebtSummary:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy báo cáo công nợ theo hợp đồng
 * GET /api/reports/contract-debt
 */
const getContractDebtSummary = async (req, res) => {
  try {
    const {
      contractId,
      customerId
    } = req.query;

    const options = {
      contractId: contractId ? parseInt(contractId) : undefined,
      customerId: customerId ? parseInt(customerId) : undefined
    };

    const result = await reportService.getContractDebtSummary(options);
    
    return successResponse(res, result, 'Lấy báo cáo công nợ theo hợp đồng thành công');
  } catch (error) {
    console.error('Error in reportController.getContractDebtSummary:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy lịch sử thanh toán chi tiết
 * GET /api/reports/payment-history
 */
const getPaymentHistoryDetail = async (req, res) => {
  try {
    const {
      customerId,
      paymentId,
      startDate,
      endDate,
      limit = 100
    } = req.query;

    const options = {
      customerId: customerId ? parseInt(customerId) : undefined,
      paymentId: paymentId ? parseInt(paymentId) : undefined,
      startDate,
      endDate,
      limit: parseInt(limit)
    };

    const result = await reportService.getPaymentHistoryDetail(options);
    
    return successResponse(res, result, 'Lấy lịch sử thanh toán chi tiết thành công');
  } catch (error) {
    console.error('Error in reportController.getPaymentHistoryDetail:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Export báo cáo aging analysis ra CSV
 * GET /api/reports/aging-analysis/export
 */
const exportAgingAnalysisCSV = async (req, res) => {
  try {
    const {
      customerId,
      asOfDate,
      includeZeroBalance = false
    } = req.query;

    const options = {
      customerId: customerId ? parseInt(customerId) : undefined,
      asOfDate,
      includeZeroBalance: includeZeroBalance === 'true'
    };

    const csvContent = await reportService.exportAgingAnalysisCSV(options);
    
    // Set headers for CSV download
    const filename = `aging-analysis-${new Date().toISOString().split('T')[0]}.csv`;
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));
    
    // Add BOM for proper UTF-8 encoding in Excel
    res.write('\ufeff');
    res.end(csvContent);
  } catch (error) {
    console.error('Error in reportController.exportAgingAnalysisCSV:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy dashboard data cho debt management
 * GET /api/reports/dashboard
 */
const getDashboardData = async (req, res) => {
  try {
    // Lấy tổng hợp tổng thể
    const overallSummary = await reportService.getOverallDebtSummary();
    
    // Lấy top 10 khách hàng có nợ nhiều nhất
    const topDebtors = await reportService.getAgingAnalysis({ 
      includeZeroBalance: false 
    });
    const top10Debtors = topDebtors.slice(0, 10);
    
    // Lấy aging analysis summary cho chart
    const agingData = await reportService.getAgingAnalysis({ 
      includeZeroBalance: false 
    });
    
    // Tính tổng cho từng bucket để vẽ chart
    const agingChart = agingData.reduce((acc, curr) => {
      acc.current += parseFloat(curr.current_amount) || 0;
      acc.days_1_30 += parseFloat(curr.days_1_30) || 0;
      acc.days_31_60 += parseFloat(curr.days_31_60) || 0;
      acc.days_61_90 += parseFloat(curr.days_61_90) || 0;
      acc.over_90_days += parseFloat(curr.over_90_days) || 0;
      return acc;
    }, {
      current: 0,
      days_1_30: 0,
      days_31_60: 0,
      days_61_90: 0,
      over_90_days: 0
    });

    const dashboardData = {
      overall_summary: overallSummary,
      top_debtors: top10Debtors,
      aging_chart: agingChart,
      summary_stats: {
        total_customers_with_debt: overallSummary.customers_with_debt,
        total_customers_with_overdue: overallSummary.customers_with_overdue,
        avg_overdue_percentage: parseFloat(overallSummary.avg_overdue_percentage || 0).toFixed(2),
        max_days_overdue: overallSummary.max_days_overdue_system
      }
    };
    
    return successResponse(res, dashboardData, 'Lấy dữ liệu dashboard thành công');
  } catch (error) {
    console.error('Error in reportController.getDashboardData:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Validate customer ID parameter middleware
 */
const validateCustomerIdParam = (req, res, next) => {
  const { customerId } = req.params;
  
  if (!customerId || isNaN(customerId) || parseInt(customerId) <= 0) {
    return errorResponse(res, 'Customer ID không hợp lệ', 400);
  }
  
  next();
};

/**
 * Validate date parameters middleware
 */
const validateDateParams = (req, res, next) => {
  const { startDate, endDate, asOfDate } = req.query;
  const errors = [];

  if (startDate && isNaN(new Date(startDate).getTime())) {
    errors.push('startDate không hợp lệ');
  }

  if (endDate && isNaN(new Date(endDate).getTime())) {
    errors.push('endDate không hợp lệ');
  }

  if (asOfDate && isNaN(new Date(asOfDate).getTime())) {
    errors.push('asOfDate không hợp lệ');
  }

  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    if (end < start) {
      errors.push('endDate không thể trước startDate');
    }
  }

  if (errors.length > 0) {
    return errorResponse(res, `Dữ liệu không hợp lệ: ${errors.join(', ')}`, 400);
  }

  next();
};

/**
 * Validate limit parameter middleware
 */
const validateLimitParam = (req, res, next) => {
  const { limit } = req.query;
  
  if (limit && (isNaN(limit) || parseInt(limit) <= 0 || parseInt(limit) > 1000)) {
    return errorResponse(res, 'Limit phải là số từ 1 đến 1000', 400);
  }
  
  next();
};

module.exports = {
  getAgingAnalysis,
  getCustomerDebtSummary,
  getOverallDebtSummary,
  getContractDebtSummary,
  getPaymentHistoryDetail,
  exportAgingAnalysisCSV,
  getDashboardData,
  validateCustomerIdParam,
  validateDateParams,
  validateLimitParam
};
