/**
 * Production Detail Dialog Component
 * <PERSON><PERSON><PERSON> thị chi tiết sản lượng theo ngày và hợp đồng
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  CalendarToday as CalendarIcon,
  Business as BusinessIcon,
  Assignment as ContractIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { GroupedProduction, DailyProduction } from '../../types/production';
import { formatCurrencyVN, formatDateVN } from '../../utils/vietnameseFormatters';
import { dailyProductionService } from '../../services/dailyProductionService';

interface ProductionDetailDialogProps {
  open: boolean;
  onClose: () => void;
  production: GroupedProduction | null;
}

const ProductionDetailDialog: React.FC<ProductionDetailDialogProps> = ({
  open,
  onClose,
  production
}) => {
  const [details, setDetails] = useState<DailyProduction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [summary, setSummary] = useState<{ total_quantity: number; total_amount: number } | null>(null);

  // Load chi tiết sản lượng
  useEffect(() => {
    if (open && production) {
      loadProductionDetails();
    }
  }, [open, production]);

  const loadProductionDetails = async () => {
    if (!production) return;

    try {
      setLoading(true);
      setError(null);

      // Format ngày đúng định dạng YYYY-MM-DD
      let formattedDate;
      if (production.production_date.includes('T')) {
        // Nếu là ISO string, lấy phần date
        formattedDate = production.production_date.split('T')[0];
      } else {
        // Nếu đã là date string, sử dụng trực tiếp
        formattedDate = production.production_date;
      }

      console.log('🔍 ProductionDetailDialog - Loading details:', {
        originalDate: production.production_date,
        formattedDate,
        contractId: production.contract_id
      });

      const response = await dailyProductionService.getAll({
        production_date: formattedDate,
        contract_id: production.contract_id,
        limit: 100 // Lấy tất cả chi tiết
      });

      console.log('📊 ProductionDetailDialog - API Response:', {
        success: response.success,
        dataLength: response.data?.length || 0,
        summary: response.summary,
        firstItem: response.data?.[0]
      });

      if (response.success) {
        setDetails(response.data || []);
        // Sử dụng summary từ API thay vì tính toán lại
        if (response.summary) {
          setSummary({
            total_quantity: parseFloat(response.summary.total_quantity || '0'),
            total_amount: parseFloat(response.summary.total_amount || '0')
          });
        }
      } else {
        throw new Error(response.message || 'Không thể tải chi tiết sản lượng');
      }
    } catch (err) {
      console.error('Error loading production details:', err);
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotals = () => {
    return details.reduce((acc, item) => ({
      quantity: acc.quantity + parseFloat(item.quantity || '0'),
      amount: acc.amount + parseFloat(item.total_amount || '0')
    }), { quantity: 0, amount: 0 });
  };

  // Sử dụng summary từ API nếu có, nếu không thì tính toán từ details
  const totals = summary || calculateTotals();

  if (!production) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="div" fontWeight={600}>
            Chi tiết sản lượng
          </Typography>
          <Button
            onClick={onClose}
            size="small"
            sx={{ minWidth: 'auto', p: 1 }}
          >
            <CloseIcon fontSize="small" />
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {/* Thông tin tổng quan */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <CalendarIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="subtitle2" fontWeight={600}>
                    Ngày sản xuất
                  </Typography>
                </Box>
                <Typography variant="body1" fontWeight={500}>
                  {formatDateVN(production.production_date)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <BusinessIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="subtitle2" fontWeight={600}>
                    Khách hàng
                  </Typography>
                </Box>
                <Typography variant="body1" fontWeight={500}>
                  {production.customer_name}
                </Typography>
                {production.customer_short_name && (
                  <Typography variant="body2" color="text.secondary">
                    ({production.customer_short_name})
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ContractIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="subtitle2" fontWeight={600}>
                    Hợp đồng
                  </Typography>
                </Box>
                <Typography variant="body1" fontWeight={500}>
                  {production.contract_number}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {production.contract_name}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <MoneyIcon fontSize="small" sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="subtitle2" fontWeight={600}>
                    Tổng tiền
                  </Typography>
                </Box>
                <Typography variant="h6" color="primary.main" fontWeight={600}>
                  {formatCurrencyVN(production.total_amount)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {production.product_count} sản phẩm
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        {/* Chi tiết sản lượng */}
        <Typography variant="h6" fontWeight={600} sx={{ mb: 2 }}>
          Chi tiết theo sản phẩm
        </Typography>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <TableContainer component={Paper} variant="outlined">
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 600 }}>Sản phẩm</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600 }}>Số lượng</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600 }}>Đơn giá</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600 }}>Thành tiền</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600 }}>Ghi chú</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {details.map((item, index) => (
                  <TableRow key={index} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {item.product_code} - {item.product_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Đơn vị: {item.product_unit_type}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Chip 
                        label={item.quantity.toLocaleString('vi-VN')} 
                        size="small" 
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight={500}>
                        {formatCurrencyVN(item.unit_price)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight={600} color="primary.main">
                        {formatCurrencyVN(item.total_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2" color="text.secondary">
                        {item.notes || '-'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
                {details.length > 0 && (
                  <TableRow sx={{ backgroundColor: 'grey.50' }}>
                    <TableCell sx={{ fontWeight: 600 }}>
                      Tổng cộng
                    </TableCell>
                    <TableCell align="center" sx={{ fontWeight: 600 }}>
                      {totals.total_quantity?.toLocaleString('vi-VN') || totals.quantity?.toLocaleString('vi-VN')}
                    </TableCell>
                    <TableCell></TableCell>
                    <TableCell align="right" sx={{ fontWeight: 600 }}>
                      <Typography variant="body1" fontWeight={600} color="primary.main">
                        {formatCurrencyVN(totals.total_amount || totals.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {!loading && !error && details.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body2" color="text.secondary">
              Không có chi tiết sản lượng
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductionDetailDialog;
