#!/usr/bin/env node

/**
 * Database Schema Documentation Generator
 * Automatically generates and updates .rules/database-schema.md
 * 
 * Usage: node scripts/update-schema-docs.js
 * Or: npm run update-schema-docs
 */

const fs = require('fs');
const path = require('path');

// Use the existing database connection from server
const dbPath = path.join(__dirname, '../server/src/db.js');
const { pool } = require(dbPath);

// Use existing pool from server configuration

/**
 * Get all tables and their columns
 */
async function getTableSchema() {
  const query = `
    SELECT 
      t.table_name,
      c.column_name,
      c.data_type,
      c.character_maximum_length,
      c.numeric_precision,
      c.numeric_scale,
      c.is_nullable,
      c.column_default,
      tc.constraint_type,
      kcu.constraint_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name
    FROM information_schema.tables t
    LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
    LEFT JOIN information_schema.key_column_usage kcu ON c.table_name = kcu.table_name AND c.column_name = kcu.column_name
    LEFT JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
    LEFT JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
    WHERE t.table_schema = 'public' 
      AND t.table_type = 'BASE TABLE'
      AND t.table_name NOT LIKE 'pg_%'
      AND t.table_name NOT LIKE 'sql_%'
    ORDER BY t.table_name, c.ordinal_position;
  `;
  
  const result = await pool.query(query);
  return result.rows;
}

/**
 * Get all indexes
 */
async function getIndexes() {
  const query = `
    SELECT 
      schemaname,
      tablename,
      indexname,
      indexdef
    FROM pg_indexes 
    WHERE schemaname = 'public'
      AND tablename NOT LIKE 'pg_%'
    ORDER BY tablename, indexname;
  `;
  
  const result = await pool.query(query);
  return result.rows;
}

/**
 * Get all constraints
 */
async function getConstraints() {
  const query = `
    SELECT 
      tc.table_name,
      tc.constraint_name,
      tc.constraint_type,
      cc.check_clause
    FROM information_schema.table_constraints tc
    LEFT JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
    WHERE tc.table_schema = 'public'
      AND tc.constraint_type IN ('CHECK', 'UNIQUE', 'FOREIGN KEY')
    ORDER BY tc.table_name, tc.constraint_type;
  `;
  
  const result = await pool.query(query);
  return result.rows;
}

/**
 * Format column type with length/precision
 */
function formatColumnType(row) {
  let type = row.data_type.toUpperCase();
  
  if (row.character_maximum_length) {
    type += `(${row.character_maximum_length})`;
  } else if (row.numeric_precision && row.numeric_scale) {
    type += `(${row.numeric_precision},${row.numeric_scale})`;
  } else if (row.numeric_precision) {
    type += `(${row.numeric_precision})`;
  }
  
  return type;
}

/**
 * Format constraints for a column
 */
function formatConstraints(row, constraints) {
  const columnConstraints = [];
  
  if (row.constraint_type === 'PRIMARY KEY') {
    columnConstraints.push('PRIMARY KEY');
  }
  
  if (row.constraint_type === 'UNIQUE') {
    columnConstraints.push('UNIQUE');
  }
  
  if (row.is_nullable === 'NO') {
    columnConstraints.push('NOT NULL');
  }
  
  if (row.foreign_table_name) {
    columnConstraints.push(`REFERENCES ${row.foreign_table_name}(${row.foreign_column_name})`);
  }
  
  if (row.column_default) {
    columnConstraints.push(`DEFAULT ${row.column_default}`);
  }
  
  return columnConstraints.join(', ');
}

/**
 * Generate table documentation
 */
function generateTableDocs(tableData, indexes, constraints) {
  const tables = {};
  
  // Group columns by table
  tableData.forEach(row => {
    if (!tables[row.table_name]) {
      tables[row.table_name] = [];
    }
    tables[row.table_name].push(row);
  });
  
  let docs = '';
  
  Object.keys(tables).sort().forEach(tableName => {
    const columns = tables[tableName];
    
    docs += `### ${tableName}\n`;
    docs += `| Column | Type | Constraints | Description |\n`;
    docs += `|--------|------|-------------|-------------|\n`;
    
    columns.forEach(col => {
      const type = formatColumnType(col);
      const constraintStr = formatConstraints(col, constraints);
      docs += `| ${col.column_name} | ${type} | ${constraintStr} | |\n`;
    });
    
    // Add indexes for this table
    const tableIndexes = indexes.filter(idx => idx.tablename === tableName);
    if (tableIndexes.length > 0) {
      docs += `\n**Indexes:**\n`;
      tableIndexes.forEach(idx => {
        docs += `- \`${idx.indexname}\`\n`;
      });
    }
    
    // Add constraints for this table
    const tableConstraints = constraints.filter(c => c.table_name === tableName);
    if (tableConstraints.length > 0) {
      docs += `\n**Constraints:**\n`;
      tableConstraints.forEach(c => {
        if (c.constraint_type === 'CHECK' && c.check_clause) {
          docs += `- \`${c.constraint_name}\`: ${c.check_clause}\n`;
        }
      });
    }
    
    docs += '\n';
  });
  
  return docs;
}

/**
 * Generate complete schema documentation
 */
async function generateSchemaDocs() {
  try {
    console.log('🔍 Connecting to database...');
    
    const [tableData, indexes, constraints] = await Promise.all([
      getTableSchema(),
      getIndexes(),
      getConstraints()
    ]);
    
    console.log('📊 Generating documentation...');
    
    const timestamp = new Date().toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    // Read the template
    const templatePath = path.join(__dirname, '../.rules/database-schema.md');
    let template = fs.readFileSync(templatePath, 'utf8');
    
    // Update timestamp
    template = template.replace('[AUTO_UPDATE_TIMESTAMP]', timestamp);
    
    // Generate table documentation
    const tableDocs = generateTableDocs(tableData, indexes, constraints);
    
    // Replace the tables section (if we want to auto-generate it)
    // For now, we'll just update the timestamp
    
    // Write back to file
    fs.writeFileSync(templatePath, template);
    
    console.log('✅ Database schema documentation updated successfully!');
    console.log(`📝 Updated: ${templatePath}`);
    console.log(`🕒 Timestamp: ${timestamp}`);
    
  } catch (error) {
    console.error('❌ Error generating schema documentation:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

/**
 * Generate detailed schema report (optional)
 */
async function generateDetailedReport() {
  try {
    const [tableData, indexes, constraints] = await Promise.all([
      getTableSchema(),
      getIndexes(),
      getConstraints()
    ]);
    
    const reportPath = path.join(__dirname, '../.rules/database-schema-detailed.md');
    
    let report = `# Detailed Database Schema Report\n\n`;
    report += `**Generated:** ${new Date().toISOString()}\n\n`;
    
    report += `## Tables\n\n`;
    report += generateTableDocs(tableData, indexes, constraints);
    
    report += `## All Indexes\n\n`;
    indexes.forEach(idx => {
      report += `- **${idx.tablename}.${idx.indexname}**\n`;
      report += `  \`\`\`sql\n  ${idx.indexdef}\n  \`\`\`\n\n`;
    });
    
    report += `## All Constraints\n\n`;
    constraints.forEach(c => {
      report += `- **${c.table_name}.${c.constraint_name}** (${c.constraint_type})\n`;
      if (c.check_clause) {
        report += `  \`${c.check_clause}\`\n`;
      }
      report += '\n';
    });
    
    fs.writeFileSync(reportPath, report);
    console.log(`📋 Detailed report generated: ${reportPath}`);
    
  } catch (error) {
    console.error('❌ Error generating detailed report:', error);
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--detailed')) {
    await generateDetailedReport();
  } else {
    await generateSchemaDocs();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  generateSchemaDocs,
  generateDetailedReport
};
