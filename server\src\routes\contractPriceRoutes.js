const express = require('express');
const { body, param, query } = require('express-validator');
const contractPriceController = require('../controllers/contractPriceController');
const { authenticateToken } = require('../middleware/auth');
const { checkPermission } = require('../middleware/permission');

const router = express.Router();

// NOTE: Tạm thời tắt authentication để tập trung phát triển core business features
// Middleware xác thực cho tất cả routes
// router.use(authenticateToken);

/**
 * @route   GET /api/v1/contract-prices/current
 * @desc    Lấy đơn giá hiện tại cho sản phẩm trong hợp đồng
 * @access  Private
 */
router.get('/current', [
  // checkPermission('contract_price', 'view'), // Tạm thời tắt
  query('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  query('date')
    .optional()
    .isISO8601()
    .withMessage('Ngày phải có định dạng YYYY-MM-DD')
], contractPriceController.getCurrentPrice);

/**
 * @route   GET /api/v1/contract-prices/history
 * @desc    Lấy lịch sử giá của sản phẩm trong hợp đồng
 * @access  Private
 */
router.get('/history', [
  // checkPermission('contract_price', 'view'), // Tạm thời tắt
  query('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  query('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương')
], contractPriceController.getPriceHistory);

/**
 * @route   POST /api/v1/contract-prices/set-price
 * @desc    Thiết lập đơn giá với tự động vô hiệu hóa giá cũ
 * @access  Private (Manager+)
 */
router.post('/set-price', [
  // checkPermission('contract_price', 'create'), // Tạm thời tắt
  body('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('price')
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('effective_date')
    .isISO8601()
    .withMessage('Ngày hiệu lực phải có định dạng YYYY-MM-DD'),
  body('expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày hết hạn phải có định dạng YYYY-MM-DD'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Ghi chú không được vượt quá 1000 ký tự'),
  body('auto_deactivate_old')
    .optional()
    .isBoolean()
    .withMessage('auto_deactivate_old phải là boolean')
], contractPriceController.setPrice);

/**
 * @route   GET /api/v1/contract-prices
 * @desc    Lấy tất cả đơn giá với filter
 * @access  Private
 */
router.get('/', [
  // checkPermission('contract_price', 'view'), // Tạm thời tắt
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page phải là số nguyên dương'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit phải là số nguyên từ 1-100'),
  query('contract_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Contract ID phải là số nguyên dương'),
  query('product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID phải là số nguyên dương'),
  query('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active phải là boolean')
], contractPriceController.getAllContractPrices);

/**
 * @route   GET /api/v1/contract-prices/:id
 * @desc    Lấy đơn giá theo ID
 * @access  Private
 */
router.get('/:id', [
  // checkPermission('contract_price', 'view'), // Tạm thời tắt
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID đơn giá phải là số nguyên dương')
], contractPriceController.getContractPriceById);

/**
 * @route   POST /api/v1/contract-prices
 * @desc    Tạo đơn giá mới
 * @access  Private (Manager+)
 */
router.post('/', [
  // checkPermission('contract_price', 'create'), // Tạm thời tắt
  body('contract_id')
    .isInt({ min: 1 })
    .withMessage('ID hợp đồng phải là số nguyên dương'),
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('ID sản phẩm phải là số nguyên dương'),
  body('price')
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('effective_date')
    .isISO8601()
    .withMessage('Ngày hiệu lực phải có định dạng YYYY-MM-DD'),
  body('expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày hết hạn phải có định dạng YYYY-MM-DD'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Ghi chú không được vượt quá 1000 ký tự')
], contractPriceController.createContractPrice);

/**
 * @route   PUT /api/v1/contract-prices/:id
 * @desc    Cập nhật đơn giá
 * @access  Private (Manager+)
 */
router.put('/:id', [
  // checkPermission('contract_price', 'update'), // Tạm thời tắt
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID đơn giá phải là số nguyên dương'),
  body('price')
    .isFloat({ min: 0.01 })
    .withMessage('Đơn giá phải là số dương'),
  body('effective_date')
    .isISO8601()
    .withMessage('Ngày hiệu lực phải có định dạng YYYY-MM-DD'),
  body('expiry_date')
    .optional()
    .isISO8601()
    .withMessage('Ngày hết hạn phải có định dạng YYYY-MM-DD'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active phải là boolean'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Ghi chú không được vượt quá 1000 ký tự')
], contractPriceController.updateContractPrice);

/**
 * @route   DELETE /api/v1/contract-prices/:id
 * @desc    Xóa đơn giá
 * @access  Private (Admin only)
 */
router.delete('/:id', [
  // checkPermission('contract_price', 'delete'), // Tạm thời tắt
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID đơn giá phải là số nguyên dương')
], contractPriceController.deleteContractPrice);

module.exports = router;
