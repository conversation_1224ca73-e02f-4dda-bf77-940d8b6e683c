# IMPLEMENTATION_PLAN.md - Tính năng Theo dõi Công nợ và Tuổi nợ

## 📋 Tổng quan Dự án

**Tên tính năng:** Hệ thống Quản lý Công nợ Phải thu và Phân tích Tuổi nợ  
**Dự án:** Laundry Management System v2  
**Ngày bắt đầu:** [Ngày hiện tại]  
**Ước tính hoàn thành:** 7-10 ngày làm việc  
**Độ ưu tiên:** Cao  

### Mục tiêu
- Xây dựng hệ thống theo dõi công nợ phải thu với phân bổ thanh toán FIFO-only
- <PERSON><PERSON> cấp báo cáo phân tích tuổi nợ chi tiết
- Tích hợp seamless với hệ thống quản lý hợp đồng và sản xuất hiện tại
- **Đơn giản hóa quy trình**: Loại bỏ tùy chọn allocation method, chỉ sử dụng FIFO

## 🎯 Tính năng chính

### 1. Quản lý Công nợ Phải thu (Receivables)
- Ghi nhận công nợ từ production data
- Tracking trạng thái: active, overdue, paid
- Liên kết với customers và contracts

### 2. Quản lý Thanh toán (Payments)
- Ghi nhận các khoản thanh toán từ khách hàng
- **Phân bổ thanh toán FIFO-only**: Tự động áp dụng First In, First Out
- Tracking unallocated amounts
- Preview allocation trước khi tạo thanh toán

### 3. Báo cáo Tuổi nợ (Aging Analysis)
- Phân loại nợ theo độ tuổi: 0-30, 31-60, 61-90, >90 ngày
- Tính toán tự động dựa trên due_date
- Export Excel cho management

## 🔄 FIFO-Only Payment Allocation

### Tại sao chỉ sử dụng FIFO?
1. **Đơn giản hóa**: Loại bỏ sự phức tạp không cần thiết trong UI
2. **Chuẩn kế toán**: FIFO là phương thức được khuyến nghị trong kế toán
3. **Nhất quán**: Đảm bảo tất cả thanh toán được xử lý theo cùng một quy tắc
4. **Tự động hóa**: Không cần can thiệp thủ công từ người dùng

### Cách hoạt động:
- Thanh toán được phân bổ vào công nợ cũ nhất trước (theo transaction_date)
- Stored procedure `allocate_payment_fifo()` xử lý logic phân bổ
- UI hiển thị preview allocation để người dùng xem trước kết quả
- Không có tùy chọn LIFO hay Manual allocation

## 🗄️ Thiết kế Database

### Schema Tables

#### 1. Bảng `receivables` (Công nợ phải thu)
```sql
CREATE TABLE receivables (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    contract_id INTEGER NOT NULL REFERENCES contracts(id),
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    transaction_date DATE NOT NULL,
    due_date DATE NOT NULL,
    description TEXT NOT NULL,
    original_amount DECIMAL(15,2) NOT NULL CHECK (original_amount > 0),
    currency VARCHAR(3) DEFAULT 'VND',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'paid', 'cancelled', 'overdue')),
    payment_terms INTEGER DEFAULT 30,
    notes TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. Bảng `payments` (Thanh toán)
```sql
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL REFERENCES customers(id),
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    payment_method VARCHAR(50) NOT NULL,
    reference_number VARCHAR(100),
    bank_account VARCHAR(100),
    description TEXT,
    status VARCHAR(20) DEFAULT 'confirmed' CHECK (status IN ('pending', 'confirmed', 'cancelled')),
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. Bảng `payment_allocations` (Phân bổ thanh toán FIFO)
```sql
CREATE TABLE payment_allocations (
    id SERIAL PRIMARY KEY,
    payment_id INTEGER NOT NULL REFERENCES payments(id),
    receivable_id INTEGER NOT NULL REFERENCES receivables(id),
    allocated_amount DECIMAL(15,2) NOT NULL CHECK (allocated_amount > 0),
    allocation_order INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Backend Development (Node.js + Express)

### API Endpoints

#### Receivables Management
- `GET /api/receivables` - Danh sách công nợ với filter
- `POST /api/receivables` - Tạo công nợ mới
- `PUT /api/receivables/:id` - Cập nhật công nợ
- `DELETE /api/receivables/:id` - Xóa công nợ
- `GET /api/receivables/:id/payments` - Lịch sử thanh toán

#### Payments Management  
- `GET /api/payments` - Danh sách thanh toán
- `POST /api/payments` - Ghi nhận thanh toán mới (tự động FIFO)
- `PUT /api/payments/:id` - Cập nhật thanh toán
- `DELETE /api/payments/:id` - Xóa thanh toán

#### Reports & Analytics
- `GET /api/reports/aging-analysis` - Báo cáo tuổi nợ
- `GET /api/reports/customer-debt/:customerId` - Công nợ theo khách hàng
- `GET /api/reports/debt-summary` - Tổng hợp công nợ
- `POST /api/receivables/bulk-create` - Tạo công nợ từ production data

### Services Layer
- `ReceivableService.js` - Logic nghiệp vụ công nợ
- `PaymentService.js` - Logic thanh toán và FIFO allocation
- `AgingAnalysisService.js` - Tính toán và phân tích tuổi nợ
- `ReportService.js` - Tạo báo cáo

## 🎨 Frontend Development (React + TypeScript + MUI)

### Page Structure
```
src/pages/receivables/
├── ReceivablesManagement.tsx     # Main page
├── components/
│   ├── ReceivablesFilter.tsx     # Bộ lọc
│   ├── ReceivablesList.tsx       # Danh sách công nợ
│   ├── PaymentForm.tsx           # Form ghi nhận thanh toán
│   ├── AgingReport.tsx           # Báo cáo tuổi nợ
│   ├── DebtSummary.tsx           # Tổng hợp công nợ
│   └── CreateReceivableModal.tsx # Modal tạo công nợ
├── hooks/
│   ├── useReceivables.ts         # Hook quản lý receivables
│   ├── usePayments.ts            # Hook quản lý payments
│   └── useAgingAnalysis.ts       # Hook báo cáo tuổi nợ
└── types/
    └── receivables.types.ts      # TypeScript definitions
```

### Navigation Integration
- Thêm menu "Quản lý Công nợ" vào sidebar
- Route: `/receivables`
- Icon: AccountBalance hoặc Receipt

## 📅 Timeline và Checklist

### Phase 1: Database & Backend Core (3 ngày)
**Ngày 1-2: Database Setup**
- [x] Tạo migration files cho 3 bảng chính
- [x] Tạo indexes cho performance
- [x] Viết stored procedures cho FIFO allocation
- [x] Tạo views cho aging analysis
- [x] Test database schema với sample data

**Ngày 3: Backend Services**
- [x] Setup base models (Receivable, Payment, PaymentAllocation)
- [x] Implement ReceivableService với CRUD operations
- [x] Implement PaymentService với FIFO logic
- [x] Viết unit tests cho services
- [x] Setup API routes cơ bản

### Phase 2: API Development (2 ngày)
**Ngày 4: Core APIs**
- [x] Implement receivables CRUD endpoints
- [x] Implement payments endpoints với auto-allocation
- [x] Add validation middleware
- [x] Add error handling
- [x] Test APIs với Postman/Thunder Client

**Ngày 5: Reports & Analytics**
- [x] Implement aging analysis endpoints
- [x] Implement customer debt summary APIs
- [x] Add filtering và pagination
- [x] Optimize queries cho performance
- [x] Add API documentation

### Phase 3: Frontend Core (2-3 ngày)
**Ngày 6: Base Components**
- [x] Setup page structure và routing
- [x] Tạo ReceivablesManagement main page
- [x] Implement ReceivablesFilter component
- [x] Implement ReceivablesList với MUI DataGrid
- [x] Add navigation menu item

**Ngày 7: Forms & Modals**
- [x] Implement PaymentForm với validation
- [x] Implement CreateReceivableModal
- [x] Add form submission logic
- [x] Integrate với backend APIs
- [x] Add loading states và error handling

**Ngày 8: Reports & Analytics UI**
- [x] Implement AgingReport component
- [x] Implement DebtSummary dashboard
- [x] Add charts với recharts hoặc MUI X Charts
- [x] Add export functionality (PDF/Excel)
- [x] Responsive design optimization

### Phase 4: Integration & Testing (1-2 ngày)
**Ngày 9: Integration**
- [ ] Integrate với existing customer/contract data
- [ ] Add bulk create receivables từ production data
- [ ] Test end-to-end workflows
- [ ] Performance optimization
- [ ] Cross-browser testing

**Ngày 10: Polish & Deployment**
- [ ] UI/UX improvements
- [ ] Add comprehensive error messages
- [ ] Add user permissions checking
- [ ] Final testing
- [ ] Documentation update
- [ ] Deploy to staging/production

## 🎯 Technical Requirements

### Performance
- [ ] Database queries < 500ms cho aging reports
- [ ] Frontend rendering < 2s cho large datasets
- [ ] Pagination cho tables > 100 records
- [ ] Lazy loading cho heavy components

### Security
- [ ] Input validation ở cả frontend và backend
- [ ] SQL injection protection
- [ ] User authentication/authorization
- [ ] Audit trail cho financial transactions

### UI/UX Standards
- [ ] MUI theme consistency (0.85rem font, 48px input height)
- [ ] Responsive design cho laptop screens
- [ ] Loading states cho async operations
- [ ] Error boundaries và graceful error handling
- [ ] Accessibility compliance (WCAG 2.1)

## 🧪 Testing Strategy

### Backend Testing
- [ ] Unit tests cho services (coverage > 80%)
- [ ] Integration tests cho APIs
- [ ] Database transaction testing
- [ ] FIFO allocation logic testing

### Frontend Testing
- [ ] Component unit tests với React Testing Library
- [ ] Integration tests cho forms
- [ ] E2E tests cho critical workflows
- [ ] Visual regression testing

## 📊 Success Metrics

### Functional Requirements
- [ ] Có thể ghi nhận công nợ từ production data
- [ ] Thanh toán được phân bổ chính xác theo FIFO
- [ ] Báo cáo tuổi nợ hiển thị đúng thông tin
- [ ] Performance đáp ứng yêu cầu

### Business Requirements
- [x] User có thể trả lời câu hỏi: "Khách hàng X còn nợ bao nhiều?"
- [x] Báo cáo aging analysis hỗ trợ quyết định thu hồi nợ
- [x] Hệ thống tích hợp seamless với workflow hiện tại
- [x] **FIFO-Only Allocation**: Hệ thống chỉ sử dụng phương thức FIFO để đơn giản hóa quy trình

## 🚀 Deployment Plan

### Database Migration
```bash
# Run migrations
npm run migrate:up

# Seed sample data (optional)
npm run seed:receivables
```

### Environment Variables
```env
# Add to .env
DB_RECEIVABLES_TABLE_PREFIX=recv_
FIFO_ALLOCATION_ENABLED=true
AGING_REPORT_CACHE_TTL=3600
```

### Monitoring
- [ ] Add logging cho financial transactions
- [ ] Setup alerts cho failed payments
- [ ] Monitor database performance
- [ ] Track user adoption metrics

## 📝 Notes & Considerations

### Business Rules
- Payment terms mặc định: 30 ngày (có thể override từ contract)
- FIFO allocation: Thanh toán áp dụng cho nợ cũ nhất trước
- Aging buckets: 0-30, 31-60, 61-90, >90 ngày
- Currency: VND (có thể mở rộng sau)

### Future Enhancements
- [ ] Multi-currency support
- [ ] Automated payment reminders
- [ ] Integration với accounting systems
- [ ] Mobile app support
- [ ] Advanced reporting với BI tools

## 💡 FIFO Payment Allocation Logic

### Nguyên tắc FIFO
Khi khách hàng thanh toán, số tiền sẽ được phân bổ theo thứ tự:
1. Trả hết khoản nợ cũ nhất trước (theo transaction_date)
2. Nếu còn tiền thừa, chuyển sang khoản nợ tiếp theo
3. Tiếp tục cho đến khi hết tiền thanh toán

### Ví dụ minh họa
**Công nợ:**
- 01/01/2025: 50 triệu (đến hạn 21/01/2025)
- 01/02/2025: 100 triệu (đến hạn 03/03/2025)
- 01/03/2025: 150 triệu (đến hạn 26/03/2025)

**Thanh toán:**
- 15/01/2025: 40 triệu → Trả cho khoản 01/01/2025 (còn lại 10 triệu)
- 20/01/2025: 60 triệu → 10 triệu trả hết khoản 01/01/2025 + 50 triệu trả cho khoản 01/02/2025

**Kết quả tại 31/03/2025:**
- INV-001 (01/01): PAID (0 VND)
- INV-002 (01/02): 50 triệu còn lại (quá hạn 28 ngày)
- INV-003 (01/03): 150 triệu (quá hạn 5 ngày)

### Stored Procedure FIFO
```sql
CREATE OR REPLACE FUNCTION allocate_payment_fifo(
    p_payment_id INTEGER,
    p_customer_id INTEGER,
    p_payment_amount DECIMAL,
    p_payment_date DATE
) RETURNS VOID AS $$
DECLARE
    remaining_amount DECIMAL := p_payment_amount;
    receivable_record RECORD;
    allocated_amount DECIMAL;
    allocation_order INTEGER := 1;
BEGIN
    FOR receivable_record IN
        SELECT
            r.id,
            r.original_amount,
            COALESCE(SUM(pa.allocated_amount), 0) as total_allocated,
            (r.original_amount - COALESCE(SUM(pa.allocated_amount), 0)) as remaining_balance
        FROM receivables r
        LEFT JOIN payment_allocations pa ON r.id = pa.receivable_id
        WHERE r.customer_id = p_customer_id
        AND r.transaction_date <= p_payment_date
        GROUP BY r.id, r.original_amount, r.transaction_date
        HAVING (r.original_amount - COALESCE(SUM(pa.allocated_amount), 0)) > 0
        ORDER BY r.transaction_date ASC, r.id ASC
    LOOP
        EXIT WHEN remaining_amount <= 0;

        allocated_amount := LEAST(remaining_amount, receivable_record.remaining_balance);

        INSERT INTO payment_allocations (
            payment_id, receivable_id, allocated_amount, allocation_order
        ) VALUES (
            p_payment_id, receivable_record.id, allocated_amount, allocation_order
        );

        remaining_amount := remaining_amount - allocated_amount;
        allocation_order := allocation_order + 1;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## 📊 Database Views và Queries

### View: Receivables Balance với FIFO
```sql
CREATE VIEW receivables_balance_fifo AS
SELECT
    r.id,
    r.customer_id,
    r.contract_id,
    r.invoice_number,
    r.transaction_date,
    r.due_date,
    r.description,
    r.original_amount,
    COALESCE(pa.total_allocated, 0) as total_paid,
    (r.original_amount - COALESCE(pa.total_allocated, 0)) as remaining_balance,
    CASE
        WHEN (r.original_amount - COALESCE(pa.total_allocated, 0)) <= 0 THEN 'paid'
        WHEN CURRENT_DATE > r.due_date THEN 'overdue'
        ELSE 'active'
    END as balance_status,
    CASE
        WHEN CURRENT_DATE > r.due_date
        THEN CURRENT_DATE - r.due_date
        ELSE 0
    END as days_overdue
FROM receivables r
LEFT JOIN (
    SELECT
        receivable_id,
        SUM(allocated_amount) as total_allocated
    FROM payment_allocations
    GROUP BY receivable_id
) pa ON r.id = pa.receivable_id
WHERE r.status != 'cancelled';
```

### Query: Aging Analysis Report
```sql
SELECT
    c.name as customer_name,
    c.tax_code,
    SUM(CASE WHEN rb.days_overdue = 0 THEN rb.remaining_balance ELSE 0 END) as current_amount,
    SUM(CASE WHEN rb.days_overdue BETWEEN 1 AND 30 THEN rb.remaining_balance ELSE 0 END) as days_1_30,
    SUM(CASE WHEN rb.days_overdue BETWEEN 31 AND 60 THEN rb.remaining_balance ELSE 0 END) as days_31_60,
    SUM(CASE WHEN rb.days_overdue BETWEEN 61 AND 90 THEN rb.remaining_balance ELSE 0 END) as days_61_90,
    SUM(CASE WHEN rb.days_overdue > 90 THEN rb.remaining_balance ELSE 0 END) as over_90_days,
    SUM(rb.remaining_balance) as total_outstanding
FROM receivables_balance_fifo rb
JOIN customers c ON rb.customer_id = c.id
WHERE rb.remaining_balance > 0
GROUP BY c.id, c.name, c.tax_code
ORDER BY total_outstanding DESC;
```

---

**Người thực hiện:** [Tên developer]
**Reviewer:** [Tên reviewer]
**Ngày cập nhật cuối:** [Ngày hiện tại]
