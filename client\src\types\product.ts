/**
 * Product Type Definitions
 */

/**
 * Product Interface
 */
export interface Product {
  id: number;
  code: string;
  name: string;
  description?: string | null;
  unit_type: ProductUnitType;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by_name?: string;
}

/**
 * Product Unit Types
 */
export type ProductUnitType = 'kg' | 'piece' | 'meter' | 'liter' | 'box' | 'set';

/**
 * Product Unit Type Options for UI
 */
export const PRODUCT_UNIT_TYPE_OPTIONS: Array<{
  value: ProductUnitType;
  label: string;
}> = [
  { value: 'kg', label: 'Kilogram (kg)' },
  { value: 'piece', label: 'Cái/Chiếc' },
  { value: 'meter', label: 'Mét (m)' },
  { value: 'liter', label: 'Lít (l)' },
  { value: 'box', label: 'Hộp/Thùng' },
  { value: 'set', label: 'Bộ/Set' },
];

/**
 * Product Create Request Interface
 * Note: code sẽ được tự động generate bởi backend
 */
export interface ProductCreateRequest {
  name: string;
  description?: string;
  unit_type: ProductUnitType;
  is_active?: boolean;
}

/**
 * Product Update Request Interface
 */
export interface ProductUpdateRequest {
  name: string;
  description?: string;
  unit_type: ProductUnitType;
  is_active?: boolean;
}

/**
 * Product Search/Filter Options Interface
 */
export interface ProductFilterOptions {
  page?: number;
  limit?: number;
  search?: string;
  unit_type?: ProductUnitType;
  is_active?: boolean;
  sortBy?: ProductSortField;
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Product Sort Fields
 */
export type ProductSortField = 'id' | 'code' | 'name' | 'unit_type' | 'is_active' | 'created_at' | 'updated_at';

/**
 * Product Sort Options for UI
 */
export const PRODUCT_SORT_OPTIONS: Array<{
  value: ProductSortField;
  label: string;
}> = [
  { value: 'code', label: 'Mã sản phẩm' },
  { value: 'name', label: 'Tên sản phẩm' },
  { value: 'unit_type', label: 'Đơn vị tính' },
  { value: 'is_active', label: 'Trạng thái' },
  { value: 'created_at', label: 'Ngày tạo' },
  { value: 'updated_at', label: 'Ngày cập nhật' },
];

/**
 * Product Form Data Interface (for forms)
 * Note: code không còn trong form vì sẽ được auto-generate
 */
export interface ProductFormData {
  name: string;
  description: string;
  unit_type: ProductUnitType;
  is_active: boolean;
}

/**
 * Product Validation Error Interface
 */
export interface ProductValidationError {
  field: keyof ProductFormData;
  message: string;
}

/**
 * Product Validation Result Interface
 */
export interface ProductValidationResult {
  isValid: boolean;
  errors: ProductValidationError[];
}

/**
 * Product Status Options for UI
 */
export const PRODUCT_STATUS_OPTIONS = [
  { value: true, label: 'Hoạt động', color: 'success' as const },
  { value: false, label: 'Ngừng hoạt động', color: 'error' as const },
];

/**
 * Product Table Column Interface
 */
export interface ProductTableColumn {
  id: keyof Product | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

/**
 * Product Table Columns Configuration
 */
export const PRODUCT_TABLE_COLUMNS: ProductTableColumn[] = [
  {
    id: 'code',
    label: 'Mã sản phẩm',
    minWidth: 120,
    sortable: true,
  },
  {
    id: 'name',
    label: 'Tên sản phẩm',
    minWidth: 200,
    sortable: true,
  },
  {
    id: 'description',
    label: 'Mô tả',
    minWidth: 150,
    sortable: false,
  },
  {
    id: 'unit_type',
    label: 'Đơn vị tính',
    minWidth: 100,
    align: 'center',
    sortable: true,
  },
  {
    id: 'is_active',
    label: 'Trạng thái',
    minWidth: 100,
    align: 'center',
    sortable: true,
  },
  {
    id: 'created_at',
    label: 'Ngày tạo',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'actions',
    label: 'Thao tác',
    minWidth: 120,
    align: 'center',
    sortable: false,
  },
];

/**
 * Product Error Types
 */
export type ProductErrorType =
  | 'PRODUCT_NOT_FOUND'
  | 'PRODUCT_CODE_EXISTS'
  | 'INVALID_UNIT_TYPE'
  | 'VALIDATION_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Product Error Interface
 */
export interface ProductError {
  type: ProductErrorType;
  message: string;
  details?: string[];
}

/**
 * Product Action Types for State Management
 */
export type ProductActionType =
  | 'FETCH_PRODUCTS_START'
  | 'FETCH_PRODUCTS_SUCCESS'
  | 'FETCH_PRODUCTS_FAILURE'
  | 'CREATE_PRODUCT_START'
  | 'CREATE_PRODUCT_SUCCESS'
  | 'CREATE_PRODUCT_FAILURE'
  | 'UPDATE_PRODUCT_START'
  | 'UPDATE_PRODUCT_SUCCESS'
  | 'UPDATE_PRODUCT_FAILURE'
  | 'DELETE_PRODUCT_START'
  | 'DELETE_PRODUCT_SUCCESS'
  | 'DELETE_PRODUCT_FAILURE'
  | 'SET_SELECTED_PRODUCT'
  | 'CLEAR_SELECTED_PRODUCT'
  | 'CLEAR_ERROR';

/**
 * Product State Interface for Context/Redux
 */
export interface ProductState {
  products: Product[];
  selectedProduct: Product | null;
  loading: boolean;
  error: ProductError | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
}

/**
 * Product Context Type
 */
export interface ProductContextType {
  state: ProductState;
  fetchProducts: (options?: ProductFilterOptions) => Promise<void>;
  createProduct: (data: ProductCreateRequest) => Promise<Product>;
  updateProduct: (id: number, data: ProductUpdateRequest) => Promise<Product>;
  deleteProduct: (id: number) => Promise<void>;
  setSelectedProduct: (product: Product | null) => void;
  clearError: () => void;
}

/**
 * Product Hook Return Type
 */
export interface UseProductReturn extends ProductContextType {
  // Additional computed properties
  activeProducts: Product[];
  inactiveProducts: Product[];
  totalProducts: number;
}

/**
 * Product Export Data Interface
 */
export interface ProductExportData {
  code: string;
  name: string;
  description: string;
  unit_type: string;
  status: string;
  created_date: string;
}

/**
 * Product Import Data Interface
 */
export interface ProductImportData {
  code: string;
  name: string;
  description?: string;
  unit_type: ProductUnitType;
  is_active?: boolean;
}

/**
 * Product Bulk Operation Result Interface
 */
export interface ProductBulkOperationResult {
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    error: string;
  }>;
}
