/**
 * Script để chạy migration cập nhật workflow sản lượng
 * Chạy: node run_production_workflow_migration.js
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'tinhtam-hp',
  password: '110591',
  port: 5432,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 Bắt đầu migration workflow sản lượng...');
    
    // Đọc file migration
    const migrationPath = path.join(__dirname, '../database/migrations/007_update_production_workflow.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Chạy migration
    console.log('📝 Đang thực thi migration...');
    await client.query(migrationSQL);
    
    console.log('✅ Migration hoàn thành thành công!');
    
    // Kiểm tra kết quả
    console.log('\n📊 Kiểm tra kết quả migration:');
    
    // Kiểm tra cấu trúc bảng daily_production
    const tableInfo = await client.query(`
      SELECT column_name, data_type, column_default, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'daily_production' 
      AND column_name IN ('status', 'updated_by')
      ORDER BY ordinal_position;
    `);
    
    console.log('Cấu trúc bảng daily_production:');
    tableInfo.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} (default: ${row.column_default || 'NULL'})`);
    });
    
    // Kiểm tra constraint
    const constraints = await client.query(`
      SELECT constraint_name, constraint_type
      FROM information_schema.table_constraints 
      WHERE table_name = 'daily_production' 
      AND constraint_name LIKE '%status%';
    `);
    
    console.log('\nConstraints cho status:');
    constraints.rows.forEach(row => {
      console.log(`  - ${row.constraint_name}: ${row.constraint_type}`);
    });
    
    // Kiểm tra trigger
    const triggers = await client.query(`
      SELECT trigger_name, event_manipulation, action_timing
      FROM information_schema.triggers 
      WHERE event_object_table = 'daily_production';
    `);
    
    console.log('\nTriggers:');
    triggers.rows.forEach(row => {
      console.log(`  - ${row.trigger_name}: ${row.action_timing} ${row.event_manipulation}`);
    });
    
    // Kiểm tra bảng production_status_log
    const logTableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'production_status_log'
      );
    `);
    
    console.log(`\nBảng production_status_log: ${logTableExists.rows[0].exists ? 'Đã tạo' : 'Chưa tạo'}`);
    
    // Thống kê dữ liệu hiện tại
    const statusStats = await client.query(`
      SELECT status, COUNT(*) as count
      FROM daily_production 
      GROUP BY status
      ORDER BY count DESC;
    `);
    
    console.log('\nThống kê trạng thái hiện tại:');
    statusStats.rows.forEach(row => {
      console.log(`  - ${row.status}: ${row.count} bản ghi`);
    });
    
  } catch (error) {
    console.error('❌ Lỗi khi chạy migration:', error.message);
    console.error('Chi tiết lỗi:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Chạy migration
runMigration().then(() => {
  console.log('\n🎉 Hoàn thành migration workflow sản lượng!');
  console.log('\n📋 Các bước tiếp theo:');
  console.log('1. Khởi động lại server: npm run dev');
  console.log('2. Test các API endpoints mới');
  console.log('3. Cập nhật frontend UI');
  process.exit(0);
}).catch(error => {
  console.error('💥 Migration thất bại:', error);
  process.exit(1);
});
