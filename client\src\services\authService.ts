import { get, post } from './api';
import { User, LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse } from '../types/auth';

/**
 * Authentication Service
 * Handles all authentication-related API calls
 */
class AuthService {
  private readonly baseUrl = '/auth';

  /**
   * Login user
   */
  async login(email: string, password: string): Promise<LoginResponse> {
    const loginData: LoginRequest = { email, password };
    const response = await post<LoginResponse>(`${this.baseUrl}/login`, loginData);
    return response.data;
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await post(`${this.baseUrl}/logout`);
    } catch (error) {
      // Logout can fail if token is already invalid, but we still want to clear local storage
      console.warn('Logout API call failed:', error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    const refreshData: RefreshTokenRequest = { refreshToken };
    const response = await post<RefreshTokenResponse>(`${this.baseUrl}/refresh`, refreshData);
    return response.data;
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<User> {
    const response = await get<User>(`${this.baseUrl}/me`);
    return response.data;
  }

  /**
   * Register new user (admin only)
   */
  async register(userData: {
    name: string;
    email: string;
    password: string;
    position?: string;
  }): Promise<User> {
    const response = await post<User>(`${this.baseUrl}/register`, userData);
    return response.data;
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 6) {
      errors.push('Mật khẩu phải có ít nhất 6 ký tự');
    }

    if (password.length > 100) {
      errors.push('Mật khẩu không được quá 100 ký tự');
    }

    // Optional: Add more password strength requirements
    // if (!/(?=.*[a-z])/.test(password)) {
    //   errors.push('Mật khẩu phải có ít nhất 1 chữ thường');
    // }

    // if (!/(?=.*[A-Z])/.test(password)) {
    //   errors.push('Mật khẩu phải có ít nhất 1 chữ hoa');
    // }

    // if (!/(?=.*\d)/.test(password)) {
    //   errors.push('Mật khẩu phải có ít nhất 1 số');
    // }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate login form data
   */
  validateLoginData(email: string, password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!email || email.trim().length === 0) {
      errors.push('Email là bắt buộc');
    } else if (!this.validateEmail(email)) {
      errors.push('Email không đúng định dạng');
    }

    if (!password || password.length === 0) {
      errors.push('Mật khẩu là bắt buộc');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate registration form data
   */
  validateRegistrationData(userData: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
    position?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Name validation
    if (!userData.name || userData.name.trim().length === 0) {
      errors.push('Tên là bắt buộc');
    } else if (userData.name.length > 100) {
      errors.push('Tên không được quá 100 ký tự');
    }

    // Email validation
    if (!userData.email || userData.email.trim().length === 0) {
      errors.push('Email là bắt buộc');
    } else if (!this.validateEmail(userData.email)) {
      errors.push('Email không đúng định dạng');
    } else if (userData.email.length > 100) {
      errors.push('Email không được quá 100 ký tự');
    }

    // Password validation
    const passwordValidation = this.validatePassword(userData.password);
    if (!passwordValidation.isValid) {
      errors.push(...passwordValidation.errors);
    }

    // Confirm password validation
    if (userData.password !== userData.confirmPassword) {
      errors.push('Mật khẩu xác nhận không khớp');
    }

    // Position validation
    if (userData.position && userData.position.length > 100) {
      errors.push('Chức vụ không được quá 100 ký tự');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    const refreshToken = localStorage.getItem('refreshToken');
    return !!(token && refreshToken);
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * Get stored refresh token
   */
  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  /**
   * Clear stored tokens
   */
  clearTokens(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }

  /**
   * Store tokens
   */
  storeTokens(token: string, refreshToken: string): void {
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', refreshToken);
  }

  /**
   * Decode JWT token (simple decode without verification)
   */
  decodeToken(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) {
        return true;
      }
      
      const currentTime = Date.now() / 1000;
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * Get user info from token
   */
  getUserFromToken(token: string): Partial<User> | null {
    try {
      const decoded = this.decodeToken(token);
      if (!decoded) {
        return null;
      }
      
      return {
        id: decoded.userId,
        email: decoded.email,
        name: decoded.name
      };
    } catch (error) {
      return null;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
