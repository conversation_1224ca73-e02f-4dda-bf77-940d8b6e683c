# Demo & Test Guide - QuickProductionForm v2.0

## Hướng dẫn test 5 tính năng mới

### 🚀 **Chuẩn bị test**
1. Mở ứng dụng: http://localhost:5374
2. <PERSON><PERSON><PERSON> nhập vào hệ thống
3. V<PERSON><PERSON> tab **"Danh sách hợp đồng"**
4. <PERSON><PERSON><PERSON> một hợp đồng đã có sản phẩm được thiết lập giá

### 📋 **Test Case 1: Sắp xếp sản phẩm theo thứ tự thiết lập giá**

**Mục tiêu**: Kiểm tra sản phẩm được sắp xếp theo ID contract_prices tăng dần

**Các bước test**:
1. Nhấn nút **"Nhập sản lượng nhanh"** tại một hợp đồng
2. <PERSON><PERSON> sát thứ tự sản phẩm trong form

**Kết quả mong đợi**:
- <PERSON><PERSON>n phẩm được thiết lập giá trước sẽ hiển thị trước
- Th<PERSON> tự sắp xếp nhất quán theo thời gian thiết lập giá

### 📋 **Test Case 2: Số lượng mặc định = 0**

**Mục tiêu**: Kiểm tra tất cả sản phẩm có số lượng mặc định = 0

**Các bước test**:
1. Mở form "Nhập sản lượng nhanh"
2. Kiểm tra giá trị trong các trường "Số lượng"

**Kết quả mong đợi**:
- Tất cả trường số lượng hiển thị = 0
- Thành tiền của tất cả dòng = 0
- Tổng cộng = 0

### 📋 **Test Case 3: Bỏ qua sản phẩm có số lượng = 0**

**Mục tiêu**: Kiểm tra hệ thống chỉ lưu sản phẩm có số lượng > 0

**Các bước test**:
1. Mở form "Nhập sản lượng nhanh"
2. Chỉ nhập số lượng cho 2-3 sản phẩm (để lại các sản phẩm khác = 0)
3. Nhấn **"Lưu sản lượng"**
4. Kiểm tra dữ liệu đã lưu

**Kết quả mong đợi**:
- Form lưu thành công
- Chỉ có các sản phẩm có số lượng > 0 được lưu vào database
- Không có bản ghi nào với số lượng = 0

**Test edge case**:
- Để tất cả sản phẩm có số lượng = 0 → Hiển thị lỗi: "Phải có ít nhất một sản phẩm có số lượng lớn hơn 0"

### 📋 **Test Case 4: Tổng cộng thông minh**

**Mục tiêu**: Kiểm tra tổng cộng chỉ tính sản phẩm có số lượng > 0

**Các bước test**:
1. Mở form "Nhập sản lượng nhanh"
2. Nhập số lượng cho một vài sản phẩm:
   - Sản phẩm A: số lượng = 2, đơn giá = 15,000 → thành tiền = 30,000
   - Sản phẩm B: số lượng = 0, đơn giá = 20,000 → thành tiền = 0
   - Sản phẩm C: số lượng = 1, đơn giá = 25,000 → thành tiền = 25,000
3. Kiểm tra phần "Tổng cộng"

**Kết quả mong đợi**:
- Tổng cộng = 55,000 VND (chỉ tính A + C)
- Hiển thị thông báo: "(Chỉ tính 2/3 sản phẩm có số lượng)"

### 📋 **Test Case 5: Format số kiểu Việt Nam**

**Mục tiêu**: Kiểm tra format số với dấu phẩy ngăn cách

**Các bước test**:
1. Mở form "Nhập sản lượng nhanh"
2. Kiểm tra hiển thị đơn giá của các sản phẩm
3. Nhập số lượng và quan sát thành tiền
4. Thử chỉnh sửa đơn giá (nếu được phép)

**Kết quả mong đợi**:
- **Đơn giá**: Hiển thị "15,000" thay vì "15000"
- **Thành tiền**: Tự động format khi thay đổi số lượng
- **Tổng cộng**: Format nhất quán "55,000 VND"
- **Input**: Khi nhập giá, tự động thêm dấu phẩy

**Test input formatting**:
1. Click vào trường đơn giá
2. Nhập "25000" → Tự động hiển thị "25,000"
3. Nhập "1500000" → Tự động hiển thị "1,500,000"

### 🎯 **Test Workflow hoàn chỉnh**

**Scenario**: Nhập sản lượng cho hợp đồng có 5 sản phẩm

1. **Mở form**: Nhấn "Nhập sản lượng nhanh" từ danh sách hợp đồng
2. **Kiểm tra auto-load**: 5 sản phẩm được thêm tự động với số lượng = 0
3. **Chọn ngày**: Chọn ngày sản xuất (auto-focus)
4. **Nhập số lượng**: 
   - Sản phẩm 1: 3
   - Sản phẩm 2: 0 (bỏ qua)
   - Sản phẩm 3: 2
   - Sản phẩm 4: 0 (bỏ qua)
   - Sản phẩm 5: 1
5. **Kiểm tra tổng**: Chỉ tính 3 sản phẩm có số lượng > 0
6. **Lưu**: Nhấn "Lưu sản lượng"
7. **Verify**: Kiểm tra chỉ có 3 bản ghi được tạo

### ⚠️ **Các trường hợp cần test đặc biệt**

#### Edge Cases:
1. **Hợp đồng không có sản phẩm nào có giá**
   - Hiển thị thông báo cảnh báo
   - Không cho phép thêm sản phẩm

2. **Tất cả sản phẩm có số lượng = 0**
   - Hiển thị lỗi validation
   - Không cho phép lưu

3. **Số lượng âm**
   - Hiển thị lỗi: "Số lượng không được âm"

4. **Format số lớn**
   - Test với số tiền > 1 triệu
   - Kiểm tra hiển thị: "1,500,000"

### 📊 **Checklist hoàn thành**

- [ ] Sản phẩm sắp xếp đúng thứ tự (theo ID contract_prices)
- [ ] Số lượng mặc định = 0 cho tất cả sản phẩm
- [ ] Chỉ lưu sản phẩm có số lượng > 0
- [ ] Tổng cộng tính đúng (chỉ sản phẩm có số lượng > 0)
- [ ] Format số hiển thị dấu phẩy ngăn cách
- [ ] Input price tự động format khi nhập
- [ ] Validation hoạt động đúng
- [ ] UX/UI mượt mà, không có lỗi console

### 🎉 **Kết quả mong đợi**

Sau khi test thành công, form "Nhập sản lượng nhanh" sẽ:
- **Tiết kiệm thời gian** hơn nữa với số lượng mặc định = 0
- **Chính xác hơn** với việc chỉ lưu sản phẩm thực sự có phát sinh
- **Trực quan hơn** với format số kiểu Việt Nam
- **Thông minh hơn** với tổng cộng chỉ tính sản phẩm có số lượng
- **Có thứ tự** với sắp xếp theo thời gian thiết lập giá
