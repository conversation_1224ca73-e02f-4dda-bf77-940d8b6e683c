/**
 * PriceHistory Component
 * Hiển thị lịch sử thay đổi giá của sản phẩm
 */

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  Chip,
  Card,
  CardContent,
  Grid,
  Divider,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Remove as RemoveIcon,
} from '@mui/icons-material';
import { ContractPrice, PriceHistoryItem } from '../../types/contractPrice';
// Temporary inline formatters
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
};

const formatDateTime = (dateString: string): string => {
  if (!dateString) return '';
  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';
  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

interface PriceHistoryProps {
  open: boolean;
  prices: PriceHistoryItem[];
  productName?: string;
  contractNumber?: string;
  loading?: boolean;
  onClose: () => void;
}

const PriceHistory: React.FC<PriceHistoryProps> = ({
  open,
  prices,
  productName,
  contractNumber,
  loading = false,
  onClose,
}) => {
  // Sắp xếp theo ngày hiệu lực giảm dần (mới nhất trước)
  const sortedPrices = [...prices].sort((a, b) =>
    new Date(b.effective_date).getTime() - new Date(a.effective_date).getTime()
  );

  const getPriceChangeIcon = (currentPrice: number, previousPrice?: number) => {
    if (!previousPrice) return <RemoveIcon fontSize="small" />;

    if (currentPrice > previousPrice) {
      return <TrendingUpIcon fontSize="small" color="error" />;
    } else if (currentPrice < previousPrice) {
      return <TrendingDownIcon fontSize="small" color="success" />;
    } else {
      return <RemoveIcon fontSize="small" />;
    }
  };

  const getPriceChangeColor = (currentPrice: number, previousPrice?: number) => {
    if (!previousPrice) return 'default';

    if (currentPrice > previousPrice) {
      return 'error';
    } else if (currentPrice < previousPrice) {
      return 'success';
    } else {
      return 'default';
    }
  };

  const getStatusChip = (price: PriceHistoryItem) => {
    const isActive = price.is_active;
    const isExpired = price.expiry_date && new Date(price.expiry_date) < new Date();

    if (!isActive) {
      return <Chip label="Không hiệu lực" color="default" size="small" />;
    }

    if (isExpired) {
      return <Chip label="Hết hạn" color="error" size="small" />;
    }

    return <Chip label="Hiệu lực" color="success" size="small" />;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { minHeight: 600 }
      }}
    >
      <DialogTitle>
        <Box>
          <Typography variant="h6">
            Lịch sử giá
          </Typography>
          {productName && contractNumber && (
            <Typography variant="body2" color="textSecondary">
              {productName} - {contractNumber}
            </Typography>
          )}
        </Box>
      </DialogTitle>

      <DialogContent>
        {loading ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography>Đang tải lịch sử giá...</Typography>
          </Box>
        ) : sortedPrices.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="textSecondary">
              Không có lịch sử giá
            </Typography>
          </Box>
        ) : (
          <Box>
            {/* Price History Cards */}
            <Box sx={{ mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Lịch sử thay đổi giá
              </Typography>
              <Grid container spacing={2}>
                {sortedPrices.map((price, index) => {
                  const previousPrice = sortedPrices[index + 1];
                  const changeIcon = getPriceChangeIcon(price.price, previousPrice?.price);
                  const changeColor = getPriceChangeColor(price.price, previousPrice?.price);
                  const priceChange = previousPrice ? price.price - previousPrice.price : 0;
                  const changePercent = previousPrice ?
                    ((price.price - previousPrice.price) / previousPrice.price * 100) : 0;

                  return (
                    <Grid item xs={12} sm={6} md={4} key={price.id}>
                      <Card
                        variant="outlined"
                        sx={{
                          height: '100%',
                          border: price.is_active ? 2 : 1,
                          borderColor: price.is_active ? 'primary.main' : 'divider'
                        }}
                      >
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2" color="textSecondary">
                              {formatDate(price.effective_date)}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                              {changeIcon}
                            </Box>
                          </Box>

                          <Typography variant="h5" component="div" sx={{ mb: 1, fontWeight: 'bold' }}>
                            {formatCurrency(price.price)}
                          </Typography>

                          {previousPrice && (
                            <Typography
                              variant="body2"
                              color={changeColor}
                              sx={{ mb: 1 }}
                            >
                              {priceChange > 0 ? '+' : ''}{formatCurrency(priceChange)}
                              {changePercent !== 0 && (
                                <span> ({changePercent > 0 ? '+' : ''}{changePercent.toFixed(1)}%)</span>
                              )}
                            </Typography>
                          )}

                          <Box sx={{ mb: 1 }}>
                            {getStatusChip(price)}
                          </Box>

                          {price.expiry_date && (
                            <Typography variant="body2" color="textSecondary" sx={{ mb: 0.5 }}>
                              Hết hạn: {formatDate(price.expiry_date)}
                            </Typography>
                          )}

                          {price.notes && (
                            <Typography variant="body2" sx={{
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical'
                            }}>
                              {price.notes}
                            </Typography>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  );
                })}
              </Grid>
            </Box>

            <Divider sx={{ my: 3 }} />

            {/* Table View */}
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Bảng chi tiết
              </Typography>
              <TableContainer component={Paper}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Ngày hiệu lực</TableCell>
                      <TableCell>Ngày hết hạn</TableCell>
                      <TableCell align="right">Đơn giá</TableCell>
                      <TableCell align="center">Thay đổi</TableCell>
                      <TableCell>Trạng thái</TableCell>
                      <TableCell>Ghi chú</TableCell>
                      <TableCell>Ngày tạo</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sortedPrices.map((price, index) => {
                      const previousPrice = sortedPrices[index + 1];
                      const priceChange = previousPrice ? price.price - previousPrice.price : 0;
                      const changePercent = previousPrice ?
                        ((price.price - previousPrice.price) / previousPrice.price * 100) : 0;

                      return (
                        <TableRow key={price.id} hover>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(price.effective_date)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {price.expiry_date ? formatDate(price.expiry_date) : 'Không giới hạn'}
                            </Typography>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2" fontWeight="medium">
                              {formatCurrency(price.price)}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            {previousPrice ? (
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
                                {getPriceChangeIcon(price.price, previousPrice.price)}
                                <Typography
                                  variant="body2"
                                  color={getPriceChangeColor(price.price, previousPrice.price)}
                                >
                                  {priceChange > 0 ? '+' : ''}{formatCurrency(priceChange)}
                                  {changePercent !== 0 && (
                                    <span> ({changePercent > 0 ? '+' : ''}{changePercent.toFixed(1)}%)</span>
                                  )}
                                </Typography>
                              </Box>
                            ) : (
                              <Typography variant="body2" color="textSecondary">
                                -
                              </Typography>
                            )}
                          </TableCell>
                          <TableCell>
                            {getStatusChip(price)}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                              {price.notes || '-'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="textSecondary">
                              {formatDateTime(price.created_at)}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Đóng
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PriceHistory;
