const { pool } = require('../db');

/**
 * Contract Model
 * Xử lý các thao tác CRUD cho bảng contracts
 */

/**
 * L<PERSON><PERSON> tất cả hợp đồng với phân trang và tìm kiếm
 * @param {Object} options - Tùy chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {number} options.customer_id - Filter theo khách hàng
 * @param {string} options.status - Filter theo trạng thái
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllContracts = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    customer_id = '',
    status = '',
    sortBy = 'created_at',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'contract_number', 'contract_name', 'start_date', 'end_date', 'status', 'created_at', 'updated_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    // Filter theo customer_id
    if (customer_id && !isNaN(parseInt(customer_id))) {
      whereClause += ` AND c.customer_id = $${paramIndex}`;
      queryParams.push(parseInt(customer_id));
      paramIndex++;
    }

    // Filter theo status
    if (status && status.trim()) {
      whereClause += ` AND c.status = $${paramIndex}`;
      queryParams.push(status.trim());
      paramIndex++;
    }

    // Tìm kiếm theo tên hoặc số hợp đồng, bao gồm cả short_name
    if (search && search.trim()) {
      whereClause += ` AND (
        c.contract_number ILIKE $${paramIndex} OR
        c.contract_name ILIKE $${paramIndex} OR
        c.notes ILIKE $${paramIndex} OR
        cust.name ILIKE $${paramIndex} OR
        cust.short_name ILIKE $${paramIndex} OR
        c.short_name ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search.trim()}%`);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM contracts c
      LEFT JOIN customers cust ON c.customer_id = cust.id
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT
        c.id,
        c.contract_number,
        c.customer_id,
        c.contract_name,
        c.start_date,
        c.end_date,
        c.status,
        c.notes,
        c.short_name,
        c.created_at,
        c.updated_at,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        u.name as created_by_name
      FROM contracts c
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN users u ON c.created_by = u.id
      ${whereClause}
      ORDER BY c.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      contracts: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllContracts:', error);
    throw error;
  }
};

/**
 * Lấy hợp đồng theo ID
 * @param {number} id - ID của hợp đồng
 * @returns {Object|null} Thông tin hợp đồng hoặc null nếu không tìm thấy
 */
const getContractById = async (id) => {
  try {
    const query = `
      SELECT
        c.id,
        c.contract_number,
        c.customer_id,
        c.contract_name,
        c.start_date,
        c.end_date,
        c.status,
        c.notes,
        c.short_name,
        c.created_at,
        c.updated_at,
        cust.name as customer_name,
        cust.short_name as customer_short_name,
        cust.tax_code as customer_tax_code,
        u.name as created_by_name
      FROM contracts c
      LEFT JOIN customers cust ON c.customer_id = cust.id
      LEFT JOIN users u ON c.created_by = u.id
      WHERE c.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getContractById:', error);
    throw error;
  }
};

/**
 * Tạo hợp đồng mới
 * @param {Object} contractData - Dữ liệu hợp đồng
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin hợp đồng vừa tạo
 */
const createContract = async (contractData, createdBy) => {
  const {
    contract_number,
    customer_id,
    contract_name,
    start_date,
    end_date,
    status,
    notes
  } = contractData;

  try {
    const query = `
      INSERT INTO contracts (
        contract_number, customer_id, contract_name, start_date, end_date, status, notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `;

    const values = [
      contract_number,
      customer_id,
      contract_name,
      start_date,
      end_date || null,
      status || 'active',
      notes || null,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createContract:', error);
    throw error;
  }
};

/**
 * Cập nhật thông tin hợp đồng
 * @param {number} id - ID của hợp đồng
 * @param {Object} contractData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin hợp đồng sau khi cập nhật
 */
const updateContract = async (id, contractData) => {
  const {
    customer_id,
    contract_name,
    start_date,
    end_date,
    status,
    notes
  } = contractData;

  try {
    const query = `
      UPDATE contracts
      SET
        customer_id = $1,
        contract_name = $2,
        start_date = $3,
        end_date = $4,
        status = $5,
        notes = $6,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $7
      RETURNING *
    `;

    const values = [
      customer_id,
      contract_name,
      start_date,
      end_date || null,
      status || 'active',
      notes || null,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateContract:', error);
    throw error;
  }
};

/**
 * Xóa hợp đồng (hard delete - chỉ admin)
 * @param {number} id - ID của hợp đồng
 * @returns {boolean} True nếu xóa thành công
 */
const deleteContract = async (id) => {
  try {
    const query = `
      DELETE FROM contracts
      WHERE id = $1
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteContract:', error);
    throw error;
  }
};

/**
 * Kiểm tra số hợp đồng đã tồn tại
 * @param {string} contractNumber - Số hợp đồng
 * @param {number} excludeId - ID hợp đồng cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu số hợp đồng đã tồn tại
 */
const checkContractNumberExists = async (contractNumber, excludeId = null) => {
  try {
    if (!contractNumber) return false;

    let query = 'SELECT id FROM contracts WHERE contract_number = $1';
    const params = [contractNumber];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkContractNumberExists:', error);
    throw error;
  }
};

/**
 * Lấy danh sách trạng thái hợp đồng có sẵn
 * @returns {Array} Danh sách trạng thái
 */
const getContractStatuses = async () => {
  try {
    const query = `
      SELECT DISTINCT status
      FROM contracts
      ORDER BY status
    `;

    const result = await pool.query(query);
    return result.rows.map(row => row.status);
  } catch (error) {
    console.error('Error in getContractStatuses:', error);
    throw error;
  }
};

/**
 * Lấy danh sách hợp đồng theo khách hàng
 * @param {number} customerId - ID khách hàng
 * @returns {Array} Danh sách hợp đồng
 */
const getContractsByCustomer = async (customerId) => {
  try {
    const query = `
      SELECT
        c.id,
        c.contract_number,
        c.contract_name,
        c.start_date,
        c.end_date,
        c.status,
        c.created_at
      FROM contracts c
      WHERE c.customer_id = $1
      ORDER BY c.created_at DESC
    `;

    const result = await pool.query(query, [customerId]);
    return result.rows;
  } catch (error) {
    console.error('Error in getContractsByCustomer:', error);
    throw error;
  }
};

module.exports = {
  getAllContracts,
  getContractById,
  createContract,
  updateContract,
  deleteContract,
  checkContractNumberExists,
  getContractStatuses,
  getContractsByCustomer
};
