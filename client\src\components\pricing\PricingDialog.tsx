/**
 * PricingDialog Component
 * Dialog để thêm/sửa đơn giá sản phẩm
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography,
  Box,
  Autocomplete,
  InputAdornment,
  Switch,
  FormControlLabel,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { vi } from 'date-fns/locale';
import { ContractPrice, ContractPriceFormData } from '../../types/contractPrice';
import { Contract } from '../../types/contract';
import { Product } from '../../types/product';
import { convertDateToISOString, convertISOStringToDate } from '../../utils/vietnameseFormatters';
// Temporary inline formatter
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

interface PricingDialogProps {
  open: boolean;
  price?: ContractPrice | null;
  contracts: Contract[];
  products: Product[];
  loading?: boolean;
  error?: string | null;
  onClose: () => void;
  onSubmit: (data: ContractPriceFormData) => void;
}

const PricingDialog: React.FC<PricingDialogProps> = ({
  open,
  price,
  contracts,
  products,
  loading = false,
  error,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState<ContractPriceFormData>({
    contract_id: 0,
    product_id: 0,
    price: 0,
    effective_date: new Date().toISOString().split('T')[0],
    expiry_date: '',
    notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasExpiryDate, setHasExpiryDate] = useState(false);

  const isEdit = Boolean(price);

  useEffect(() => {
    if (price) {
      setFormData({
        contract_id: price.contract_id,
        product_id: price.product_id,
        price: price.price,
        effective_date: price.effective_date,
        expiry_date: price.expiry_date || '',
        notes: price.notes || '',
      });
      setHasExpiryDate(Boolean(price.expiry_date));
    } else {
      setFormData({
        contract_id: 0,
        product_id: 0,
        price: 0,
        effective_date: new Date().toISOString().split('T')[0],
        expiry_date: '',
        notes: '',
      });
      setHasExpiryDate(false);
    }
    setErrors({});
  }, [price, open]);

  const handleChange = (field: keyof ContractPriceFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleExpiryDateToggle = (checked: boolean) => {
    setHasExpiryDate(checked);
    if (!checked) {
      setFormData(prev => ({
        ...prev,
        expiry_date: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.contract_id) {
      newErrors.contract_id = 'Vui lòng chọn hợp đồng';
    }

    if (!formData.product_id) {
      newErrors.product_id = 'Vui lòng chọn sản phẩm';
    }

    if (!formData.price || formData.price <= 0) {
      newErrors.price = 'Đơn giá phải lớn hơn 0';
    }

    if (!formData.effective_date) {
      newErrors.effective_date = 'Vui lòng chọn ngày hiệu lực';
    }

    if (hasExpiryDate && formData.expiry_date) {
      const effectiveDate = new Date(formData.effective_date);
      const expiryDate = new Date(formData.expiry_date);

      if (expiryDate <= effectiveDate) {
        newErrors.expiry_date = 'Ngày hết hạn phải sau ngày hiệu lực';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    const submitData = {
      ...formData,
      expiry_date: hasExpiryDate ? formData.expiry_date : undefined,
    };

    onSubmit(submitData);
  };

  const selectedContract = contracts.find(c => c.id === formData.contract_id);
  const selectedProduct = products.find(p => p.id === formData.product_id);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: 500 }
        }}
      >
        <DialogTitle>
          {isEdit ? 'Cập nhật đơn giá' : 'Thêm đơn giá mới'}
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mt: 1 }}>
            {error && (
              <Typography color="error" sx={{ mb: 2 }}>
                {error}
              </Typography>
            )}

            <Grid container spacing={3}>
              {/* Hợp đồng */}
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={contracts}
                  getOptionLabel={(option) => `${option.contract_number} - ${option.contract_name}`}
                  value={selectedContract || null}
                  onChange={(_, value) => handleChange('contract_id', value?.id || 0)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Hợp đồng"
                      error={Boolean(errors.contract_id)}
                      helperText={errors.contract_id}
                      required
                    />
                  )}
                  disabled={isEdit}
                />
              </Grid>

              {/* Sản phẩm */}
              <Grid item xs={12} md={6}>
                <Autocomplete
                  options={products}
                  getOptionLabel={(option) => `${option.code} - ${option.name}`}
                  value={selectedProduct || null}
                  onChange={(_, value) => handleChange('product_id', value?.id || 0)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Sản phẩm"
                      error={Boolean(errors.product_id)}
                      helperText={errors.product_id}
                      required
                    />
                  )}
                  disabled={isEdit}
                />
              </Grid>

              {/* Đơn giá */}
              <Grid item xs={12} md={6}>
                <TextField
                  label="Đơn giá"
                  type="number"
                  value={formData.price}
                  onChange={(e) => handleChange('price', parseFloat(e.target.value) || 0)}
                  error={Boolean(errors.price)}
                  helperText={errors.price}
                  required
                  fullWidth
                  InputProps={{
                    endAdornment: <InputAdornment position="end">VNĐ</InputAdornment>,
                  }}
                />
                {formData.price > 0 && (
                  <Typography variant="caption" color="textSecondary">
                    {formatCurrency(formData.price)}
                  </Typography>
                )}
              </Grid>

              {/* Ngày hiệu lực */}
              <Grid item xs={12} md={6}>
                <DatePicker
                  label="Ngày hiệu lực"
                  value={convertISOStringToDate(formData.effective_date)}
                  onChange={(date) => handleChange('effective_date', convertDateToISOString(date))}
                  format="dd/MM/yyyy"
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      error: Boolean(errors.effective_date),
                      helperText: errors.effective_date,
                      required: true,
                      placeholder: 'dd/mm/yyyy'
                    },
                  }}
                />
              </Grid>

              {/* Có ngày hết hạn */}
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={hasExpiryDate}
                      onChange={(e) => handleExpiryDateToggle(e.target.checked)}
                    />
                  }
                  label="Có ngày hết hạn"
                />
              </Grid>

              {/* Ngày hết hạn */}
              {hasExpiryDate && (
                <Grid item xs={12} md={6}>
                  <DatePicker
                    label="Ngày hết hạn"
                    value={convertISOStringToDate(formData.expiry_date)}
                    onChange={(date) => handleChange('expiry_date', convertDateToISOString(date))}
                    format="dd/MM/yyyy"
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: Boolean(errors.expiry_date),
                        helperText: errors.expiry_date,
                        placeholder: 'dd/mm/yyyy'
                      },
                    }}
                  />
                </Grid>
              )}

              {/* Ghi chú */}
              <Grid item xs={12}>
                <TextField
                  label="Ghi chú"
                  multiline
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => handleChange('notes', e.target.value)}
                  fullWidth
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading}>
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Đang xử lý...' : (isEdit ? 'Cập nhật' : 'Thêm mới')}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default PricingDialog;
