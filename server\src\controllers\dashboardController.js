const dashboardService = require('../services/dashboardService');
const { successResponse, errorResponse } = require('../utils/responseUtils');

/**
 * Dashboard Controller
 * Xử lý các API endpoints cho dashboard
 */

/**
 * <PERSON><PERSON><PERSON> thống kê tổng quan hệ thống
 * GET /api/dashboard/stats
 */
const getGeneralStats = async (req, res) => {
  try {
    const stats = await dashboardService.getGeneralStats();
    return successResponse(res, stats, 'Lấy thống kê tổng quan thành công');
  } catch (error) {
    console.error('Error in dashboardController.getGeneralStats:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy sản lượng hôm nay
 * GET /api/dashboard/production-today
 */
const getTodayProduction = async (req, res) => {
  try {
    const production = await dashboardService.getTodayProduction();
    return successResponse(res, production, '<PERSON><PERSON>y sản lượng hôm nay thành công');
  } catch (error) {
    console.error('Error in dashboardController.getTodayProduction:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy top sản phẩm
 * GET /api/dashboard/top-products
 * Query params: period (today, week, month)
 */
const getTopProducts = async (req, res) => {
  try {
    const { period = 'today' } = req.query;
    
    // Validate period parameter
    const validPeriods = ['today', 'week', 'month'];
    if (!validPeriods.includes(period)) {
      return errorResponse(res, 'Tham số period không hợp lệ. Chỉ chấp nhận: today, week, month', 400);
    }
    
    const products = await dashboardService.getTopProducts(period);
    return successResponse(res, {
      period,
      products
    }, `Lấy top sản phẩm ${period} thành công`);
  } catch (error) {
    console.error('Error in dashboardController.getTopProducts:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy tóm tắt tình hình công nợ
 * GET /api/dashboard/debt-summary
 */
const getDebtSummary = async (req, res) => {
  try {
    const debtSummary = await dashboardService.getDebtSummary();
    return successResponse(res, debtSummary, 'Lấy tóm tắt công nợ thành công');
  } catch (error) {
    console.error('Error in dashboardController.getDebtSummary:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy top khách hàng nợ quá hạn
 * GET /api/dashboard/top-overdue-customers
 */
const getTopOverdueCustomers = async (req, res) => {
  try {
    const customers = await dashboardService.getTopOverdueCustomers();
    return successResponse(res, customers, 'Lấy top khách hàng nợ quá hạn thành công');
  } catch (error) {
    console.error('Error in dashboardController.getTopOverdueCustomers:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy thống kê sản lượng theo tháng
 * GET /api/dashboard/monthly-production
 * Query params: month (1-12), year (YYYY)
 */
const getMonthlyProductionStats = async (req, res) => {
  try {
    const { month, year } = req.query;

    // Validate month parameter
    if (month && (isNaN(month) || month < 1 || month > 12)) {
      return errorResponse(res, 'Tham số month phải là số từ 1 đến 12', 400);
    }

    // Validate year parameter
    if (year && (isNaN(year) || year < 2000 || year > 2100)) {
      return errorResponse(res, 'Tham số year phải là số từ 2000 đến 2100', 400);
    }

    const monthlyStats = await dashboardService.getMonthlyProductionStats(
      month ? parseInt(month) : null,
      year ? parseInt(year) : null
    );

    return successResponse(res, monthlyStats, `Lấy thống kê sản lượng tháng ${monthlyStats.month}/${monthlyStats.year} thành công`);
  } catch (error) {
    console.error('Error in dashboardController.getMonthlyProductionStats:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Lấy tất cả dữ liệu dashboard
 * GET /api/dashboard/all
 */
const getAllDashboardData = async (req, res) => {
  try {
    const dashboardData = await dashboardService.getAllDashboardData();
    return successResponse(res, dashboardData, 'Lấy tất cả dữ liệu dashboard thành công');
  } catch (error) {
    console.error('Error in dashboardController.getAllDashboardData:', error);
    return errorResponse(res, error.message, 500);
  }
};

/**
 * Middleware để validate query parameters chung
 */
const validateCommonParams = (req, res, next) => {
  // Có thể thêm validation chung ở đây nếu cần
  next();
};

module.exports = {
  getGeneralStats,
  getTodayProduction,
  getTopProducts,
  getDebtSummary,
  getTopOverdueCustomers,
  getMonthlyProductionStats,
  getAllDashboardData,
  validateCommonParams
};
