import React from 'react';
import {
  Box,
  Pagination as MuiPagination,
  FormControl,
  Select,
  MenuItem,
  Typography,
  SelectChangeEvent,
} from '@mui/material';

/**
 * Pagination Props Interface
 */
export interface PaginationProps {
  page: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
  pageSizeOptions?: number[];
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  showPageSizeSelector?: boolean;
  showInfo?: boolean;
  size?: 'small' | 'medium' | 'large';
}

/**
 * Pagination Component
 * Enhanced pagination component with page size selector and info display
 */
const Pagination: React.FC<PaginationProps> = ({
  page,
  totalPages,
  totalCount,
  pageSize,
  pageSizeOptions = [5, 10, 25, 50, 100],
  onPageChange,
  onPageSizeChange,
  showPageSizeSelector = true,
  showInfo = true,
  size = 'medium',
}) => {
  /**
   * Handle page change
   */
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    onPageChange(value);
  };

  /**
   * Handle page size change
   */
  const handlePageSizeChange = (event: SelectChangeEvent<number>) => {
    const newPageSize = event.target.value as number;
    onPageSizeChange(newPageSize);
  };

  /**
   * Calculate display range
   */
  const getDisplayRange = () => {
    const start = (page - 1) * pageSize + 1;
    const end = Math.min(page * pageSize, totalCount);
    return { start, end };
  };

  const { start, end } = getDisplayRange();

  if (totalCount === 0) {
    return null;
  }

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: 2,
        py: 2,
        px: 1,
      }}
    >
      {/* Left side - Page size selector and info */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        {showPageSizeSelector && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" sx={{ fontSize: '0.85rem', color: 'text.secondary' }}>
              Hiển thị:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 70 }}>
              <Select
                value={pageSize}
                onChange={handlePageSizeChange}
                sx={{
                  fontSize: '0.85rem',
                  '& .MuiSelect-select': {
                    py: 0.5,
                  },
                }}
              >
                {pageSizeOptions.map((option) => (
                  <MenuItem key={option} value={option} sx={{ fontSize: '0.85rem' }}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <Typography variant="body2" sx={{ fontSize: '0.85rem', color: 'text.secondary' }}>
              mục/trang
            </Typography>
          </Box>
        )}

        {showInfo && (
          <Typography variant="body2" sx={{ fontSize: '0.85rem', color: 'text.secondary' }}>
            Hiển thị {start}-{end} của {totalCount.toLocaleString()} mục
          </Typography>
        )}
      </Box>

      {/* Right side - Pagination controls */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        {totalPages > 1 && (
          <>
            <Typography variant="body2" sx={{ fontSize: '0.85rem', color: 'text.secondary' }}>
              Trang {page} / {totalPages}
            </Typography>
            <MuiPagination
              count={totalPages}
              page={page}
              onChange={handlePageChange}
              size={size}
              shape="rounded"
              showFirstButton
              showLastButton
              sx={{
                '& .MuiPaginationItem-root': {
                  fontSize: '0.85rem',
                  minWidth: size === 'small' ? 28 : 32,
                  height: size === 'small' ? 28 : 32,
                },
              }}
            />
          </>
        )}
      </Box>
    </Box>
  );
};

export default Pagination;
