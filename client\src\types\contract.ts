/**
 * Contract Type Definitions
 */

/**
 * Contract Interface
 */
export interface Contract {
  id: number;
  contract_number: string;
  customer_id: number;
  contract_name: string;
  start_date: string;
  end_date?: string | null;
  status: ContractStatus;
  notes?: string | null;
  short_name?: string | null;
  created_at: string;
  updated_at: string;
  created_by_name?: string;
  // Joined fields from customer
  customer_name?: string;
  customer_short_name?: string;
  customer_tax_code?: string;
}

/**
 * Contract Status Types
 */
export type ContractStatus = 'active' | 'paused' | 'terminated' | 'expired';

/**
 * Contract Status Options for UI
 */
export const CONTRACT_STATUS_OPTIONS: Array<{
  value: ContractStatus;
  label: string;
  color: 'success' | 'warning' | 'error' | 'default';
}> = [
  { value: 'active', label: 'Đang hoạt động', color: 'success' },
  { value: 'paused', label: 'Tạm dừng', color: 'warning' },
  { value: 'terminated', label: 'Đã chấm dứt', color: 'error' },
  { value: 'expired', label: 'Đã hết hạn', color: 'default' },
];

/**
 * Contract Create Request Interface
 */
export interface ContractCreateRequest {
  contract_number: string;
  customer_id: number;
  contract_name: string;
  start_date: string;
  end_date?: string;
  status?: ContractStatus;
  notes?: string;
}

/**
 * Contract Update Request Interface
 */
export interface ContractUpdateRequest {
  customer_id: number;
  contract_name: string;
  start_date: string;
  end_date?: string;
  status?: ContractStatus;
  notes?: string;
}

/**
 * Contract Search/Filter Options Interface
 */
export interface ContractFilterOptions {
  page?: number;
  limit?: number;
  search?: string;
  customer_id?: number;
  status?: ContractStatus;
  start_date_from?: string;
  start_date_to?: string;
  end_date_from?: string;
  end_date_to?: string;
  sortBy?: ContractSortField;
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Contract Sort Fields
 */
export type ContractSortField = 
  | 'id' 
  | 'contract_number' 
  | 'contract_name' 
  | 'start_date' 
  | 'end_date' 
  | 'status' 
  | 'created_at' 
  | 'updated_at';

/**
 * Contract Sort Options for UI
 */
export const CONTRACT_SORT_OPTIONS: Array<{
  value: ContractSortField;
  label: string;
}> = [
  { value: 'contract_number', label: 'Số hợp đồng' },
  { value: 'contract_name', label: 'Tên hợp đồng' },
  { value: 'start_date', label: 'Ngày bắt đầu' },
  { value: 'end_date', label: 'Ngày kết thúc' },
  { value: 'status', label: 'Trạng thái' },
  { value: 'created_at', label: 'Ngày tạo' },
];

/**
 * Contract Form Data Interface (for forms)
 */
export interface ContractFormData {
  contract_number: string;
  customer_id: number | '';
  contract_name: string;
  start_date: string;
  end_date: string;
  status: ContractStatus;
  notes: string;
}

/**
 * Contract Validation Error Interface
 */
export interface ContractValidationError {
  field: keyof ContractFormData;
  message: string;
}

/**
 * Contract Validation Result Interface
 */
export interface ContractValidationResult {
  isValid: boolean;
  errors: ContractValidationError[];
}

/**
 * Contract Table Column Interface
 */
export interface ContractTableColumn {
  id: keyof Contract | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

/**
 * Contract Table Columns Configuration
 */
export const CONTRACT_TABLE_COLUMNS: ContractTableColumn[] = [
  {
    id: 'contract_number',
    label: 'Số hợp đồng',
    minWidth: 140,
    sortable: true,
  },
  {
    id: 'contract_name',
    label: 'Tên hợp đồng',
    minWidth: 200,
    sortable: true,
  },
  {
    id: 'customer_name',
    label: 'Khách hàng',
    minWidth: 180,
    sortable: false,
  },
  {
    id: 'start_date',
    label: 'Ngày bắt đầu',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'end_date',
    label: 'Ngày kết thúc',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string | null) => 
      value ? new Date(value).toLocaleDateString('vi-VN') : 'Không giới hạn',
  },
  {
    id: 'status',
    label: 'Trạng thái',
    minWidth: 120,
    align: 'center',
    sortable: true,
  },
  {
    id: 'created_at',
    label: 'Ngày tạo',
    minWidth: 120,
    align: 'center',
    sortable: true,
    format: (value: string) => new Date(value).toLocaleDateString('vi-VN'),
  },
  {
    id: 'actions',
    label: 'Thao tác',
    minWidth: 120,
    align: 'center',
    sortable: false,
  },
];

/**
 * Contract Detail Interface (with additional info)
 */
export interface ContractDetail extends Contract {
  customer: {
    id: number;
    name: string;
    short_name: string;
    tax_code: string;
    address: string;
    contact_person: string;
    contact_phone: string;
    contact_email: string;
  };
  prices_count?: number;
  production_count?: number;
  total_production_amount?: number;
  last_production_date?: string;
}

/**
 * Contract Summary Interface
 */
export interface ContractSummary {
  total_contracts: number;
  active_contracts: number;
  paused_contracts: number;
  terminated_contracts: number;
  expired_contracts: number;
  contracts_expiring_soon: number; // trong 30 ngày tới
}

/**
 * Contract Error Types
 */
export type ContractErrorType = 
  | 'CONTRACT_NOT_FOUND'
  | 'CONTRACT_NUMBER_EXISTS'
  | 'CUSTOMER_NOT_FOUND'
  | 'INVALID_DATE_RANGE'
  | 'CONTRACT_HAS_DEPENDENCIES'
  | 'VALIDATION_ERROR'
  | 'NETWORK_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Contract Error Interface
 */
export interface ContractError {
  type: ContractErrorType;
  message: string;
  details?: string[];
}

/**
 * Contract Action Types for State Management
 */
export type ContractActionType =
  | 'FETCH_CONTRACTS_START'
  | 'FETCH_CONTRACTS_SUCCESS'
  | 'FETCH_CONTRACTS_FAILURE'
  | 'FETCH_CONTRACT_DETAIL_START'
  | 'FETCH_CONTRACT_DETAIL_SUCCESS'
  | 'FETCH_CONTRACT_DETAIL_FAILURE'
  | 'CREATE_CONTRACT_START'
  | 'CREATE_CONTRACT_SUCCESS'
  | 'CREATE_CONTRACT_FAILURE'
  | 'UPDATE_CONTRACT_START'
  | 'UPDATE_CONTRACT_SUCCESS'
  | 'UPDATE_CONTRACT_FAILURE'
  | 'DELETE_CONTRACT_START'
  | 'DELETE_CONTRACT_SUCCESS'
  | 'DELETE_CONTRACT_FAILURE'
  | 'SET_SELECTED_CONTRACT'
  | 'CLEAR_SELECTED_CONTRACT'
  | 'CLEAR_ERROR';

/**
 * Contract State Interface for Context/Redux
 */
export interface ContractState {
  contracts: Contract[];
  selectedContract: ContractDetail | null;
  loading: boolean;
  error: ContractError | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  summary: ContractSummary | null;
}

/**
 * Contract Context Type
 */
export interface ContractContextType {
  state: ContractState;
  fetchContracts: (options?: ContractFilterOptions) => Promise<void>;
  fetchContractDetail: (id: number) => Promise<void>;
  createContract: (data: ContractCreateRequest) => Promise<Contract>;
  updateContract: (id: number, data: ContractUpdateRequest) => Promise<Contract>;
  deleteContract: (id: number) => Promise<void>;
  setSelectedContract: (contract: ContractDetail | null) => void;
  clearError: () => void;
}

/**
 * Contract Hook Return Type
 */
export interface UseContractReturn extends ContractContextType {
  // Additional computed properties
  activeContracts: Contract[];
  expiredContracts: Contract[];
  contractsExpiringSoon: Contract[];
}

/**
 * Contract Export Data Interface
 */
export interface ContractExportData {
  contract_number: string;
  contract_name: string;
  customer_name: string;
  start_date: string;
  end_date: string;
  status: string;
  notes: string;
  created_date: string;
}

/**
 * Contract Search Result Interface
 */
export interface ContractSearchResult {
  id: number;
  contract_number: string;
  contract_name: string;
  customer_name: string;
  status: ContractStatus;
}

/**
 * Contract Status Change Request Interface
 */
export interface ContractStatusChangeRequest {
  status: ContractStatus;
  notes?: string;
}
