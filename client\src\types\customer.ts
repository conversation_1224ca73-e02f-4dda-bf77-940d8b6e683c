/**
 * Customer Type Definitions
 */

/**
 * Customer Interface
 */
export interface Customer {
  id: number;
  tax_code?: string | null;
  name: string;
  short_name?: string | null;
  address?: string | null;
  contact_person?: string | null;
  phone?: string | null;
  email?: string | null;
  is_active?: boolean;
  created_by?: number | null;
  created_by_name?: string | null;
  created_at?: string;
  updated_at?: string;
}

/**
 * Customer Form Data Interface
 */
export interface CustomerFormData {
  tax_code?: string;
  name: string;
  short_name?: string;
  address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
}

/**
 * Customer Create Request Interface
 */
export interface CustomerCreateRequest extends CustomerFormData {}

/**
 * Customer Update Request Interface
 */
export interface CustomerUpdateRequest extends Partial<CustomerFormData> {
  name: string; // name is still required for updates
}

/**
 * Customer Query Parameters Interface
 */
export interface CustomerParams {
  page?: number;
  limit?: number;
  search?: string;
  sort?: CustomerSortField;
  order?: SortOrder;
}

/**
 * Customer Sort Fields
 */
export type CustomerSortField = 
  | 'id'
  | 'name'
  | 'short_name'
  | 'tax_code'
  | 'created_at'
  | 'updated_at';

/**
 * Sort Order
 */
export type SortOrder = 'ASC' | 'DESC' | 'asc' | 'desc';

/**
 * Customer Result Interface
 */
export interface CustomerResult {
  customers: Customer[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Customer Table Column Interface
 */
export interface CustomerTableColumn {
  id: keyof Customer | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  sortable?: boolean;
  format?: (value: any) => string;
}

/**
 * Customer Filter Interface
 */
export interface CustomerFilter {
  search?: string;
  isActive?: boolean;
  hasEmail?: boolean;
  hasPhone?: boolean;
  hasTaxCode?: boolean;
  createdDateFrom?: string;
  createdDateTo?: string;
}

/**
 * Customer Validation Error Interface
 */
export interface CustomerValidationError {
  field: keyof CustomerFormData;
  message: string;
}

/**
 * Customer Validation Result Interface
 */
export interface CustomerValidationResult {
  isValid: boolean;
  errors: CustomerValidationError[];
}

/**
 * Customer Export Options Interface
 */
export interface CustomerExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  includeInactive?: boolean;
  fields?: (keyof Customer)[];
  filename?: string;
}

/**
 * Customer Import Result Interface
 */
export interface CustomerImportResult {
  success: boolean;
  totalRows: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    row: number;
    field?: string;
    message: string;
  }>;
  importedCustomers?: Customer[];
}

/**
 * Customer Statistics Interface
 */
export interface CustomerStatistics {
  total: number;
  active: number;
  inactive: number;
  withEmail: number;
  withPhone: number;
  withTaxCode: number;
  recentlyAdded: number; // Added in last 30 days
}

/**
 * Customer Search Suggestion Interface
 */
export interface CustomerSearchSuggestion {
  id: number;
  name: string;
  tax_code?: string;
  type: 'name' | 'tax_code' | 'short_name';
}

/**
 * Customer Dialog Mode
 */
export type CustomerDialogMode = 'create' | 'edit' | 'view';

/**
 * Customer Action Type
 */
export type CustomerActionType = 'view' | 'edit' | 'delete' | 'export';

/**
 * Customer Table Action Interface
 */
export interface CustomerTableAction {
  type: CustomerActionType;
  label: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  disabled?: boolean;
  onClick: (customer: Customer) => void;
}

/**
 * Customer Form Field Configuration Interface
 */
export interface CustomerFormFieldConfig {
  name: keyof CustomerFormData;
  label: string;
  type: 'text' | 'email' | 'tel' | 'textarea';
  required?: boolean;
  maxLength?: number;
  placeholder?: string;
  helperText?: string;
  multiline?: boolean;
  rows?: number;
  validation?: (value: string) => string | null;
}

/**
 * Default Customer Form Field Configurations
 */
export const CUSTOMER_FORM_FIELDS: CustomerFormFieldConfig[] = [
  {
    name: 'name',
    label: 'Tên khách hàng',
    type: 'text',
    required: true,
    maxLength: 200,
    placeholder: 'Nhập tên khách hàng',
  },
  {
    name: 'tax_code',
    label: 'Mã số thuế',
    type: 'text',
    required: true,
    maxLength: 20,
    placeholder: 'Nhập mã số thuế',
  },
  {
    name: 'short_name',
    label: 'Tên viết tắt',
    type: 'text',
    required: true,
    maxLength: 100,
    placeholder: 'Nhập tên viết tắt',
  },
  {
    name: 'address',
    label: 'Địa chỉ',
    type: 'textarea',
    required: true,
    maxLength: 1000,
    placeholder: 'Nhập địa chỉ',
    multiline: true,
    rows: 2,
  },
  {
    name: 'contact_person',
    label: 'Người liên hệ',
    type: 'text',
    maxLength: 100,
    placeholder: 'Nhập tên người liên hệ',
  },
  {
    name: 'phone',
    label: 'Số điện thoại',
    type: 'tel',
    maxLength: 20,
    placeholder: 'Nhập số điện thoại',
  },
  {
    name: 'email',
    label: 'Email',
    type: 'email',
    maxLength: 100,
    placeholder: 'Nhập email',
  },
];

/**
 * Customer Table Columns Configuration
 */
export const CUSTOMER_TABLE_COLUMNS: CustomerTableColumn[] = [
  {
    id: 'id',
    label: 'ID',
    minWidth: 70,
    align: 'center',
    sortable: true,
  },
  {
    id: 'tax_code',
    label: 'Mã số thuế',
    minWidth: 120,
    sortable: true,
  },
  {
    id: 'name',
    label: 'Tên khách hàng',
    minWidth: 200,
    sortable: true,
  },
  {
    id: 'short_name',
    label: 'Tên viết tắt',
    minWidth: 150,
    sortable: true,
  },
  {
    id: 'contact_person',
    label: 'Người liên hệ',
    minWidth: 150,
  },
  {
    id: 'phone',
    label: 'Số điện thoại',
    minWidth: 130,
  },
  {
    id: 'email',
    label: 'Email',
    minWidth: 180,
  },
  {
    id: 'created_at',
    label: 'Ngày tạo',
    minWidth: 120,
    sortable: true,
    format: (value: string) => value ? new Date(value).toLocaleDateString('vi-VN') : '',
  },
  {
    id: 'actions',
    label: 'Thao tác',
    minWidth: 120,
    align: 'center',
  },
];
