/**
 * QuickProductionForm Component
 * Form nhập nhanh sản lượng theo ngày - T<PERSON>o lại hoàn toàn
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Grid,
  Typography,
  IconButton,
  InputAdornment,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Autocomplete,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { vi } from 'date-fns/locale';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  MonetizationOn as MoneyIcon
} from '@mui/icons-material';
import { formatCurrencyVN, handleCurrencyInputVN, convertDateToISOString, convertISOStringToDate } from '../../utils/vietnameseFormatters';

// Types
import { Contract } from '../../types/contract';
import { Product } from '../../types/product';
import { Customer } from '../../types/customer';

// Services
import { contractService } from '../../services/contractService';
import { productService } from '../../services/productService';
import { customerService } from '../../services/customerService';
import { contractPriceService } from '../../services/contractPriceService';

// Utils
import { formatPriceInput, handlePriceInputChange } from '../../utils/formatters';

// Interfaces
interface ProductionItem {
  product_id: number;
  quantity: number;
  unit_price: number;
  total_amount: number;
}

export interface QuickProductionFormData {
  contract_id: number;
  production_date: string;
  notes: string;
  items: ProductionItem[];
}

interface QuickProductionFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: QuickProductionFormData) => Promise<void>;
  loading?: boolean;
  error?: string | null;
  prefilledContractId?: number; // Từ bảng hợp đồng
  prefilledCustomerId?: number; // Từ bảng hợp đồng
}

interface ProductWithPrice {
  id: number;
  code: string;
  name: string;
  unit_type: string;
  current_price: number;
}

// Utility functions - DEPRECATED: Use convertDateToISOString instead
const formatDateForStorage = (date: Date): string => {
  return convertDateToISOString(date);
};

const initialFormData: QuickProductionFormData = {
  contract_id: 0,
  production_date: formatDateForStorage(new Date()),
  notes: '',
  items: [{
    product_id: 0,
    quantity: 1,
    unit_price: 0,
    total_amount: 0
  }]
};

const QuickProductionForm: React.FC<QuickProductionFormProps> = ({
  open,
  onClose,
  onSubmit,
  loading = false,
  error = null,
  prefilledContractId,
  prefilledCustomerId
}) => {
  console.log('QuickProductionForm props:', {
    open,
    prefilledContractId,
    prefilledCustomerId
  });

  // State
  const [formData, setFormData] = useState<QuickProductionFormData>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [productsWithPrices, setProductsWithPrices] = useState<ProductWithPrice[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [loadingData, setLoadingData] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);

  // State for price display formatting
  const [priceDisplayValues, setPriceDisplayValues] = useState<Record<number, string>>({});

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      if (!open) return;

      console.log('Loading initial data...');
      setLoadingData(true);

      try {
        // Load customers nếu không có prefilled customer
        if (!prefilledCustomerId) {
          const customersResponse = await customerService.getAll({
            limit: 1000,
            is_active: true
          });
          console.log('Customers response:', customersResponse);
          if (customersResponse.success) {
            setCustomers(customersResponse.data || []);
          }
        }

        // Load contracts nếu có prefilled customer
        if (prefilledCustomerId) {
          setSelectedCustomerId(prefilledCustomerId);
          const contractsResponse = await contractService.getAll({
            customer_id: prefilledCustomerId,
            status: 'active',
            limit: 1000
          });
          console.log('Contracts response:', contractsResponse);
          if (contractsResponse.success) {
            setContracts(contractsResponse.data || []);
          }
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setLoadingData(false);
      }
    };

    loadInitialData();
  }, [open, prefilledCustomerId]);

  // Initialize form với prefilled data
  useEffect(() => {
    if (open && prefilledContractId) {
      console.log('Initializing form with prefilled contract:', prefilledContractId);

      setFormData(prev => ({
        ...prev,
        contract_id: prefilledContractId
      }));

      // Load contract details và tự động thêm sản phẩm
      loadContractDetailsAndAutoAddProducts(prefilledContractId);
    } else if (open) {
      // Reset form
      setFormData(initialFormData);
      setFormErrors({});
      setSelectedContract(null);
      setProductsWithPrices([]);
    }
  }, [open, prefilledContractId]);

  // Load contract details và products with prices
  const loadContractDetails = async (contractId: number) => {
    console.log('Loading contract details for:', contractId);

    try {
      // Load contract info
      const contractResponse = await contractService.getById(contractId);
      console.log('Contract details response:', contractResponse);

      if (contractResponse.success) {
        setSelectedContract(contractResponse.data);
      }

      // Load products with prices for this contract
      await loadProductsWithPrices(contractId);

    } catch (error) {
      console.error('Error loading contract details:', error);
    }
  };

  // Load contract details và tự động thêm tất cả sản phẩm có giá
  const loadContractDetailsAndAutoAddProducts = async (contractId: number) => {
    console.log('Loading contract details and auto-adding products for:', contractId);

    try {
      // Load contract info
      const contractResponse = await contractService.getById(contractId);
      console.log('Contract details response:', contractResponse);

      if (contractResponse.success) {
        setSelectedContract(contractResponse.data);
      }

      // Load products with prices for this contract
      const productsWithPrices = await loadProductsWithPricesAndAutoAdd(contractId);

    } catch (error) {
      console.error('Error loading contract details and auto-adding products:', error);
    }
  };

  // Load products có giá cho contract
  const loadProductsWithPrices = async (contractId: number) => {
    console.log('Loading products with prices for contract:', contractId);
    setLoadingProducts(true);

    try {
      // Lấy danh sách giá active cho contract, sắp xếp theo ID tăng dần (sản phẩm set giá trước hiển thị trước)
      const pricesResponse = await contractPriceService.getAll({
        contract_id: contractId,
        is_active: true,
        limit: 1000,
        sortBy: 'id',
        sortOrder: 'ASC'
      });

      console.log('Contract prices response:', pricesResponse);

      if (pricesResponse.success && pricesResponse.data) {
        // Lấy tất cả products
        const productsResponse = await productService.getAll({
          limit: 1000,
          is_active: true
        });

        console.log('Products response:', productsResponse);

        if (productsResponse.success && productsResponse.data) {
          // Map products với prices
          const productsWithPricesData: ProductWithPrice[] = [];

          pricesResponse.data.forEach((price: any) => {
            const product = productsResponse.data.find((p: Product) => p.id === price.product_id);
            if (product) {
              productsWithPricesData.push({
                id: product.id,
                code: product.code,
                name: product.name,
                unit_type: product.unit_type,
                current_price: price.price
              });
            }
          });

          console.log('Products with prices:', productsWithPricesData);
          setProductsWithPrices(productsWithPricesData);
        }
      } else {
        console.log('No active prices found for contract');
        setProductsWithPrices([]);
      }
    } catch (error) {
      console.error('Error loading products with prices:', error);
      setProductsWithPrices([]);
    } finally {
      setLoadingProducts(false);
    }
  };

  // Load products có giá cho contract và tự động thêm vào form
  const loadProductsWithPricesAndAutoAdd = async (contractId: number): Promise<ProductWithPrice[]> => {
    console.log('Loading products with prices and auto-adding for contract:', contractId);
    setLoadingProducts(true);

    try {
      // Lấy danh sách giá active cho contract, sắp xếp theo ID tăng dần (sản phẩm set giá trước hiển thị trước)
      const pricesResponse = await contractPriceService.getAll({
        contract_id: contractId,
        is_active: true,
        limit: 1000,
        sortBy: 'id',
        sortOrder: 'ASC'
      });

      console.log('Contract prices response:', pricesResponse);

      if (pricesResponse.success && pricesResponse.data) {
        // Lấy tất cả products
        const productsResponse = await productService.getAll({
          limit: 1000,
          is_active: true
        });

        console.log('Products response:', productsResponse);

        if (productsResponse.success && productsResponse.data) {
          // Map products với prices
          const productsWithPricesData: ProductWithPrice[] = [];

          pricesResponse.data.forEach((price: any) => {
            const product = productsResponse.data.find((p: Product) => p.id === price.product_id);
            if (product) {
              productsWithPricesData.push({
                id: product.id,
                code: product.code,
                name: product.name,
                unit_type: product.unit_type,
                current_price: price.price
              });
            }
          });

          console.log('Products with prices:', productsWithPricesData);
          setProductsWithPrices(productsWithPricesData);

          // Tự động thêm tất cả sản phẩm có giá vào form items
          if (productsWithPricesData.length > 0) {
            const autoItems: ProductionItem[] = productsWithPricesData.map(product => ({
              product_id: product.id,
              quantity: 0, // Mặc định số lượng = 0 (người dùng sẽ nhập số lượng thực tế)
              unit_price: product.current_price,
              total_amount: 0 // Thành tiền = 0 vì số lượng = 0
            }));

            console.log('Auto-adding items:', autoItems);
            setFormData(prev => ({
              ...prev,
              items: autoItems
            }));

            // Khởi tạo display values cho price inputs
            const initialDisplayValues: Record<number, string> = {};
            autoItems.forEach((item, index) => {
              initialDisplayValues[index] = formatCurrencyVN(item.unit_price);
            });
            setPriceDisplayValues(initialDisplayValues);
          }

          return productsWithPricesData;
        }
      } else {
        console.log('No active prices found for contract');
        setProductsWithPrices([]);
        return [];
      }
    } catch (error) {
      console.error('Error loading products with prices and auto-adding:', error);
      setProductsWithPrices([]);
      return [];
    } finally {
      setLoadingProducts(false);
    }

    return [];
  };

  // Handle customer change
  const handleCustomerChange = async (customerId: number) => {
    console.log('Customer changed to:', customerId);
    setSelectedCustomerId(customerId);
    setFormData(prev => ({ ...prev, contract_id: 0 }));
    setSelectedContract(null);
    setProductsWithPrices([]);

    if (customerId) {
      try {
        const response = await contractService.getAll({
          customer_id: customerId,
          status: 'active',
          limit: 1000
        });
        if (response.success) {
          setContracts(response.data || []);
        }
      } catch (error) {
        console.error('Error fetching contracts:', error);
        setContracts([]);
      }
    } else {
      setContracts([]);
    }
  };

  // Handle contract change
  const handleContractChange = async (contractId: number) => {
    console.log('Contract changed to:', contractId);
    setFormData(prev => ({ ...prev, contract_id: contractId }));

    if (formErrors.contract_id) {
      setFormErrors(prev => ({ ...prev, contract_id: undefined }));
    }

    if (contractId) {
      await loadContractDetails(contractId);
    } else {
      setSelectedContract(null);
      setProductsWithPrices([]);
    }
  };

  // Handle product change
  const handleProductChange = (index: number, productId: number) => {
    console.log('Product changed:', { index, productId });

    const product = productsWithPrices.find(p => p.id === productId);
    console.log('Selected product:', product);

    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      product_id: productId,
      unit_price: product ? product.current_price : 0,
      total_amount: product ? newItems[index].quantity * product.current_price : 0
    };

    setFormData(prev => ({ ...prev, items: newItems }));

    // Cập nhật display value cho price
    setPriceDisplayValues(prev => ({
      ...prev,
      [index]: product ? formatPriceInput(product.current_price) : ''
    }));

    // Clear error
    if (formErrors[`items[${index}].product_id`]) {
      setFormErrors(prev => ({ ...prev, [`items[${index}].product_id`]: undefined }));
    }
  };

  // Handle quantity change
  const handleQuantityChange = (index: number, quantity: number) => {
    console.log('Quantity changed:', { index, quantity });

    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      quantity,
      total_amount: quantity * newItems[index].unit_price
    };

    setFormData(prev => ({ ...prev, items: newItems }));

    // Clear error
    if (formErrors[`items[${index}].quantity`]) {
      setFormErrors(prev => ({ ...prev, [`items[${index}].quantity`]: undefined }));
    }
  };

  // Handle price change with formatting
  const handlePriceChange = (index: number, inputValue: string) => {
    console.log('Price changed:', { index, inputValue });

    // Sử dụng utility function để format và parse
    const { displayValue, numericValue } = handleCurrencyInputVN(inputValue);

    // Cập nhật display value
    setPriceDisplayValues(prev => ({
      ...prev,
      [index]: displayValue
    }));

    // Cập nhật numeric value trong form data
    const newItems = [...formData.items];
    newItems[index] = {
      ...newItems[index],
      unit_price: numericValue,
      total_amount: newItems[index].quantity * numericValue
    };

    setFormData(prev => ({ ...prev, items: newItems }));

    // Clear error
    if (formErrors[`items[${index}].unit_price`]) {
      setFormErrors(prev => ({ ...prev, [`items[${index}].unit_price`]: undefined }));
    }
  };



  // Add new item
  const handleAddItem = () => {
    console.log('Adding new item');
    const newItem: ProductionItem = {
      product_id: 0,
      quantity: 0, // Mặc định số lượng = 0
      unit_price: 0,
      total_amount: 0
    };
    const newIndex = formData.items.length;
    setFormData(prev => ({ ...prev, items: [...prev.items, newItem] }));

    // Khởi tạo display value cho price input
    setPriceDisplayValues(prev => ({
      ...prev,
      [newIndex]: ''
    }));
  };

  // Remove item
  const handleRemoveItem = (index: number) => {
    console.log('Removing item at index:', index);
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, items: newItems }));

      // Cập nhật lại display values sau khi xóa item
      const newDisplayValues: Record<number, string> = {};
      newItems.forEach((item, newIndex) => {
        const oldIndex = formData.items.findIndex((oldItem, oldIdx) =>
          oldIdx < index ? oldIdx === newIndex : oldIdx === newIndex + 1
        );
        if (oldIndex !== -1 && priceDisplayValues[oldIndex]) {
          newDisplayValues[newIndex] = priceDisplayValues[oldIndex];
        } else {
          newDisplayValues[newIndex] = formatCurrencyVN(item.unit_price);
        }
      });
      setPriceDisplayValues(newDisplayValues);
    }
  };

  // Calculate total - chỉ tính sản phẩm có số lượng > 0
  const calculateTotal = (): number => {
    return formData.items
      .filter(item => item.quantity > 0) // Chỉ tính sản phẩm có số lượng > 0
      .reduce((total, item) => total + item.total_amount, 0);
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.contract_id || formData.contract_id === 0) {
      errors.contract_id = 'Hợp đồng là bắt buộc';
    }

    if (!formData.production_date) {
      errors.production_date = 'Ngày sản xuất là bắt buộc';
    }

    // Validate production date is not in future
    const productionDate = new Date(formData.production_date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    if (productionDate > today) {
      errors.production_date = 'Ngày sản xuất không được trong tương lai';
    }

    // Validate items - chỉ validate những item có số lượng > 0
    formData.items.forEach((item, index) => {
      if (!item.product_id || item.product_id === 0) {
        errors[`items[${index}].product_id`] = 'Sản phẩm là bắt buộc';
      }

      // Chỉ validate số lượng và đơn giá nếu số lượng > 0
      if (item.quantity > 0) {
        if (!item.unit_price || item.unit_price <= 0) {
          errors[`items[${index}].unit_price`] = 'Đơn giá phải lớn hơn 0';
        }
      }

      // Validate số lượng không được âm
      if (item.quantity < 0) {
        errors[`items[${index}].quantity`] = 'Số lượng không được âm';
      }
    });

    if (formData.items.length === 0) {
      errors.items = 'Phải có ít nhất một sản phẩm';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle submit
  const handleSubmit = async () => {
    console.log('Form submit triggered');
    console.log('Form data before validation:', formData);

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    console.log('Form validation passed, submitting...');

    // Lọc bỏ các sản phẩm có số lượng = 0 (không có phát sinh)
    const itemsToSubmit = formData.items.filter(item => item.quantity > 0);

    console.log('Submitting data:', {
      contract_id: formData.contract_id,
      production_date: formData.production_date,
      notes: formData.notes,
      items: itemsToSubmit.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_amount: item.total_amount
      }))
    });

    // Kiểm tra có ít nhất 1 sản phẩm có số lượng > 0
    if (itemsToSubmit.length === 0) {
      console.log('No items with quantity > 0 to submit');
      setFormErrors({ items: 'Phải có ít nhất một sản phẩm có số lượng lớn hơn 0' });
      return;
    }

    setFormSubmitting(true);
    try {
      // Submit với data đã lọc
      const dataToSubmit = {
        ...formData,
        items: itemsToSubmit
      };
      await onSubmit(dataToSubmit);
      console.log('Form submitted successfully');
    } catch (error: any) {
      console.error('Form submission error:', error);
      console.error('Error details:', {
        message: error?.message,
        status: error?.status,
        code: error?.code,
        details: error?.details
      });
    } finally {
      setFormSubmitting(false);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { minHeight: '70vh' }
        }}
      >
        <DialogTitle>
          <Typography variant="h6" component="div">
            Nhập sản lượng nhanh
          </Typography>
          {selectedContract && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Nhập sản lượng cho hợp đồng {selectedContract.contract_number} - {selectedContract.contract_name}
            </Typography>
          )}
        </DialogTitle>

        <DialogContent>
          {error && (
            <Typography color="error" sx={{ mb: 2 }}>
              {error}
            </Typography>
          )}

          {loadingData && (
            <Typography color="info" sx={{ mb: 2 }}>
              Đang tải dữ liệu...
            </Typography>
          )}

          {/* Hướng dẫn nhanh khi mở từ hợp đồng */}
          {prefilledContractId && !loadingProducts && productsWithPrices.length > 0 && (
            <Box sx={{ mb: 3, p: 2, bgcolor: 'success.50', borderRadius: 1, border: '1px solid', borderColor: 'success.200' }}>
              <Typography variant="body2" color="success.dark" sx={{ fontWeight: 'medium', mb: 1 }}>
                🎉 Tất cả sản phẩm có giá đã được thêm tự động!
              </Typography>
              <Typography variant="body2" color="success.dark">
                Bạn chỉ cần: <strong>1)</strong> Chọn ngày sản xuất, <strong>2)</strong> Nhập số lượng cho từng sản phẩm, <strong>3)</strong> Nhấn Lưu.
              </Typography>
            </Box>
          )}

          <Grid container spacing={3}>
            {/* Ngày sản xuất - Ưu tiên hàng đầu */}
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Ngày sản xuất *"
                value={convertISOStringToDate(formData.production_date)}
                onChange={(date) => {
                  setFormData(prev => ({
                    ...prev,
                    production_date: convertDateToISOString(date)
                  }));
                  // Clear error when date is changed
                  if (formErrors.production_date) {
                    setFormErrors(prev => ({ ...prev, production_date: undefined }));
                  }
                }}
                format="dd/MM/yyyy"
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    size: "small",
                    error: !!formErrors.production_date,
                    helperText: formErrors.production_date,
                    autoFocus: !!prefilledContractId, // Auto focus khi mở từ hợp đồng
                    placeholder: 'dd/mm/yyyy',
                    sx: {
                      '& .MuiOutlinedInput-root': {
                        backgroundColor: prefilledContractId ? 'primary.50' : 'inherit',
                        '&:hover': {
                          backgroundColor: prefilledContractId ? 'primary.100' : 'inherit',
                        }
                      }
                    }
                  }
                }}
              />
            </Grid>

            {/* Customer Selection - Only show if not prefilled */}
            {!prefilledCustomerId && (
              <Grid item xs={12}>
                <FormControl fullWidth required size="small">
                  <InputLabel>Khách hàng</InputLabel>
                  <Select
                    value={selectedCustomerId || ''}
                    onChange={(e) => handleCustomerChange(e.target.value as number)}
                    label="Khách hàng"
                    disabled={loadingData}
                  >
                    <MenuItem value={0} disabled>
                      {loadingData ? 'Đang tải...' : 'Chọn khách hàng'}
                    </MenuItem>
                    {customers.map((customer) => (
                      <MenuItem key={customer.id} value={customer.id}>
                        {customer.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}

            {/* Contract Selection - Only show if not prefilled */}
            {!prefilledContractId && (
              <Grid item xs={12} md={6}>
                <FormControl
                  fullWidth
                  required
                  error={!!formErrors.contract_id}
                  size="small"
                  disabled={loadingData}
                >
                  <InputLabel id="contract-select-label">Hợp đồng</InputLabel>
                  <Select
                    labelId="contract-select-label"
                    value={formData.contract_id || ''}
                    onChange={(e) => handleContractChange(e.target.value as number)}
                    label="Hợp đồng"
                    disabled={loadingData}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                        },
                      },
                    }}
                  >
                    <MenuItem value={0} disabled>
                      {loadingData ? 'Đang tải...' : 'Chọn hợp đồng'}
                    </MenuItem>
                    {contracts.map((contract) => (
                      <MenuItem key={contract.id} value={contract.id}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', py: 0.5 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'medium', lineHeight: 1.2 }}>
                            {contract.contract_number}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ lineHeight: 1.2 }}>
                            {contract.contract_name}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  {formErrors.contract_id && <FormHelperText>{formErrors.contract_id}</FormHelperText>}
                </FormControl>
              </Grid>
            )}
          </Grid>

          <Box sx={{ mt: 3, mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Danh sách sản phẩm và sản lượng
              {prefilledContractId && productsWithPrices.length > 0 && (
                <Typography component="span" variant="body2" color="success.main" sx={{ ml: 1 }}>
                  ({productsWithPrices.length} sản phẩm đã được thêm tự động)
                </Typography>
              )}
            </Typography>
            <Divider />
          </Box>

          {loadingProducts && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, bgcolor: 'info.50', borderRadius: 1 }}>
              <Typography color="info.main">
                Đang tải danh sách sản phẩm có giá cho hợp đồng...
              </Typography>
            </Box>
          )}

          {!loadingProducts && productsWithPrices.length === 0 && formData.contract_id && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, p: 2, bgcolor: 'warning.50', borderRadius: 1 }}>
              <Typography color="warning.main">
                Hợp đồng này chưa có sản phẩm nào được thiết lập giá. Vui lòng thiết lập giá trước khi nhập sản lượng.
              </Typography>
            </Box>
          )}

          {formErrors.items && (
            <FormHelperText error sx={{ mb: 2 }}>{formErrors.items}</FormHelperText>
          )}

          {/* Product Items - Excel-like Table Layout */}
          <TableContainer component={Paper} sx={{ mb: 2, border: '1px solid', borderColor: 'grey.300' }}>
            <Table size="small" sx={{ minWidth: 650 }}>
              <TableHead>
                <TableRow sx={{ backgroundColor: 'grey.100' }}>
                  <TableCell sx={{ fontWeight: 'bold', minWidth: 300 }}>Sản phẩm</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: 120, textAlign: 'center' }}>Số lượng</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: 150, textAlign: 'center' }}>Đơn giá</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: 150, textAlign: 'center' }}>Thành tiền</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', width: 60, textAlign: 'center' }}>Xóa</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {formData.items.map((item, index) => (
                  <TableRow key={index} sx={{ '&:nth-of-type(odd)': { backgroundColor: 'grey.50' } }}>
                    <TableCell sx={{ p: 1 }}>
                      <Autocomplete
                        options={productsWithPrices}
                        getOptionLabel={(option) => `${option.code} - ${option.name}`}
                        value={productsWithPrices.find(p => p.id === item.product_id) || null}
                        onChange={(_, newValue) => {
                          handleProductChange(index, newValue ? newValue.id : 0);
                        }}
                        disabled={loadingProducts}
                        renderOption={(props, option) => (
                          <Box component="li" {...props}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {option.code} - {option.name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Đơn vị: {option.unit_type} | Giá: {formatCurrencyVN(option.current_price)} VND
                              </Typography>
                            </Box>
                          </Box>
                        )}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            required
                            size="small"
                            error={!!formErrors[`items[${index}].product_id`]}
                            placeholder={
                              loadingProducts ? 'Đang tải sản phẩm...' :
                              productsWithPrices.length === 0 ? 'Không có sản phẩm nào có giá' :
                              'Gõ để tìm sản phẩm...'
                            }
                            sx={{ '& .MuiOutlinedInput-root': { backgroundColor: 'white' } }}
                          />
                        )}
                        noOptionsText={
                          loadingProducts ? 'Đang tải sản phẩm...' :
                          productsWithPrices.length === 0 ? 'Không có sản phẩm nào có giá' :
                          'Không tìm thấy sản phẩm'
                        }
                        clearOnBlur
                        selectOnFocus
                        handleHomeEndKeys
                      />
                    </TableCell>

                    <TableCell sx={{ p: 1 }}>
                      <TextField
                        type="number"
                        value={item.quantity}
                        onChange={(e) => handleQuantityChange(index, parseFloat(e.target.value) || 0)}
                        fullWidth
                        required
                        error={!!formErrors[`items[${index}].quantity`]}
                        size="small"
                        InputProps={{
                          inputProps: { min: 0, step: 0.01 }
                        }}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#fff3cd', // Màu vàng nhạt giống Excel
                            '&:hover': {
                              backgroundColor: '#ffeaa7',
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffeaa7',
                            }
                          }
                        }}
                      />
                    </TableCell>

                    <TableCell sx={{ p: 1 }}>
                      <TextField
                        type="text"
                        value={priceDisplayValues[index] || ''}
                        onChange={(e) => handlePriceChange(index, e.target.value)}
                        fullWidth
                        required
                        error={!!formErrors[`items[${index}].unit_price`]}
                        size="small"
                        placeholder="Nhập số tiền..."
                        sx={{ '& .MuiOutlinedInput-root': { backgroundColor: 'white' } }}
                      />
                    </TableCell>

                    <TableCell sx={{ p: 1 }}>
                      <TextField
                        type="text"
                        value={formatCurrencyVN(item.total_amount)}
                        fullWidth
                        size="small"
                        InputProps={{
                          readOnly: true
                        }}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'grey.100',
                            '& input': {
                              fontWeight: 'medium'
                            }
                          }
                        }}
                      />
                    </TableCell>

                    <TableCell sx={{ p: 1, textAlign: 'center' }}>
                      <IconButton
                        color="error"
                        onClick={() => handleRemoveItem(index)}
                        disabled={formData.items.length <= 1}
                        size="small"
                        title={formData.items.length <= 1 ? 'Phải có ít nhất một sản phẩm' : 'Xóa sản phẩm này'}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Chỉ hiển thị nút thêm sản phẩm khi không phải auto-load từ hợp đồng hoặc khi cần thêm thủ công */}
          {(!prefilledContractId || (prefilledContractId && formData.items.length === 0)) && (
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<AddIcon />}
                onClick={handleAddItem}
                disabled={!formData.contract_id || productsWithPrices.length === 0 || loadingProducts}
              >
                {loadingProducts ? 'Đang tải...' : 'Thêm sản phẩm'}
              </Button>
            </Box>
          )}

          {/* Thông tin hướng dẫn khi có sản phẩm tự động */}
          {prefilledContractId && formData.items.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                💡 Mẹo: Bạn có thể xóa các sản phẩm không cần thiết bằng nút <DeleteIcon fontSize="small" sx={{ mx: 0.5, verticalAlign: 'middle' }} /> bên cạnh mỗi sản phẩm.
              </Typography>
            </Box>
          )}

          {/* Total Summary - chỉ hiển thị tổng của các sản phẩm có số lượng > 0 */}
          <Box sx={{ mt: 2, p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', textAlign: 'center' }}>
              Tổng cộng: {formatCurrencyVN(calculateTotal())} VND
            </Typography>
            {formData.items.filter(item => item.quantity > 0).length !== formData.items.length && (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 1 }}>
                (Chỉ tính {formData.items.filter(item => item.quantity > 0).length}/{formData.items.length} sản phẩm có số lượng)
              </Typography>
            )}
          </Box>

          {/* General Notes */}
          <Box sx={{ mt: 2 }}>
            <TextField
              label="Ghi chú chung"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              fullWidth
              multiline
              rows={2}
              size="small"
              placeholder="Ghi chú chung cho đợt sản xuất này..."
            />
          </Box>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={loading || formSubmitting || loadingData}>
            Hủy
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || formSubmitting || loadingData || loadingProducts}
          >
            {loading || formSubmitting ? 'Đang xử lý...' : 'Lưu sản lượng'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default QuickProductionForm;