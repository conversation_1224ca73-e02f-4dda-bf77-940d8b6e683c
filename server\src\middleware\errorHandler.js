/**
 * Error handling middleware
 * <PERSON><PERSON> lý tất cả các lỗi trong ứng dụng
 */

const { createErrorResponse } = require('../utils/responseUtils');

/**
 * Error handler middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  console.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Database errors
  if (err.code) {
    switch (err.code) {
      case '23505': // Unique violation
        return res.status(409).json(
          createErrorResponse(
            'DUPLICATE_ENTRY',
            'Dữ liệu đã tồn tại',
            ['<PERSON>i<PERSON> trị này đã được sử dụng, vui lòng chọn giá trị khác'],
            409
          )
        );
      
      case '23503': // Foreign key violation
        return res.status(400).json(
          createErrorResponse(
            'FOREIGN_KEY_VIOLATION',
            'Dữ liệu tham chiếu không hợp lệ',
            ['Dữ liệu bạn tham chiếu không tồn tại'],
            400
          )
        );
      
      case '23502': // Not null violation
        return res.status(400).json(
          createErrorResponse(
            'REQUIRED_FIELD_MISSING',
            'Thiếu dữ liệu bắt buộc',
            ['Vui lòng cung cấp đầy đủ thông tin bắt buộc'],
            400
          )
        );
      
      case '22001': // String data too long
        return res.status(400).json(
          createErrorResponse(
            'DATA_TOO_LONG',
            'Dữ liệu quá dài',
            ['Một hoặc nhiều trường dữ liệu vượt quá độ dài cho phép'],
            400
          )
        );
    }
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json(
      createErrorResponse(
        'INVALID_TOKEN',
        'Token không hợp lệ',
        ['Token đã bị thay đổi hoặc không đúng định dạng'],
        401
      )
    );
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json(
      createErrorResponse(
        'TOKEN_EXPIRED',
        'Token đã hết hạn',
        ['Vui lòng đăng nhập lại để lấy token mới'],
        401
      )
    );
  }

  // Validation errors
  if (err.name === 'ValidationError') {
    const details = Object.values(err.errors).map(error => error.message);
    return res.status(400).json(
      createErrorResponse(
        'VALIDATION_ERROR',
        'Dữ liệu không hợp lệ',
        details,
        400
      )
    );
  }

  // Express validator errors
  if (err.array && typeof err.array === 'function') {
    const details = err.array().map(error => `${error.param}: ${error.msg}`);
    return res.status(400).json(
      createErrorResponse(
        'VALIDATION_ERROR',
        'Dữ liệu không hợp lệ',
        details,
        400
      )
    );
  }

  // Custom application errors
  if (err.statusCode) {
    return res.status(err.statusCode).json(
      createErrorResponse(
        err.code || 'APPLICATION_ERROR',
        err.message,
        err.details || [],
        err.statusCode
      )
    );
  }

  // Default server error
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return res.status(500).json(
    createErrorResponse(
      'INTERNAL_SERVER_ERROR',
      'Lỗi máy chủ nội bộ',
      isDevelopment ? [err.message, err.stack] : ['Có lỗi xảy ra, vui lòng thử lại sau'],
      500
    )
  );
};

/**
 * Custom error classes
 */

class AppError extends Error {
  constructor(message, statusCode, code = null, details = []) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = []) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Không tìm thấy tài nguyên', details = []) {
    super(message, 404, 'NOT_FOUND', details);
  }
}

class UnauthorizedError extends AppError {
  constructor(message = 'Không có quyền truy cập', details = []) {
    super(message, 401, 'UNAUTHORIZED', details);
  }
}

class ForbiddenError extends AppError {
  constructor(message = 'Truy cập bị từ chối', details = []) {
    super(message, 403, 'FORBIDDEN', details);
  }
}

class ConflictError extends AppError {
  constructor(message = 'Xung đột dữ liệu', details = []) {
    super(message, 409, 'CONFLICT', details);
  }
}

/**
 * Async error wrapper
 * Wrap async functions để tự động catch errors
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404 handler
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(
    'Endpoint không tồn tại',
    [`Không tìm thấy endpoint: ${req.method} ${req.originalUrl}`]
  );
  next(error);
};

module.exports = {
  errorHandler,
  asyncHandler,
  notFoundHandler,
  AppError,
  ValidationError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ConflictError
};
