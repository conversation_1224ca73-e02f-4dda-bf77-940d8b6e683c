import api from './api';
import {
  AgingAnalysisItem,
  AgingAnalysisFilters,
  CustomerDebtSummary,
  OverallDebtSummary,
  ContractDebtSummary,
  PaymentHistoryDetail,
  PaymentHistoryFilters,
  DashboardData,
  ChartDataPoint
} from '../types/report';

/**
 * Report Service
 * Xử lý các API calls liên quan đến báo cáo và aging analysis
 */

const BASE_URL = '/reports';

export const reportService = {
  /**
   * Lấy báo cáo aging analysis
   */
  async getAgingAnalysis(filters: AgingAnalysisFilters = {}): Promise<AgingAnalysisItem[]> {
    try {
      const params = new URLSearchParams();
      
      if (filters.customerId) params.append('customerId', filters.customerId.toString());
      if (filters.asOfDate) params.append('asOfDate', filters.asOfDate);
      if (filters.includeZeroBalance !== undefined) {
        params.append('includeZeroBalance', filters.includeZeroBalance.toString());
      }

      const response = await api.get(`${BASE_URL}/aging-analysis?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching aging analysis:', error);
      throw error;
    }
  },

  /**
   * Export aging analysis ra CSV
   */
  async exportAgingAnalysisCSV(filters: AgingAnalysisFilters = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      
      if (filters.customerId) params.append('customerId', filters.customerId.toString());
      if (filters.asOfDate) params.append('asOfDate', filters.asOfDate);
      if (filters.includeZeroBalance !== undefined) {
        params.append('includeZeroBalance', filters.includeZeroBalance.toString());
      }

      const response = await api.get(`${BASE_URL}/aging-analysis/export?${params.toString()}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting aging analysis:', error);
      throw error;
    }
  },

  /**
   * Lấy tổng hợp công nợ theo khách hàng
   */
  async getCustomerDebtSummary(customerId: number): Promise<CustomerDebtSummary> {
    try {
      const response = await api.get(`${BASE_URL}/customer-debt/${customerId}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching customer debt summary:', error);
      throw error;
    }
  },

  /**
   * Lấy tổng hợp công nợ tổng thể
   */
  async getOverallDebtSummary(): Promise<OverallDebtSummary> {
    try {
      const response = await api.get(`${BASE_URL}/debt-summary`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching overall debt summary:', error);
      throw error;
    }
  },

  /**
   * Lấy báo cáo công nợ theo hợp đồng
   */
  async getContractDebtSummary(filters: { contractId?: number; customerId?: number } = {}): Promise<ContractDebtSummary[]> {
    try {
      const params = new URLSearchParams();
      
      if (filters.contractId) params.append('contractId', filters.contractId.toString());
      if (filters.customerId) params.append('customerId', filters.customerId.toString());

      const response = await api.get(`${BASE_URL}/contract-debt?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching contract debt summary:', error);
      throw error;
    }
  },

  /**
   * Lấy lịch sử thanh toán chi tiết
   */
  async getPaymentHistoryDetail(filters: PaymentHistoryFilters = {}): Promise<PaymentHistoryDetail[]> {
    try {
      const params = new URLSearchParams();
      
      if (filters.customerId) params.append('customerId', filters.customerId.toString());
      if (filters.paymentId) params.append('paymentId', filters.paymentId.toString());
      if (filters.startDate) params.append('startDate', filters.startDate);
      if (filters.endDate) params.append('endDate', filters.endDate);
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response = await api.get(`${BASE_URL}/payment-history?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  },

  /**
   * Lấy dữ liệu dashboard
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      const response = await api.get(`${BASE_URL}/dashboard`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  },

  /**
   * Format aging analysis data cho display
   */
  formatAgingAnalysisForDisplay(items: AgingAnalysisItem[]) {
    return items.map(item => ({
      ...item,
      total_invoiced_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.total_invoiced),
      total_payments_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.total_payments),
      total_outstanding_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.total_outstanding),
      current_amount_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.current_amount),
      days_1_30_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.days_1_30),
      days_31_60_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.days_31_60),
      days_61_90_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.days_61_90),
      over_90_days_formatted: new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(item.over_90_days)
    }));
  },

  /**
   * Tạo chart data từ aging analysis
   */
  createAgingChartData(summary: OverallDebtSummary): ChartDataPoint[] {
    const total = parseFloat(summary.total_outstanding.toString());
    
    if (total === 0) return [];

    return [
      {
        name: 'Chưa đến hạn',
        value: parseFloat(summary.total_current.toString()),
        percentage: parseFloat(summary.current_percentage),
        color: '#4caf50'
      },
      {
        name: '1-30 ngày',
        value: parseFloat(summary.total_1_30.toString()),
        percentage: parseFloat(summary.days_1_30_percentage),
        color: '#ff9800'
      },
      {
        name: '31-60 ngày',
        value: parseFloat(summary.total_31_60.toString()),
        percentage: parseFloat(summary.days_31_60_percentage),
        color: '#f44336'
      },
      {
        name: '61-90 ngày',
        value: parseFloat(summary.total_61_90.toString()),
        percentage: parseFloat(summary.days_61_90_percentage),
        color: '#9c27b0'
      },
      {
        name: 'Trên 90 ngày',
        value: parseFloat(summary.total_over_90.toString()),
        percentage: parseFloat(summary.over_90_days_percentage),
        color: '#607d8b'
      }
    ].filter(item => item.value > 0);
  },

  /**
   * Download CSV file
   */
  downloadCSV(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  /**
   * Format currency cho display
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  },

  /**
   * Format percentage cho display
   */
  formatPercentage(percentage: number | string): string {
    const numValue = typeof percentage === 'string' ? parseFloat(percentage) : percentage;
    if (isNaN(numValue)) return '0.00%';
    return `${numValue.toFixed(2)}%`;
  }
};

export default reportService;
