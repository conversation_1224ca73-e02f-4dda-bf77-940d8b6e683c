# PowerShell script de backup va xoa du lieu
# Chay script nay de thuc hien toan bo quy trinh

Write-Host "=== SCRIPT XOA DU LIEU DE CHUAN BI PRODUCTION ===" -ForegroundColor Green
Write-Host ""

# Thiet lap bien moi truong
$env:PGPASSWORD = "110591"
$env:PGCLIENTENCODING = "UTF8"

# Duong dan database
$dbHost = "localhost"
$dbPort = "5432"
$dbName = "tinhtam-hp"
$dbUser = "postgres"

Write-Host "Thong tin ket noi:" -ForegroundColor Yellow
Write-Host "- Host: $dbHost"
Write-Host "- Port: $dbPort"
Write-Host "- Database: $dbName"
Write-Host "- User: $dbUser"
Write-Host ""

# Kiem tra ket noi database
Write-Host "Buoc 1: Kiem tra ket noi database..." -ForegroundColor Cyan
try {
    $testConnection = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "SELECT current_database();" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Ket noi database thanh cong!" -ForegroundColor Green
    } else {
        Write-Host "Loi ket noi database:" -ForegroundColor Red
        Write-Host $testConnection
        exit 1
    }
} catch {
    Write-Host "Khong the ket noi database: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Xac nhan tu nguoi dung
Write-Host "CANH BAO: Script nay se XOA TAT CA DU LIEU TEST/DEMO!" -ForegroundColor Red
Write-Host "Chi giu lai:" -ForegroundColor Yellow
Write-Host "- Cau truc bang va rang buoc"
Write-Host "- Tai khoan admin de dang nhap"
Write-Host "- Views, functions, triggers"
Write-Host ""

$confirmation = Read-Host "Ban co chac chan muon tiep tuc? (yes/no)"
if ($confirmation -ne "yes") {
    Write-Host "Huy bo thuc hien." -ForegroundColor Red
    exit 0
}

Write-Host ""

# Buoc 2: Backup du lieu
Write-Host "Buoc 2: Backup du lieu hien tai..." -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/backup_before_clear.sql"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Backup du lieu thanh cong!" -ForegroundColor Green
    } else {
        Write-Host "Loi backup du lieu!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Khong the backup du lieu: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Buoc 3: Xoa du lieu
Write-Host "Buoc 3: Xoa du lieu test/demo..." -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/clear_data_for_production.sql"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Xoa du lieu thanh cong!" -ForegroundColor Green
    } else {
        Write-Host "Loi xoa du lieu!" -ForegroundColor Red
        
        # Hoi co muon khoi phuc khong
        $restore = Read-Host "Co muon khoi phuc du lieu tu backup? (yes/no)"
        if ($restore -eq "yes") {
            Write-Host "Dang khoi phuc du lieu..." -ForegroundColor Yellow
            psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -f "database/restore_backup.sql"
        }
        exit 1
    }
} catch {
    Write-Host "Khong the xoa du lieu: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""

# Buoc 4: Kiem tra ket qua
Write-Host "Buoc 4: Kiem tra ket qua..." -ForegroundColor Cyan
try {
    psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "SELECT 'users' as table_name, COUNT(*) as records FROM users UNION ALL SELECT 'customers' as table_name, COUNT(*) as records FROM customers UNION ALL SELECT 'products' as table_name, COUNT(*) as records FROM products UNION ALL SELECT 'contracts' as table_name, COUNT(*) as records FROM contracts;"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Thong ke du lieu sau khi xoa hien thi o tren." -ForegroundColor Green
    }
} catch {
    Write-Host "Khong the kiem tra ket qua, nhung qua trinh xoa da hoan thanh." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "HOAN THANH!" -ForegroundColor Green
Write-Host ""
Write-Host "Ket qua:" -ForegroundColor Yellow
Write-Host "- Du lieu test/demo da duoc xoa"
Write-Host "- Cau truc database duoc giu nguyen"
Write-Host "- Tai khoan admin duoc giu lai"
Write-Host "- Backup duoc luu trong schema backup_data"
Write-Host ""
Write-Host "Thong tin dang nhap:" -ForegroundColor Cyan
Write-Host "- Email: <EMAIL>"
Write-Host "- Password: 123456"
Write-Host ""
Write-Host "Luu y: Nen doi password admin sau khi production!" -ForegroundColor Red
