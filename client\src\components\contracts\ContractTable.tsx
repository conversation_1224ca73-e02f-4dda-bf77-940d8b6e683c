/**
 * Contract Table Component
 * <PERSON>ển thị danh sách hợp đồng với các tính năng tìm kiếm, sắp xếp, phân trang
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  Toolbar,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Skeleton,
  FormControl,
  InputLabel,
  Select,
  Button,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  Upload as ImportIcon,
  Visibility as ViewIcon,
  Assignment as ContractIcon,
  Pause as PauseIcon,
  PlayArrow as ResumeIcon,
  Stop as TerminateIcon,
  AttachMoney as PriceIcon,
  Assessment as ProductionIcon,
  Speed as QuickIcon,
} from '@mui/icons-material';
import { visuallyHidden } from '@mui/utils';
import {
  Contract,
  ContractFilterOptions,
  CONTRACT_STATUS_OPTIONS,
  ContractStatus
} from '../../types/contract';
import ContractStatusIcon from './ContractStatusIcon';

interface ContractTableProps {
  contracts: Contract[];
  loading?: boolean;
  totalCount?: number;
  page?: number;
  rowsPerPage?: number;
  orderBy?: string;
  order?: 'asc' | 'desc';
  searchQuery?: string;
  filters?: ContractFilterOptions;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onSort?: (orderBy: string, order: 'asc' | 'desc') => void;
  onSearch?: (query: string) => void;
  onFilter?: (filters: ContractFilterOptions) => void;
  onAdd?: () => void;
  onEdit?: (contract: Contract) => void;
  onDelete?: (contract: Contract) => void;
  onView?: (contract: Contract) => void;
  onRefresh?: () => void;
  onExport?: () => void;
  onImportProduction?: () => void;
  onChangeStatus?: (contract: Contract, status: ContractStatus) => void;
  // Quick Actions
  onQuickPrice?: (contract: Contract) => void;
  onQuickProduction?: (contract: Contract) => void;
}

interface HeadCell {
  id: keyof Contract | 'short_name';
  label: string;
  numeric: boolean;
  sortable: boolean;
  width?: number;
}

const headCells: HeadCell[] = [
  { id: 'contract_number', label: 'Số hợp đồng', numeric: false, sortable: true, width: 140 },
  { id: 'contract_name', label: 'Tên hợp đồng', numeric: false, sortable: true, width: 200 },
  { id: 'customer_name', label: 'Khách hàng', numeric: false, sortable: false, width: 280 },
  { id: 'short_name', label: 'Tên viết tắt', numeric: false, sortable: false, width: 120 },
  { id: 'start_date', label: 'Ngày bắt đầu', numeric: false, sortable: true, width: 120 },
  { id: 'end_date', label: 'Ngày kết thúc', numeric: false, sortable: true, width: 120 },
  { id: 'status', label: 'Trạng thái', numeric: false, sortable: true, width: 80 },
];

const ContractTable: React.FC<ContractTableProps> = ({
  contracts,
  loading = false,
  totalCount = 0,
  page = 0,
  rowsPerPage = 10,
  orderBy = 'created_at',
  order = 'desc',
  searchQuery = '',
  filters = {},
  onPageChange,
  onRowsPerPageChange,
  onSort,
  onSearch,
  onFilter,
  onAdd,
  onEdit,
  onDelete,
  onView,
  onRefresh,
  onExport,
  onImportProduction,
  onChangeStatus,
  onQuickPrice,
  onQuickProduction,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const [statusFilter, setStatusFilter] = useState<ContractStatus | ''>('');

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearchQuery !== searchQuery) {
        onSearch?.(localSearchQuery);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [localSearchQuery, searchQuery, onSearch]);

  const handleRequestSort = useCallback((property: keyof Contract) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    onSort?.(property as string, newOrder);
  }, [orderBy, order, onSort]);

  const handleChangePage = useCallback((_: unknown, newPage: number) => {
    onPageChange?.(newPage);
  }, [onPageChange]);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange?.(parseInt(event.target.value, 10));
  }, [onRowsPerPageChange]);

  const handleMenuClick = useCallback((event: React.MouseEvent<HTMLElement>, contract: Contract) => {
    setAnchorEl(event.currentTarget);
    setSelectedContract(contract);
  }, []);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedContract(null);
  }, []);

  const handleStatusFilterChange = useCallback((status: ContractStatus | '') => {
    setStatusFilter(status);
    onFilter?.({ ...filters, status: status || undefined });
  }, [filters, onFilter]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Không giới hạn';
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getStatusInfo = (status: ContractStatus) => {
    return CONTRACT_STATUS_OPTIONS.find(opt => opt.value === status);
  };

  const renderStatusIcon = (status: ContractStatus) => {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <ContractStatusIcon status={status} size="medium" showTooltip={true} />
      </Box>
    );
  };

  const getStatusActions = (contract: Contract) => {
    const actions = [];

    switch (contract.status) {
      case 'active':
        actions.push(
          <MenuItem key="pause" onClick={() => { onChangeStatus?.(contract, 'paused'); handleMenuClose(); }}>
            <ListItemIcon><PauseIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Tạm dừng</ListItemText>
          </MenuItem>,
          <MenuItem key="terminate" onClick={() => { onChangeStatus?.(contract, 'terminated'); handleMenuClose(); }}>
            <ListItemIcon><TerminateIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Chấm dứt</ListItemText>
          </MenuItem>
        );
        break;
      case 'paused':
        actions.push(
          <MenuItem key="resume" onClick={() => { onChangeStatus?.(contract, 'active'); handleMenuClose(); }}>
            <ListItemIcon><ResumeIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Kích hoạt lại</ListItemText>
          </MenuItem>,
          <MenuItem key="terminate" onClick={() => { onChangeStatus?.(contract, 'terminated'); handleMenuClose(); }}>
            <ListItemIcon><TerminateIcon fontSize="small" /></ListItemIcon>
            <ListItemText>Chấm dứt</ListItemText>
          </MenuItem>
        );
        break;
    }

    return actions;
  };

  const renderSkeletonRows = () => {
    return Array.from(new Array(rowsPerPage)).map((_, index) => (
      <TableRow key={index}>
        {headCells.map((headCell) => (
          <TableCell key={headCell.id}>
            <Skeleton variant="text" width="80%" />
          </TableCell>
        ))}
        {(onQuickPrice || onQuickProduction) && (
          <TableCell>
            <Skeleton variant="circular" width={24} height={24} />
          </TableCell>
        )}
        <TableCell>
          <Skeleton variant="circular" width={24} height={24} />
        </TableCell>
      </TableRow>
    ));
  };

  return (
    <Paper sx={{ width: '100%', mb: 2 }}>
      <Toolbar
        sx={{
          pl: { sm: 2 },
          pr: { xs: 1, sm: 1 },
          minHeight: { xs: 56, sm: 64 },
        }}
      >
        <Typography
          sx={{ flex: '1 1 100%' }}
          variant="h6"
          id="tableTitle"
          component="div"
        >
          Danh sách hợp đồng
        </Typography>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TextField
            size="small"
            placeholder="Tìm kiếm hợp đồng, khách hàng..."
            value={localSearchQuery}
            onChange={(e) => setLocalSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ width: 250 }}
          />

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Trạng thái</InputLabel>
            <Select
              value={statusFilter}
              label="Trạng thái"
              onChange={(e) => handleStatusFilterChange(e.target.value as ContractStatus | '')}
            >
              <MenuItem value="">Tất cả</MenuItem>
              {CONTRACT_STATUS_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Tooltip title="Làm mới">
            <IconButton onClick={onRefresh} size="small">
              <RefreshIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Xuất Excel">
            <IconButton onClick={onExport} size="small">
              <ExportIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Import sản lượng từ Excel">
            <IconButton onClick={onImportProduction} size="small" color="secondary">
              <ImportIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Thêm hợp đồng">
            <IconButton onClick={onAdd} size="small" color="primary">
              <AddIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>



      <TableContainer>
        <Table
          sx={{ minWidth: 750 }}
          aria-labelledby="tableTitle"
          size="small"
        >
          <TableHead>
            <TableRow>
              {headCells.map((headCell) => (
                <TableCell
                  key={headCell.id}
                  align={headCell.numeric ? 'right' : 'left'}
                  padding="normal"
                  sortDirection={orderBy === headCell.id ? order : false}
                  sx={{ width: headCell.width }}
                >
                  {headCell.sortable ? (
                    <TableSortLabel
                      active={orderBy === headCell.id}
                      direction={orderBy === headCell.id ? order : 'asc'}
                      onClick={() => handleRequestSort(headCell.id)}
                    >
                      {headCell.label}
                      {orderBy === headCell.id ? (
                        <Box component="span" sx={visuallyHidden}>
                          {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                        </Box>
                      ) : null}
                    </TableSortLabel>
                  ) : (
                    headCell.label
                  )}
                </TableCell>
              ))}
              {(onQuickPrice || onQuickProduction) && (
                <TableCell align="center" sx={{ width: 140 }}>
                  Thao tác nhanh
                </TableCell>
              )}
              <TableCell align="center" sx={{ width: 80 }}>
                Thao tác
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              renderSkeletonRows()
            ) : (
              contracts.map((contract) => (
                <TableRow
                  hover
                  key={contract.id}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => onView?.(contract)}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight={500}>
                      {contract.contract_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {contract.contract_name}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {contract.customer_name || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" color="text.secondary">
                      {contract.short_name || contract.customer_short_name || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell>{formatDate(contract.start_date)}</TableCell>
                  <TableCell>{formatDate(contract.end_date)}</TableCell>
                  <TableCell align="center">{renderStatusIcon(contract.status)}</TableCell>
                  {(onQuickPrice || onQuickProduction) && (
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
                        {onQuickPrice && (
                          <Tooltip title="Cập nhật giá nhanh">
                            <IconButton
                              size="small"
                              color="primary"
                              onClick={(e) => {
                                e.stopPropagation();
                                onQuickPrice(contract);
                              }}
                            >
                              <PriceIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        {onQuickProduction && (
                          <Tooltip title="Nhập sản lượng nhanh">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={(e) => {
                                e.stopPropagation();
                                onQuickProduction(contract);
                              }}
                            >
                              <ProductionIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </TableCell>
                  )}
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuClick(e, contract);
                      }}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Số dòng mỗi trang:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}–${to} của ${count !== -1 ? count : `hơn ${to}`}`
        }
      />

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => { onView?.(selectedContract!); handleMenuClose(); }}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xem chi tiết</ListItemText>
        </MenuItem>

        {/* Quick Actions Section */}
        {(onQuickPrice || onQuickProduction) && (
          <>
            <MenuItem disabled sx={{ opacity: 0.6, fontWeight: 'bold', fontSize: '0.75rem' }}>
              <ListItemIcon>
                <QuickIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="THAO TÁC NHANH" />
            </MenuItem>

            {onQuickPrice && (
              <MenuItem onClick={() => { onQuickPrice(selectedContract!); handleMenuClose(); }}>
                <ListItemIcon>
                  <PriceIcon fontSize="small" color="primary" />
                </ListItemIcon>
                <ListItemText>
                  <Typography variant="body2" color="primary">Cập nhật giá nhanh</Typography>
                </ListItemText>
              </MenuItem>
            )}

            {onQuickProduction && (
              <MenuItem onClick={() => { onQuickProduction(selectedContract!); handleMenuClose(); }}>
                <ListItemIcon>
                  <ProductionIcon fontSize="small" color="success" />
                </ListItemIcon>
                <ListItemText>
                  <Typography variant="body2" color="success.main">Nhập sản lượng nhanh</Typography>
                </ListItemText>
              </MenuItem>
            )}
          </>
        )}

        <MenuItem onClick={() => { onEdit?.(selectedContract!); handleMenuClose(); }}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Chỉnh sửa</ListItemText>
        </MenuItem>
        {selectedContract && getStatusActions(selectedContract).map((action, index) => (
          <React.Fragment key={index}>{action}</React.Fragment>
        ))}
        <MenuItem onClick={() => { onDelete?.(selectedContract!); handleMenuClose(); }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Xóa</ListItemText>
        </MenuItem>
      </Menu>
    </Paper>
  );
};

export default ContractTable;
