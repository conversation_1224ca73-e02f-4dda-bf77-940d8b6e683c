/**
 * Contracts Page
 * Trang quản lý hợp đồng
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Container,
  Typography,
  Alert,
  Snackbar,
} from '@mui/material';
import { useSearchParams } from 'react-router-dom';
import ContractTable from '../components/contracts/ContractTable';
import ContractDialog from '../components/contracts/ContractDialog';
import ContractDetail from '../components/contracts/ContractDetail';
import ConfirmDialog from '../components/common/ConfirmDialog';
import QuickPriceForm from '../components/pricing/QuickPriceForm';
import QuickProductionForm from '../components/production/QuickProductionForm';
import ProductionImportDialog from '../components/production/ProductionImportDialog';
import { contractService } from '../services/contractService';
import { contractPriceService } from '../services/contractPriceService';
import { dailyProductionService } from '../services/dailyProductionService';
import { productService } from '../services/productService';
import {
  Contract,
  ContractDetail as ContractDetailType,
  ContractFilterOptions,
  ContractCreateRequest,
  ContractUpdateRequest,
  ContractStatus
} from '../types/contract';

const Contracts: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // State
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Pagination & Filtering
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [orderBy, setOrderBy] = useState('created_at');
  const [order, setOrder] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ContractFilterOptions>({});

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(null);
  const [selectedContractDetail, setSelectedContractDetail] = useState<ContractDetailType | null>(null);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [dialogError, setDialogError] = useState<string | null>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // Quick forms state
  const [quickPriceOpen, setQuickPriceOpen] = useState(false);
  const [quickProductionOpen, setQuickProductionOpen] = useState(false);
  const [quickFormLoading, setQuickFormLoading] = useState(false);
  const [quickFormError, setQuickFormError] = useState<string | null>(null);

  // Import dialog state
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [importLoading, setImportLoading] = useState(false);

  // Toast
  const [toast, setToast] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load contracts
  const loadContracts = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const options: ContractFilterOptions = {
        page: page + 1, // API sử dụng 1-based pagination
        limit: rowsPerPage,
        search: searchQuery || undefined,
        sortBy: orderBy as any,
        sortOrder: order.toUpperCase() as 'ASC' | 'DESC',
        ...filters,
      };

      const response = await contractService.getAll(options);

      if (response.success) {
        setContracts(response.data);
        setTotalCount(response.meta?.pagination?.total || 0);
      } else {
        throw new Error(response.message || 'Không thể tải danh sách hợp đồng');
      }
    } catch (err: any) {
      setError(err.message || 'Có lỗi xảy ra khi tải dữ liệu');
      setContracts([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, orderBy, order, searchQuery, filters]);

  // Load contract detail
  const loadContractDetail = useCallback(async (contractId: number) => {
    setDetailLoading(true);

    try {
      const response = await contractService.getById(contractId);

      if (response.success) {
        setSelectedContractDetail(response.data);
      } else {
        throw new Error(response.message || 'Không thể tải chi tiết hợp đồng');
      }
    } catch (err: any) {
      setToast({
        open: true,
        message: err.message || 'Có lỗi xảy ra khi tải chi tiết hợp đồng',
        severity: 'error',
      });
    } finally {
      setDetailLoading(false);
    }
  }, []);

  // Load contracts when dependencies change
  useEffect(() => {
    loadContracts();
  }, [loadContracts]);

  // URL sync
  useEffect(() => {
    const params = new URLSearchParams();
    if (page > 0) params.set('page', (page + 1).toString());
    if (rowsPerPage !== 10) params.set('limit', rowsPerPage.toString());
    if (searchQuery) params.set('search', searchQuery);
    if (orderBy !== 'created_at') params.set('sortBy', orderBy);
    if (order !== 'desc') params.set('order', order);

    setSearchParams(params);
  }, [page, rowsPerPage, searchQuery, orderBy, order, setSearchParams]);

  // Event handlers
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(0);
  }, []);

  const handleSort = useCallback((property: string, direction: 'asc' | 'desc') => {
    setOrderBy(property);
    setOrder(direction);
    setPage(0);
  }, []);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setPage(0);
  }, []);

  const handleFilter = useCallback((newFilters: ContractFilterOptions) => {
    setFilters(newFilters);
    setPage(0);
  }, []);

  const handleAdd = useCallback(() => {
    setSelectedContract(null);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleEdit = useCallback((contract: Contract) => {
    setSelectedContract(contract);
    setDialogError(null);
    setDialogOpen(true);
  }, []);

  const handleView = useCallback((contract: Contract) => {
    setSelectedContract(contract);
    setSelectedContractDetail(null);
    setDetailOpen(true);
    loadContractDetail(contract.id);
  }, [loadContractDetail]);

  const handleDelete = useCallback((contract: Contract) => {
    setSelectedContract(contract);
    setDeleteDialogOpen(true);
  }, []);

  const handleDialogClose = useCallback(() => {
    setDialogOpen(false);
    setSelectedContract(null);
    setDialogError(null);
  }, []);

  const handleDetailClose = useCallback(() => {
    setDetailOpen(false);
    setSelectedContract(null);
    setSelectedContractDetail(null);
  }, []);

  const handleChangeStatus = useCallback(async (contract: Contract, status: ContractStatus) => {
    try {
      const response = await contractService.updateStatus(contract.id, { status });
      if (response.success) {
        setToast({
          open: true,
          message: 'Cập nhật trạng thái hợp đồng thành công',
          severity: 'success',
        });
        loadContracts();
      } else {
        throw new Error(response.message || 'Không thể cập nhật trạng thái hợp đồng');
      }
    } catch (err: any) {
      setToast({
        open: true,
        message: err.message || 'Có lỗi xảy ra khi cập nhật trạng thái',
        severity: 'error',
      });
    }
  }, [loadContracts]);

  const handleDialogSubmit = useCallback(async (data: ContractCreateRequest | ContractUpdateRequest) => {
    setDialogLoading(true);
    setDialogError(null);

    try {
      if (selectedContract) {
        // Update
        const response = await contractService.update(selectedContract.id, data as ContractUpdateRequest);
        if (response.success) {
          setToast({
            open: true,
            message: 'Cập nhật hợp đồng thành công',
            severity: 'success',
          });
          handleDialogClose();
          loadContracts();
        } else {
          throw new Error(response.message || 'Không thể cập nhật hợp đồng');
        }
      } else {
        // Create
        const response = await contractService.create(data as ContractCreateRequest);
        if (response.success) {
          setToast({
            open: true,
            message: 'Thêm hợp đồng thành công',
            severity: 'success',
          });
          handleDialogClose();
          loadContracts();
        } else {
          throw new Error(response.message || 'Không thể thêm hợp đồng');
        }
      }
    } catch (err: any) {
      setDialogError(err.message || 'Có lỗi xảy ra');
    } finally {
      setDialogLoading(false);
    }
  }, [selectedContract, handleDialogClose, loadContracts]);

  const handleConfirmDelete = useCallback(async () => {
    if (!selectedContract) return;

    try {
      const response = await contractService.delete(selectedContract.id);
      if (response.success) {
        setToast({
          open: true,
          message: 'Xóa hợp đồng thành công',
          severity: 'success',
        });
        setDeleteDialogOpen(false);
        setSelectedContract(null);
        loadContracts();
      } else {
        throw new Error(response.message || 'Không thể xóa hợp đồng');
      }
    } catch (err: any) {
      setToast({
        open: true,
        message: err.message || 'Có lỗi xảy ra khi xóa hợp đồng',
        severity: 'error',
      });
    }
  }, [selectedContract, loadContracts]);

  const handleExport = useCallback(async () => {
    try {
      const blob = await contractService.exportToExcel(filters);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `contracts_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setToast({
        open: true,
        message: 'Xuất file Excel thành công',
        severity: 'success',
      });
    } catch (err: any) {
      setToast({
        open: true,
        message: 'Có lỗi xảy ra khi xuất file',
        severity: 'error',
      });
    }
  }, [filters]);

  const handleToastClose = useCallback(() => {
    setToast(prev => ({ ...prev, open: false }));
  }, []);

  // Quick action handlers
  const handleQuickPrice = useCallback((contract: Contract) => {
    setSelectedContract(contract);
    setQuickPriceOpen(true);
    setQuickFormError(null);
  }, []);

  const handleQuickProduction = useCallback((contract: Contract) => {
    setSelectedContract(contract);
    setQuickProductionOpen(true);
    setQuickFormError(null);
  }, []);

  const handleQuickPriceSubmit = useCallback(async (data: any) => {
    if (!selectedContract) return;

    setQuickFormLoading(true);
    setQuickFormError(null);

    try {
      // Create multiple price entries for each item
      const promises = data.items.map((item: any) =>
        contractPriceService.create({
          contract_id: data.contract_id,
          product_id: item.product_id,
          price: item.price,
          effective_date: data.effective_date,
          expiry_date: data.expiry_date || null,
          is_active: true
        })
      );

      await Promise.all(promises);

      setToast({
        open: true,
        message: `Cập nhật giá cho hợp đồng ${selectedContract.contract_number} thành công!`,
        severity: 'success',
      });
      setQuickPriceOpen(false);
      setSelectedContract(null);
      loadContracts(); // Refresh data
    } catch (error) {
      console.error('Error submitting quick price:', error);
      setQuickFormError('Có lỗi xảy ra khi cập nhật giá');
    } finally {
      setQuickFormLoading(false);
    }
  }, [selectedContract, loadContracts]);

  const handleQuickProductionSubmit = useCallback(async (data: any) => {
    if (!selectedContract) return;

    setQuickFormLoading(true);
    setQuickFormError(null);

    try {
      console.log('Quick production submit data:', data);

      // Create multiple production entries for each item
      const promises = data.items.map((item: any) =>
        dailyProductionService.create({
          contract_id: data.contract_id,
          product_id: item.product_id,
          production_date: data.production_date,
          quantity: item.quantity,
          unit_price: item.unit_price,
          notes: data.notes, // Chỉ sử dụng ghi chú chung
          auto_get_price: false // Đã có unit_price từ form
        })
      );

      const results = await Promise.all(promises);
      console.log('Quick production submit results:', results);

      setToast({
        open: true,
        message: `Nhập sản lượng cho hợp đồng ${selectedContract.contract_number} thành công!`,
        severity: 'success',
      });
      setQuickProductionOpen(false);
      setSelectedContract(null);
      loadContracts(); // Refresh data
    } catch (error) {
      console.error('Error submitting quick production:', error);
      setQuickFormError('Có lỗi xảy ra khi nhập sản lượng');
    } finally {
      setQuickFormLoading(false);
    }
  }, [selectedContract, loadContracts]);

  // Import production handler
  const handleImportProduction = useCallback(() => {
    setImportDialogOpen(true);
  }, []);

  const handleImportSubmit = useCallback(async (importData: any[]) => {
    setImportLoading(true);

    try {
      // Transform data to match API format
      const apiData = importData.map(row => ({
        contract_number: row.contract_number,
        production_date: row.production_date,
        product_code: row.product_code,
        quantity: row.quantity,
        notes: row.notes || ''
      }));

      // Call import API
      const response = await dailyProductionService.importFromExcel(apiData);

      if (response.success) {
        const { total, success, failed, errors } = response.data;

        if (failed > 0) {
          // Show detailed error information
          const errorMessages = errors.map(err =>
            `Dòng ${err.row}: ${err.error}`
          ).join('\n');

          setToast({
            open: true,
            message: `Import hoàn tất: ${success}/${total} thành công. Lỗi:\n${errorMessages}`,
            severity: 'warning',
          });
        } else {
          setToast({
            open: true,
            message: `Import thành công ${success}/${total} dòng dữ liệu sản lượng!`,
            severity: 'success',
          });
        }

        setImportDialogOpen(false);
        loadContracts(); // Refresh data
      } else {
        throw new Error(response.message || 'Import thất bại');
      }
    } catch (error: any) {
      console.error('Error importing production:', error);
      setToast({
        open: true,
        message: error.message || 'Có lỗi xảy ra khi import dữ liệu',
        severity: 'error',
      });
    } finally {
      setImportLoading(false);
    }
  }, [loadContracts]);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Quản lý hợp đồng
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Quản lý thông tin hợp đồng và khách hàng trong hệ thống
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <ContractTable
        contracts={contracts}
        loading={loading}
        totalCount={totalCount}
        page={page}
        rowsPerPage={rowsPerPage}
        orderBy={orderBy}
        order={order}
        searchQuery={searchQuery}
        filters={filters}
        onPageChange={handlePageChange}
        onRowsPerPageChange={handleRowsPerPageChange}
        onSort={handleSort}
        onSearch={handleSearch}
        onFilter={handleFilter}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        onRefresh={loadContracts}
        onExport={handleExport}
        onImportProduction={handleImportProduction}
        onChangeStatus={handleChangeStatus}
        onQuickPrice={handleQuickPrice}
        onQuickProduction={handleQuickProduction}
      />

      {/* Contract Dialog */}
      <ContractDialog
        open={dialogOpen}
        contract={selectedContract}
        loading={dialogLoading}
        error={dialogError}
        onClose={handleDialogClose}
        onSubmit={handleDialogSubmit}
      />

      {/* Contract Detail */}
      <ContractDetail
        open={detailOpen}
        contract={selectedContractDetail}
        loading={detailLoading}
        onClose={handleDetailClose}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />

      {/* Delete Confirmation */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Xác nhận xóa hợp đồng"
        message={`Bạn có chắc chắn muốn xóa hợp đồng "${selectedContract?.contract_number}"? Hành động này không thể hoàn tác.`}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteDialogOpen(false)}
        confirmText="Xóa"
        cancelText="Hủy"
        severity="error"
      />

      {/* Quick Forms */}
      {selectedContract && (
        <>
          <QuickPriceForm
            open={quickPriceOpen}
            onClose={() => {
              setQuickPriceOpen(false);
              setSelectedContract(null);
              setQuickFormError(null);
            }}
            onSubmit={handleQuickPriceSubmit}
            loading={quickFormLoading}
            error={quickFormError}
            prefilledContractId={selectedContract.id}
            prefilledCustomerId={selectedContract.customer_id}
            title="Cập nhật giá nhanh"
            subtitle={`Thiết lập giá cho hợp đồng ${selectedContract.contract_number}`}
          />

          <QuickProductionForm
            open={quickProductionOpen}
            onClose={() => {
              setQuickProductionOpen(false);
              setSelectedContract(null);
              setQuickFormError(null);
            }}
            onSubmit={handleQuickProductionSubmit}
            loading={quickFormLoading}
            error={quickFormError}
            prefilledContractId={selectedContract.id}
            prefilledCustomerId={selectedContract.customer_id}
          />
        </>
      )}

      {/* Import Dialog */}
      <ProductionImportDialog
        open={importDialogOpen}
        onClose={() => setImportDialogOpen(false)}
        onImport={handleImportSubmit}
        loading={importLoading}
      />

      {/* Toast */}
      <Snackbar
        open={toast.open}
        autoHideDuration={6000}
        onClose={handleToastClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleToastClose} severity={toast.severity}>
          {toast.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default Contracts;
