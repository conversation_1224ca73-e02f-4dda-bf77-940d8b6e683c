import React from 'react';
import { Card, CardContent, Typography, Box, alpha } from '@mui/material';

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

/**
 * StatCard Component with Fresh & Clean Theme
 * Displays statistics with modern design and laundry-appropriate colors
 */
const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, description }) => {
  return (
    <Card
      className="laundry-hover-lift laundry-focus-visible"
      sx={{
        height: '100%',
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 2,
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: `1px solid ${alpha(color, 0.1)}`,
        borderTop: `4px solid ${color}`,
        transition: 'all 0.3s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: `0 12px 24px ${alpha(color, 0.15)}`,
          borderTop: `4px solid ${color}`,
        }
      }}
    >
      {/* Background decorative circle */}
      <Box
        sx={{
          position: 'absolute',
          top: -30,
          right: -30,
          width: 120,
          height: 120,
          borderRadius: '50%',
          background: `linear-gradient(135deg, ${alpha(color, 0.1)} 0%, ${alpha(color, 0.05)} 100%)`,
          zIndex: 0,
        }}
      />
      
      {/* Smaller decorative circle */}
      <Box
        sx={{
          position: 'absolute',
          bottom: -20,
          left: -20,
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: `linear-gradient(135deg, ${alpha(color, 0.05)} 0%, transparent 100%)`,
          zIndex: 0,
        }}
      />

      <CardContent sx={{ position: 'relative', zIndex: 1, p: 3 }}>
        {/* Header with icon */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography 
            variant="subtitle2" 
            color="text.secondary" 
            sx={{ 
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: 0.5,
              fontSize: '0.75rem'
            }}
          >
            {title}
          </Typography>
          
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: '50%',
              background: `linear-gradient(135deg, ${color} 0%, ${alpha(color, 0.8)} 100%)`,
              color: 'white',
              boxShadow: `0 4px 12px ${alpha(color, 0.3)}`,
            }}
          >
            {icon}
          </Box>
        </Box>

        {/* Value */}
        <Typography 
          variant="h3" 
          component="div" 
          sx={{ 
            fontWeight: 700,
            color: 'text.primary',
            mb: 1,
            fontSize: { xs: '2rem', sm: '2.5rem' }
          }}
        >
          {value}
        </Typography>

        {/* Description */}
        <Typography 
          variant="body2" 
          color="text.secondary"
          sx={{ 
            fontWeight: 500,
            opacity: 0.8
          }}
        >
          {description}
        </Typography>

        {/* Bottom accent line */}
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: 3,
            background: `linear-gradient(90deg, ${color} 0%, ${alpha(color, 0.3)} 100%)`,
          }}
        />
      </CardContent>
    </Card>
  );
};

export default StatCard;
