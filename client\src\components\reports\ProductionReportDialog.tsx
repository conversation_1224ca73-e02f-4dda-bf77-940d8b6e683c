import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Divider,
  Grid
} from '@mui/material';
import { Download, Print } from '@mui/icons-material';
import * as XLSX from 'xlsx';
import * as XLSXStyle from 'xlsx-js-style';
import { dailyProductionService } from '../../services/dailyProductionService';
import { formatCurrency } from '../../utils/formatters';

interface ProductionReportDialogProps {
  open: boolean;
  onClose: () => void;
  year?: number;
  month?: number;
  startDate?: string;
  endDate?: string;
  contractId: number;
  status?: string;
  contractName?: string;
  customerName?: string;
}

interface ReportData {
  contract: {
    id: number;
    contract_number: string;
    contract_name: string;
    customer_name: string;
    customer_short_name: string;
  };
  year: number;
  month: number;
  products: Array<{
    id: number;
    code: string;
    name: string;
    unit_type: string;
    unit_price: string;
  }>;
  dailyData: Record<number, number[]>;
  productTotals: Record<number, { quantity: number; amount: number }>;
  summary: {
    total_before_vat: number;
    vat_amount: number;
    total_with_vat: number;
    total_paid: number;
    remaining_amount: number;
  };
}

const ProductionReportDialog: React.FC<ProductionReportDialogProps> = ({
  open,
  onClose,
  year,
  month,
  startDate,
  endDate,
  contractId,
  status = 'all',
  contractName,
  customerName
}) => {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Lấy số ngày trong tháng
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month, 0).getDate();
  };

  const daysInMonth = getDaysInMonth(year, month);

  useEffect(() => {
    if (open && contractId) {
      loadReportData();
    }
  }, [open, year, month, startDate, endDate, contractId, status]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await dailyProductionService.getMonthlyReport({
        year,
        month,
        contract_id: contractId,
        status: status !== 'all' ? status : undefined
      });

      if (response.success) {
        // API trả về dữ liệu trong data.report
        setReportData(response.data.report);
      } else {
        throw new Error(response.message || 'Không thể tải báo cáo');
      }
    } catch (error) {
      console.error('Error loading report:', error);
      setError(error instanceof Error ? error.message : 'Có lỗi xảy ra khi tải báo cáo');
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = () => {
    if (!reportData) return;

    // Tạo workbook mới
    const wb = XLSXStyle.utils.book_new();

    // Tạo dữ liệu cho worksheet
    const wsData: any[][] = [];

    // Thêm tiêu đề báo cáo
    wsData.push([`${reportData.contract?.contract_number || ''} - ${reportData.contract?.customer_name || ''}`]);
    const reportTitle = year && month
      ? `Báo cáo sản lượng tháng ${month}/${year}`
      : `Báo cáo sản lượng từ ${startDate} đến ${endDate}`;
    wsData.push([reportTitle]);
    wsData.push([]); // Dòng trống

    // Thêm header cho bảng
    const headers = [
      'TT', 'MẶT HÀNG', 'ĐVT', 'ĐƠN GIÁ'
    ];

    // Thêm các ngày trong tháng
    for (let i = 1; i <= 31; i++) {
      headers.push(i.toString());
    }

    // Thêm cột tổng
    headers.push('Số lượng');
    headers.push('Thành tiền');

    wsData.push(headers);

    // Tính tổng cho mỗi sản phẩm
    const totals: { [key: number]: { quantity: number; amount: number } } = {};
    (reportData.products || []).forEach(product => {
      const dailyValues = reportData.dailyData?.[product.id] || new Array(31).fill(0);
      const totalQuantity = dailyValues.reduce((sum: number, qty: number) => sum + qty, 0);
      const totalAmount = totalQuantity * parseFloat(product.unit_price);

      totals[product.id] = {
        quantity: totalQuantity,
        amount: totalAmount
      };
    });

    // Thêm dữ liệu sản phẩm
    (reportData.products || []).forEach((product, index) => {
      const row = [
        index + 1,
        product.name,
        product.unit_type,
        parseFloat(product.unit_price)
      ];

      // Thêm dữ liệu theo ngày
      for (let i = 0; i < 31; i++) {
        row.push(reportData.dailyData?.[product.id]?.[i] || 0);
      }

      // Thêm tổng số lượng và thành tiền
      row.push(totals[product.id]?.quantity);
      row.push(totals[product.id]?.amount);

      wsData.push(row);
    });

    // Tính tổng cộng
    const totalAmount = Object.values(totals).reduce((sum, item) => sum + item.amount, 0);
    const vatAmount = totalAmount * 0.08;
    const totalWithVat = totalAmount + vatAmount;

    // Thêm dòng tổng cộng
    const totalRow = [
      '', 'Tổng chưa gồm VAT', '', ''
    ];

    // Thêm các ô trống cho các ngày
    for (let i = 0; i < 31; i++) {
      totalRow.push('');
    }

    // Thêm tổng số lượng và thành tiền
    totalRow.push(Object.values(totals).reduce((sum, item) => sum + item.quantity, 0));
    totalRow.push(totalAmount);

    wsData.push(totalRow);

    // Thêm dòng VAT
    const vatRow = [
      '', 'VAT', '', ''
    ];

    // Thêm các ô trống cho các ngày
    for (let i = 0; i < 31; i++) {
      vatRow.push('');
    }

    // Thêm ô trống cho số lượng và thành tiền VAT
    vatRow.push('');
    vatRow.push(vatAmount);

    wsData.push(vatRow);

    // Thêm dòng tổng cộng có VAT
    const totalWithVatRow = [
      '', 'Tổng gồm VAT', '', ''
    ];

    // Thêm các ô trống cho các ngày
    for (let i = 0; i < 31; i++) {
      totalWithVatRow.push('');
    }

    // Thêm ô trống cho số lượng và tổng tiền có VAT
    totalWithVatRow.push('');
    totalWithVatRow.push(totalWithVat);

    wsData.push(totalWithVatRow);

    // Thêm dòng đã thanh toán
    const paidRow = [
      '', 'Đã thanh toán', '', ''
    ];

    // Thêm các ô trống cho các ngày
    for (let i = 0; i < 31; i++) {
      paidRow.push('');
    }

    // Thêm ô trống cho số lượng và số tiền đã thanh toán
    paidRow.push('');
    paidRow.push(totalWithVat);

    wsData.push(paidRow);

    // Thêm dòng còn phải thanh toán
    const remainingRow = [
      '', 'Còn phải thanh toán', '', ''
    ];

    // Thêm các ô trống cho các ngày
    for (let i = 0; i < 31; i++) {
      remainingRow.push('');
    }

    // Thêm ô trống cho số lượng và số tiền còn phải thanh toán
    remainingRow.push('');
    remainingRow.push(0);

    wsData.push(remainingRow);

    // Tạo worksheet từ dữ liệu
    const ws = XLSXStyle.utils.aoa_to_sheet(wsData);

    // Thiết lập định dạng cho các ô
    const range = XLSXStyle.utils.decode_range(ws['!ref'] || 'A1');

    // Thiết lập độ rộng cột
    const colWidths = [
      { wch: 5 },  // TT
      { wch: 20 }, // MẶT HÀNG
      { wch: 5 },  // ĐVT
      { wch: 10 }, // ĐƠN GIÁ
    ];

    // Thêm độ rộng cho các ngày
    for (let i = 0; i < 31; i++) {
      colWidths.push({ wch: 5 });
    }

    // Thêm độ rộng cho cột tổng
    colWidths.push({ wch: 10 }); // Số lượng
    colWidths.push({ wch: 15 }); // Thành tiền

    ws['!cols'] = colWidths;

    // Thêm đường viền cho các ô
    // Xác định số dòng dữ liệu sản phẩm (không bao gồm tiêu đề và tổng cộng)
    const headerRowIndex = 3; // Dòng header (0-based)
    const dataStartRowIndex = headerRowIndex + 1; // Dòng bắt đầu dữ liệu
    const dataEndRowIndex = dataStartRowIndex + (reportData.products || []).length - 1; // Dòng kết thúc dữ liệu
    const totalRowsStartIndex = dataEndRowIndex + 1; // Dòng bắt đầu phần tổng

    // Tạo style cho đường viền đầy đủ (cho phần dữ liệu chi tiết)
    const fullBorderStyle = {
      font: { name: 'Arial', sz: 10 },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
      }
    };

    // Tạo style cho đường viền ngang (cho phần tổng)
    const horizontalBorderStyle = {
      font: { name: 'Arial', sz: 10, bold: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'none' },
        right: { style: 'none' }
      }
    };

    // Tạo style cho header (đậm và có đường viền)
    const headerBorderStyle = {
      font: { name: 'Arial', sz: 10, bold: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
      },
      alignment: { horizontal: 'center', vertical: 'center', wrapText: true }
    };

    // Style cho các ô số lượng (căn giữa)
    const quantityCellStyle = {
      font: { name: 'Arial', sz: 10 },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
      },
      alignment: { horizontal: 'center' }
    };

    // Style cho các ô tiền tệ (căn phải)
    const moneyCellStyle = {
      font: { name: 'Arial', sz: 10 },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
      },
      alignment: { horizontal: 'right' },
      numFmt: '#,##0'
    };

    // Style cho các ô tổng tiền (căn phải, đậm)
    const totalMoneyCellStyle = {
      font: { name: 'Arial', sz: 10, bold: true },
      border: {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'none' },
        right: { style: 'none' }
      },
      alignment: { horizontal: 'right' },
      numFmt: '#,##0'
    };

    // Thêm đường viền cho tất cả các ô
    if (!ws['!rows']) ws['!rows'] = [];

    // Style cho tiêu đề báo cáo
    const titleStyle = {
      font: { name: 'Arial', bold: true, sz: 14 },
      alignment: { horizontal: 'left' }
    };

    const subtitleStyle = {
      font: { name: 'Arial', bold: true, sz: 12 },
      alignment: { horizontal: 'left' }
    };

    // Thêm style cho các ô
    for (let r = 0; r <= range.e.r; r++) {
      for (let c = 0; c <= range.e.c; c++) {
        const cellAddress = XLSXStyle.utils.encode_cell({ r, c });
        if (!ws[cellAddress]) continue;

        // Khởi tạo đối tượng style nếu chưa có
        if (!ws[cellAddress].s) ws[cellAddress].s = {};

        // Áp dụng style tùy theo vị trí của ô
        if (r === 0) {
          // Tiêu đề báo cáo
          ws[cellAddress].s = titleStyle;
        } else if (r === 1) {
          // Tiêu đề phụ
          ws[cellAddress].s = subtitleStyle;
        } else if (r === headerRowIndex) {
          // Header row
          ws[cellAddress].s = headerBorderStyle;
        } else if (r >= dataStartRowIndex && r <= dataEndRowIndex) {
          // Data rows
          if (c >= 4 && c < 4 + 31) {
            // Các ô số lượng theo ngày (căn giữa)
            ws[cellAddress].s = quantityCellStyle;
          } else if (c === 4 + 31) {
            // Cột tổng số lượng (căn giữa)
            ws[cellAddress].s = quantityCellStyle;
          } else if (c === 4 + 31 + 1) {
            // Cột thành tiền (căn phải)
            ws[cellAddress].s = moneyCellStyle;
          } else {
            // Các ô khác trong phần dữ liệu
            ws[cellAddress].s = fullBorderStyle;
          }
        } else if (r >= totalRowsStartIndex) {
          // Total rows
          if (c === 4 + 31 + 1) {
            // Cột thành tiền trong phần tổng (căn phải, đậm)
            ws[cellAddress].s = totalMoneyCellStyle;
          } else {
            // Các ô khác trong phần tổng
            ws[cellAddress].s = horizontalBorderStyle;
          }
        }
      }
    }

    // Merge các ô cho tiêu đề
    ws['!merges'] = [
      // Merge tiêu đề chính (dòng 1)
      { s: { r: 0, c: 0 }, e: { r: 0, c: 36 } },
      // Merge tiêu đề phụ (dòng 2)
      { s: { r: 1, c: 0 }, e: { r: 1, c: 36 } }
    ];

    // Thêm worksheet vào workbook
    XLSXStyle.utils.book_append_sheet(wb, ws, 'Báo cáo sản lượng');

    // Xuất file Excel
    const fileName = `Báo_cáo_sản_lượng_${reportData.contract?.contract_number || 'Unknown'}_${month}_${year}.xlsx`;
    XLSXStyle.writeFile(wb, fileName);
  };

  const handlePrint = () => {
    window.print();
  };

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogContent>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>Lỗi</DialogTitle>
        <DialogContent>
          <Alert severity="error">{error}</Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Đóng</Button>
        </DialogActions>
      </Dialog>
    );
  }

  if (!reportData) {
    return null;
  }

  return (
    <Dialog open={open} onClose={onClose} maxWidth="xl" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {year && month
              ? `Báo cáo sản lượng tháng ${month}/${year}`
              : `Báo cáo sản lượng từ ${startDate} đến ${endDate}`
            }
          </Typography>
          <Box>
            <Button
              startIcon={<Print />}
              onClick={handlePrint}
              sx={{ mr: 1 }}
            >
              In
            </Button>
            <Button
              startIcon={<Download />}
              onClick={handleExportExcel}
              variant="contained"
            >
              Xuất Excel
            </Button>
          </Box>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {/* Header thông tin - giống dự án mẫu */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="h6" color="primary" gutterBottom>
            {reportData.contract?.contract_number || ''} - {reportData.contract?.customer_name || ''}
          </Typography>
          <Typography variant="subtitle1">
            {year && month
              ? `Báo cáo sản lượng tháng ${month}/${year}`
              : `Báo cáo sản lượng từ ${startDate} đến ${endDate}`
            }
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Bảng báo cáo - giống dự án mẫu */}
        <TableContainer component={Paper} sx={{ overflowX: 'auto' }}>
          <Table size="small" sx={{ minWidth: 1200 }}>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell rowSpan={2} align="center" sx={{ fontWeight: 'bold' }}>TT</TableCell>
                <TableCell rowSpan={2} align="center" sx={{ fontWeight: 'bold' }}>MẶT HÀNG</TableCell>
                <TableCell rowSpan={2} align="center" sx={{ fontWeight: 'bold' }}>ĐVT</TableCell>
                <TableCell rowSpan={2} align="center" sx={{ fontWeight: 'bold' }}>ĐƠN GIÁ</TableCell>
                <TableCell colSpan={31} align="center" sx={{ fontWeight: 'bold' }}>THÁNG {month}</TableCell>
                <TableCell colSpan={2} align="center" sx={{ fontWeight: 'bold' }}>TỔNG THÁNG {month}</TableCell>
              </TableRow>
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i + 1} align="center" sx={{ fontWeight: 'bold', padding: '4px' }}>
                    {i + 1}
                  </TableCell>
                ))}
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Số lượng</TableCell>
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>Thành tiền</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {(reportData.products || []).map((product, index) => {
                const dailyQuantities = reportData.dailyData?.[product.id] || new Array(31).fill(0);
                const productTotal = reportData.productTotals?.[product.id] || { quantity: 0, amount: 0 };

                return (
                  <TableRow
                    key={product.id}
                    sx={{
                      '&:nth-of-type(odd)': { backgroundColor: '#fafafa' }
                    }}
                  >
                    <TableCell align="center">{index + 1}</TableCell>
                    <TableCell>{product.name}</TableCell>
                    <TableCell align="center">{product.unit_type}</TableCell>
                    <TableCell align="right">{parseFloat(product.unit_price).toLocaleString()}</TableCell>

                    {/* Dữ liệu theo ngày - hiển thị đầy đủ 31 ngày như dự án mẫu */}
                    {Array.from({ length: 31 }, (_, i) => (
                      <TableCell key={i} align="center" sx={{ padding: '4px' }}>
                        {dailyQuantities[i] || 0}
                      </TableCell>
                    ))}

                    {/* Tổng số lượng và thành tiền */}
                    <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                      {productTotal.quantity.toLocaleString('vi-VN')}
                    </TableCell>
                    <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                      {productTotal.amount.toLocaleString('vi-VN')}
                    </TableCell>
                  </TableRow>
                );
              })}

              {/* Dòng tổng cộng - giống y hệt dự án mẫu */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>Tổng chưa gồm VAT</TableCell>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i}></TableCell>
                ))}
                <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                  {Object.values(reportData.productTotals || {}).reduce((sum, item) => sum + item.quantity, 0).toLocaleString('vi-VN')}
                </TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {(reportData.summary?.total_before_vat || 0).toLocaleString('vi-VN')}
                </TableCell>
              </TableRow>

              {/* Dòng VAT */}
              <TableRow>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>VAT</TableCell>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i}></TableCell>
                ))}
                <TableCell></TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {(reportData.summary?.vat_amount || 0).toLocaleString('vi-VN')}
                </TableCell>
              </TableRow>

              {/* Dòng tổng cộng có VAT */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>Tổng gồm VAT</TableCell>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i}></TableCell>
                ))}
                <TableCell></TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {(reportData.summary?.total_with_vat || 0).toLocaleString('vi-VN')}
                </TableCell>
              </TableRow>

              {/* Dòng đã thanh toán */}
              <TableRow>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>Đã thanh toán</TableCell>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i}></TableCell>
                ))}
                <TableCell></TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                  {(reportData.summary?.total_with_vat || 0).toLocaleString('vi-VN')}
                </TableCell>
              </TableRow>

              {/* Dòng còn phải thanh toán */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={4} align="right" sx={{ fontWeight: 'bold' }}>Còn phải thanh toán</TableCell>
                {Array.from({ length: 31 }, (_, i) => (
                  <TableCell key={i}></TableCell>
                ))}
                <TableCell></TableCell>
                <TableCell align="right" sx={{ fontWeight: 'bold' }}>0</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Đóng</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductionReportDialog;
