# 🚀 Quy trình Deploy hoàn chỉnh lên <PERSON>ku - DEMO READY

## 📋 Thông tin Heroku App (Đã Deploy Thành Công)

### App chính (Production-like)
- **Tên app**: `ivc-audit-app`
- **URL**: https://ivc-audit-app-706c36cfdde0.herokuapp.com/
- **Git remote**: `ivc-audit`
- **Database**: postgresql-rugged-16988
- **M<PERSON>c đích**: Demo cho khách hàng - SẴN SÀNG SỬ DỤNG
- **Trạng thái**: ✅ HOẠT ĐỘNG - Database đầy đủ dữ liệu

### Dữ liệu hiện tại (Đã import thành công)
- ✅ **19 Khách hàng** - Đầy đủ thông tin
- ✅ **19 Hợp đồng** - Có giá sản phẩm
- ✅ **119 Sản phẩm** - Danh mục hoàn chỉnh
- ✅ **Dữ liệu sản xuất** - Lịch sử đầy đủ
- ✅ **Công nợ & Thanh toán** - Tính năng FIFO hoạt động

### Trạng thái hiện tại (v50 - HOẠT ĐỘNG HOÀN HẢO)
- ✅ **Frontend** - Hiển thị dữ liệu chính xác
- ✅ **Backend API** - Hỗ trợ cả /api và /api/v1
- ✅ **Database** - Kết nối ổn định, dữ liệu đầy đủ
- ✅ **Tất cả trang** - Customers, Products, Contracts, Dashboard
- ✅ **Sẵn sàng demo** - Không có lỗi

## 🔧 Cấu hình ban đầu (chỉ làm 1 lần)

### 1. Cài đặt Heroku CLI
```bash
# Kiểm tra đã cài chưa
heroku --version

# Đăng nhập
heroku login
```

### 2. Thêm remote cho app chính
```bash
# Trong thư mục project
heroku git:remote -a ivc-audit-app

# Kiểm tra remote
git remote -v
```

### 3. Cấu hình environment variables (ĐÃ HOÀN THÀNH)
```bash
# Xem config hiện tại
heroku config -a ivc-audit-app

# Config đã được thiết lập:
# NODE_ENV=production
# JWT_SECRET=ivc_audit_app_super_secret_jwt_key_production_2024
# CLIENT_URL=https://ivc-audit-app.herokuapp.com
# DATABASE_URL=postgres://... (tự động)
```

## 🎯 QUY TRÌNH DEPLOY HOÀN CHỈNH (Đã thực hiện thành công)

### Bước 1: Chuẩn bị Database Backup
```bash
# Tạo backup sạch không có ownership issues
$env:PGPASSWORD = "110591"; pg_dump -U postgres -h localhost -d tinhtam-hp --no-owner --no-privileges --clean --if-exists -f heroku_clean_backup.sql --verbose
```

### Bước 2: Reset Database Heroku hoàn toàn
```bash
heroku pg:reset postgresql-rugged-16988 --app ivc-audit-app --confirm ivc-audit-app
```

### Bước 3: Deploy Code
```bash
# Push code lên GitHub trước
git push origin master

# Force push lên Heroku để thay thế hoàn toàn
git push ivc-audit master --force
```

### Bước 4: Import Database
```bash
# Import database sạch
Get-Content heroku_clean_backup.sql | heroku pg:psql postgresql-rugged-16988 --app ivc-audit-app
```

### Bước 5: Kiểm tra Deploy
```bash
# Kiểm tra app status
heroku ps --app ivc-audit-app

# Kiểm tra logs
heroku logs --tail --app ivc-audit-app --num 50

# Kiểm tra dữ liệu
heroku pg:psql postgresql-rugged-16988 --app ivc-audit-app -c "SELECT COUNT(*) as customers FROM customers; SELECT COUNT(*) as contracts FROM contracts; SELECT COUNT(*) as products FROM products;"

# Mở app
heroku open --app ivc-audit-app
```

## ⚡ Quy trình Deploy nhanh (Development)

### Bước 1: Chuẩn bị code
```bash
# Đảm bảo code đã commit
git add .
git commit -m "feat: tính năng mới hoặc fix bug"

# Tạo branch backup (khuyến nghị)
git checkout -b backup-$(date +%Y%m%d-%H%M%S)
git checkout main  # hoặc branch chính
```

### Bước 2: Deploy nhanh (Force push)
```bash
# Deploy nhanh - ưu tiên tốc độ
git push heroku main --force

# Hoặc nếu đang ở branch khác
git push heroku your-branch:main --force
```

### Bước 3: Kiểm tra deploy
```bash
# Xem logs real-time
heroku logs --tail -a ivc-audit-app

# Kiểm tra status
heroku ps -a ivc-audit-app

# Mở app trong browser
heroku open -a ivc-audit-app
```

## 🗄️ Đồng bộ Database nhanh

### Reset và đồng bộ từ local (cho development)
```bash
# 1. Tạo dump từ local
set PGPASSWORD=110591 && pg_dump -U postgres -h localhost -d tinhtam-hp --clean --no-owner --no-privileges -f local_dump.sql

# 2. Reset database Heroku (XÓA TOÀN BỘ DỮ LIỆU)

# 3. Import dữ liệu từ local

### Kiểm tra dữ liệu sau sync
```bash
# Kiểm tra số lượng records
```

## 🔍 Troubleshooting

### Lỗi thường gặp và cách fix

#### 1. Build failed
```bash
# Xem logs chi tiết

# Restart app
```

#### 2. Database connection error
```bash
# Kiểm tra database URL

# Restart database
```

#### 3. App không start
```bash
# Kiểm tra Procfile
cat Procfile

# Scale dyno
heroku ps:scale web=1 -a ivc-audit-app
```

## 📝 Checklist Deploy nhanh

### Trước khi deploy:
- [ ] Code đã commit và test local
- [ ] Tạo backup branch (nếu cần)
- [ ] Kiểm tra không có file sensitive trong commit

### Sau khi deploy:
- [ ] Kiểm tra logs không có error
- [ ] Test các tính năng chính
- [ ] Kiểm tra database connection


## 🎯 Thông tin đăng nhập Test


## ⚠️ Lưu ý quan trọng

### Development vs Production:
- **Development**: Dùng force push, reset database thoải mái
- **Production**: Cần backup, migration cẩn thận

### Bảo mật:
- Không commit file .env
- Không hardcode API keys
- Sử dụng Heroku config vars

### Performance:
- Heroku free tier có giới hạn
- App sẽ sleep sau 30 phút không hoạt động
- Database có giới hạn 10,000 rows (free tier)
---

**Cập nhật lần cuối**: $(date)
**Người tạo**: Development Team
**Mục đích**: Hướng dẫn deploy nhanh cho giai đoạn phát triển
