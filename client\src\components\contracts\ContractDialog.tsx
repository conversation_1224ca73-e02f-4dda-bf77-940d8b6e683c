/**
 * Contract Dialog Component
 * Dialog để tạo mới hoặc chỉnh sửa hợp đồng
 */

import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Divider,
  Alert,
  Autocomplete,
  TextField,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { vi } from 'date-fns/locale';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { FormTextField } from '../common/FormFields';
import {
  Contract,
  ContractFormData,
  ContractCreateRequest,
  ContractUpdateRequest,
  CONTRACT_STATUS_OPTIONS
} from '../../types/contract';
import { Customer } from '../../types/customer';
import { customerService } from '../../services/customerService';
import { convertDateToISOString, convertISOStringToDate } from '../../utils/vietnameseFormatters';

interface ContractDialogProps {
  open: boolean;
  contract?: Contract | null;
  loading?: boolean;
  error?: string | null;
  onClose: () => void;
  onSubmit: (data: ContractCreateRequest | ContractUpdateRequest) => Promise<void>;
}

// Validation schema
const validationSchema = yup.object({
  contract_number: yup
    .string()
    .required('Số hợp đồng là bắt buộc')
    .min(3, 'Số hợp đồng phải có ít nhất 3 ký tự')
    .max(100, 'Số hợp đồng không được vượt quá 100 ký tự')
    .matches(/^[A-Za-z0-9\/\-]+$/, 'Số hợp đồng chỉ chứa chữ, số, dấu / và -'),
  customer_id: yup
    .mixed()
    .required('Khách hàng là bắt buộc')
    .test('customer-selected', 'Vui lòng chọn khách hàng', function(value) {
      return value && Number(value) > 0;
    }),
  contract_name: yup
    .string()
    .required('Tên hợp đồng là bắt buộc')
    .min(3, 'Tên hợp đồng phải có ít nhất 3 ký tự')
    .max(200, 'Tên hợp đồng không được vượt quá 200 ký tự'),
  start_date: yup
    .string()
    .required('Ngày bắt đầu là bắt buộc'),
  end_date: yup
    .string()
    .nullable()
    .test('end-date', 'Ngày kết thúc phải sau ngày bắt đầu', function(value) {
      const { start_date } = this.parent;
      if (!value || !start_date) return true;
      return new Date(value) > new Date(start_date);
    }),
  status: yup
    .string()
    .required('Trạng thái là bắt buộc')
    .oneOf(['active', 'paused', 'terminated', 'expired'], 'Trạng thái không hợp lệ'),
  notes: yup
    .string()
    .max(1000, 'Ghi chú không được vượt quá 1000 ký tự'),
});

const ContractDialog: React.FC<ContractDialogProps> = ({
  open,
  contract,
  loading = false,
  error,
  onClose,
  onSubmit,
}) => {
  const isEdit = Boolean(contract);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [customersLoading, setCustomersLoading] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<ContractFormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      contract_number: '',
      customer_id: '',
      contract_name: 'dịch vụ giặt là',
      start_date: '',
      end_date: '',
      status: 'active',
      notes: '',
    },
  });

  // Load customers
  useEffect(() => {
    const loadCustomers = async () => {
      setCustomersLoading(true);
      try {
        const response = await customerService.getAll({ limit: 1000 });
        if (response.success) {
          setCustomers(response.data);
        }
      } catch (error) {
        console.error('Error loading customers:', error);
      } finally {
        setCustomersLoading(false);
      }
    };

    if (open) {
      loadCustomers();
    }
  }, [open]);

  // Reset form khi dialog mở/đóng hoặc contract thay đổi
  useEffect(() => {
    if (open) {
      if (contract) {
        reset({
          contract_number: contract.contract_number,
          customer_id: contract.customer_id,
          contract_name: contract.contract_name,
          start_date: contract.start_date,
          end_date: contract.end_date || '',
          status: contract.status,
          notes: contract.notes || '',
        });
      } else {
        reset({
          contract_number: '',
          customer_id: '',
          contract_name: 'dịch vụ giặt là',
          start_date: '',
          end_date: '',
          status: 'active',
          notes: '',
        });
      }
    }
  }, [open, contract, reset]);

  const handleFormSubmit = async (data: ContractFormData) => {
    try {
      if (isEdit) {
        // Khi edit, không gửi contract_number vì không được phép thay đổi
        const updateData: ContractUpdateRequest = {
          customer_id: Number(data.customer_id),
          contract_name: data.contract_name,
          start_date: data.start_date,
          end_date: data.end_date || undefined,
          status: data.status,
          notes: data.notes || undefined,
        };
        await onSubmit(updateData);
      } else {
        // Khi tạo mới
        const createData: ContractCreateRequest = {
          contract_number: data.contract_number,
          customer_id: Number(data.customer_id),
          contract_name: data.contract_name,
          start_date: data.start_date,
          end_date: data.end_date || undefined,
          status: data.status,
          notes: data.notes || undefined,
        };
        await onSubmit(createData);
      }
    } catch (error) {
      console.error('Error submitting contract:', error);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const selectedCustomer = customers.find(c => c.id === Number(watch('customer_id')));

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={vi}>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { minHeight: 500 }
        }}
      >
        <DialogTitle>
          <Typography variant="h6" component="div">
            {isEdit ? 'Chỉnh sửa hợp đồng' : 'Thêm hợp đồng mới'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {isEdit
              ? 'Cập nhật thông tin hợp đồng'
              : 'Nhập thông tin hợp đồng mới'
            }
          </Typography>
        </DialogTitle>

        <form onSubmit={handleSubmit(handleFormSubmit)}>
          <DialogContent dividers>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Grid container spacing={3}>
              {/* DÒNG 1: Dropdown khách hàng với autocomplete */}
              <Grid item xs={12}>
                <Controller
                  name="customer_id"
                  control={control}
                  render={({ field, fieldState }) => (
                    <Autocomplete
                      value={customers.find(c => c.id === Number(field.value)) || null}
                      onChange={(_, newValue) => {
                        field.onChange(newValue ? newValue.id : '');
                      }}
                      options={customers}
                      getOptionLabel={(option) => `${option.name} (${option.tax_code || 'N/A'})`}
                      renderOption={(props, option) => (
                        <Box component="li" {...props}>
                          <Box>
                            <Typography variant="body2" fontWeight="medium">
                              {option.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {option.short_name} • MST: {option.tax_code || 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      )}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Khách hàng"
                          required
                          error={!!fieldState.error}
                          helperText={fieldState.error?.message || 'Chọn khách hàng cho hợp đồng'}
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {customersLoading && <Box sx={{ display: 'flex', alignItems: 'center', mr: 1 }}>
                                  <Typography variant="caption" color="text.secondary">Đang tải...</Typography>
                                </Box>}
                                {params.InputProps.endAdornment}
                              </>
                            ),
                          }}
                        />
                      )}
                      loading={customersLoading}
                      disabled={customersLoading}
                      noOptionsText="Không tìm thấy khách hàng"
                      loadingText="Đang tải danh sách khách hàng..."
                    />
                  )}
                />
              </Grid>

              {/* DÒNG 2: Số hợp đồng và Nội dung hợp đồng */}
              <Grid item xs={12} sm={6}>
                <FormTextField
                  name="contract_number"
                  control={control}
                  label="Số hợp đồng"
                  required
                  disabled={isEdit}
                  helperText={isEdit ? 'Số hợp đồng không thể thay đổi' : 'Số duy nhất để định danh hợp đồng'}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormTextField
                  name="contract_name"
                  control={control}
                  label="Nội dung hợp đồng"
                  required
                  helperText="Mô tả nội dung chính của hợp đồng"
                />
              </Grid>

              {/* DÒNG 3: Ngày bắt đầu, Ngày kết thúc và Trạng thái */}
              <Grid item xs={12} md={4}>
                <Controller
                  name="start_date"
                  control={control}
                  render={({ field, fieldState }) => (
                    <DatePicker
                      label="Ngày bắt đầu"
                      value={convertISOStringToDate(field.value)}
                      onChange={(newValue) => {
                        field.onChange(convertDateToISOString(newValue));
                      }}
                      format="dd/MM/yyyy"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          required: true,
                          error: !!fieldState.error,
                          helperText: fieldState.error?.message || 'Ngày bắt đầu hiệu lực hợp đồng',
                          placeholder: 'dd/mm/yyyy'
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="end_date"
                  control={control}
                  render={({ field, fieldState }) => (
                    <DatePicker
                      label="Ngày kết thúc"
                      value={convertISOStringToDate(field.value)}
                      onChange={(newValue) => {
                        field.onChange(convertDateToISOString(newValue));
                      }}
                      format="dd/MM/yyyy"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          error: !!fieldState.error,
                          helperText: fieldState.error?.message || 'Ngày kết thúc hợp đồng (có thể để trống)',
                          placeholder: 'dd/mm/yyyy'
                        },
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field, fieldState }) => (
                    <FormControl fullWidth error={!!fieldState.error}>
                      <InputLabel required>Trạng thái</InputLabel>
                      <Select
                        {...field}
                        label="Trạng thái"
                        size="medium"
                      >
                        {CONTRACT_STATUS_OPTIONS.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                      {fieldState.error && (
                        <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                          {fieldState.error.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              {/* Ghi chú (optional) */}
              <Grid item xs={12}>
                <FormTextField
                  name="notes"
                  control={control}
                  label="Ghi chú"
                  multiline
                  rows={3}
                  helperText="Thông tin bổ sung về hợp đồng (không bắt buộc)"
                />
              </Grid>
            </Grid>
          </DialogContent>

          <DialogActions sx={{ px: 3, py: 2 }}>
            <Button
              onClick={handleClose}
              disabled={isSubmitting}
              color="inherit"
            >
              Hủy
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={isSubmitting || loading}
              sx={{ minWidth: 100 }}
            >
              {isSubmitting ? 'Đang xử lý...' : (isEdit ? 'Cập nhật' : 'Thêm mới')}
            </Button>
          </DialogActions>
        </form>
      </Dialog>
    </LocalizationProvider>
  );
};

export default ContractDialog;
