const { pool } = require('../db');

/**
 * Product Model
 * Xử lý các thao tác CRUD cho bảng products
 */

/**
 * L<PERSON>y tất cả sản phẩm với phân trang và tìm kiếm
 * @param {Object} options - Tùy chọn truy vấn
 * @param {number} options.page - Trang hiện tại
 * @param {number} options.limit - Số lượng bản ghi trên mỗi trang
 * @param {string} options.search - Từ khóa tìm kiếm
 * @param {string} options.unit_type - Filter theo loại đơn vị
 * @param {boolean} options.is_active - Filter theo trạng thái
 * @param {string} options.sortBy - Cột để sắp xếp
 * @param {string} options.sortOrder - Thứ tự sắp xếp (ASC/DESC)
 * @returns {Object} Kết quả truy vấn với dữ liệu và thông tin phân trang
 */
const getAllProducts = async (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    unit_type = '',
    is_active = true,
    sortBy = 'created_at',
    sortOrder = 'DESC'
  } = options;

  try {
    // Validate sortBy để tránh SQL injection
    const allowedSortColumns = ['id', 'code', 'name', 'unit_type', 'is_active', 'created_at', 'updated_at'];
    const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const validSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Tính offset
    const offset = (page - 1) * limit;

    // Xây dựng điều kiện WHERE
    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    // Filter theo trạng thái active
    if (is_active !== undefined) {
      whereClause += ` AND p.is_active = $${paramIndex}`;
      queryParams.push(is_active);
      paramIndex++;
    }

    // Filter theo unit_type
    if (unit_type && unit_type.trim()) {
      whereClause += ` AND p.unit_type = $${paramIndex}`;
      queryParams.push(unit_type.trim());
      paramIndex++;
    }

    // Tìm kiếm theo tên hoặc mã sản phẩm
    if (search && search.trim()) {
      whereClause += ` AND (
        p.name ILIKE $${paramIndex} OR
        p.code ILIKE $${paramIndex} OR
        p.description ILIKE $${paramIndex}
      )`;
      queryParams.push(`%${search.trim()}%`);
      paramIndex++;
    }

    // Query để lấy tổng số bản ghi
    const countQuery = `
      SELECT COUNT(*) as total
      FROM products p
      ${whereClause}
    `;

    // Query để lấy dữ liệu
    const dataQuery = `
      SELECT
        p.id,
        p.code,
        p.name,
        p.description,
        p.unit_type,
        p.is_active,
        p.created_at,
        p.updated_at,
        u.name as created_by_name
      FROM products p
      LEFT JOIN users u ON p.created_by = u.id
      ${whereClause}
      ORDER BY p.${validSortBy} ${validSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Thực hiện cả hai query
    const [countResult, dataResult] = await Promise.all([
      pool.query(countQuery, queryParams.slice(0, -2)), // Loại bỏ limit và offset cho count query
      pool.query(dataQuery, queryParams)
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      products: dataResult.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    console.error('Error in getAllProducts:', error);
    throw error;
  }
};

/**
 * Lấy sản phẩm theo ID
 * @param {number} id - ID của sản phẩm
 * @returns {Object|null} Thông tin sản phẩm hoặc null nếu không tìm thấy
 */
const getProductById = async (id) => {
  try {
    const query = `
      SELECT
        p.id,
        p.code,
        p.name,
        p.description,
        p.unit_type,
        p.is_active,
        p.created_at,
        p.updated_at,
        u.name as created_by_name
      FROM products p
      LEFT JOIN users u ON p.created_by = u.id
      WHERE p.id = $1
    `;

    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in getProductById:', error);
    throw error;
  }
};

/**
 * Tạo sản phẩm mới
 * @param {Object} productData - Dữ liệu sản phẩm
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Thông tin sản phẩm vừa tạo
 */
const createProduct = async (productData, createdBy) => {
  const {
    code,
    name,
    description,
    unit_type
  } = productData;

  try {
    const query = `
      INSERT INTO products (
        code, name, description, unit_type, created_by
      ) VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `;

    const values = [
      code,
      name,
      description || null,
      unit_type,
      createdBy
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  } catch (error) {
    console.error('Error in createProduct:', error);
    throw error;
  }
};

/**
 * Cập nhật thông tin sản phẩm
 * @param {number} id - ID của sản phẩm
 * @param {Object} productData - Dữ liệu cập nhật
 * @returns {Object|null} Thông tin sản phẩm sau khi cập nhật
 */
const updateProduct = async (id, productData) => {
  const {
    name,
    description,
    unit_type,
    is_active
  } = productData;

  try {
    const query = `
      UPDATE products
      SET
        name = $1,
        description = $2,
        unit_type = $3,
        is_active = $4,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
      RETURNING *
    `;

    const values = [
      name,
      description || null,
      unit_type,
      is_active !== undefined ? is_active : true,
      id
    ];

    const result = await pool.query(query, values);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error in updateProduct:', error);
    throw error;
  }
};

/**
 * Xóa sản phẩm (soft delete)
 * @param {number} id - ID của sản phẩm
 * @returns {boolean} True nếu xóa thành công
 */
const deleteProduct = async (id) => {
  try {
    const query = `
      UPDATE products
      SET is_active = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND is_active = true
      RETURNING id
    `;

    const result = await pool.query(query, [id]);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in deleteProduct:', error);
    throw error;
  }
};

/**
 * Kiểm tra mã sản phẩm đã tồn tại
 * @param {string} code - Mã sản phẩm
 * @param {number} excludeId - ID sản phẩm cần loại trừ (dùng khi update)
 * @returns {boolean} True nếu mã sản phẩm đã tồn tại
 */
const checkCodeExists = async (code, excludeId = null) => {
  try {
    if (!code) return false;

    let query = 'SELECT id FROM products WHERE code = $1';
    const params = [code];

    if (excludeId) {
      query += ' AND id != $2';
      params.push(excludeId);
    }

    const result = await pool.query(query, params);
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error in checkCodeExists:', error);
    throw error;
  }
};

/**
 * Lấy danh sách unit types có sẵn
 * @returns {Array} Danh sách unit types
 */
const getUnitTypes = async () => {
  try {
    const query = `
      SELECT DISTINCT unit_type
      FROM products
      WHERE is_active = true
      ORDER BY unit_type
    `;

    const result = await pool.query(query);
    return result.rows.map(row => row.unit_type);
  } catch (error) {
    console.error('Error in getUnitTypes:', error);
    throw error;
  }
};

/**
 * Lấy mã sản phẩm tiếp theo
 * @returns {string} Mã sản phẩm tiếp theo (VD: SP001, SP002, ...)
 */
const getNextProductCode = async () => {
  try {
    const query = `
      SELECT code
      FROM products
      WHERE code ~ '^SP[0-9]+$'
      ORDER BY CAST(SUBSTRING(code FROM 3) AS INTEGER) DESC
      LIMIT 1
    `;

    const result = await pool.query(query);

    if (result.rows.length === 0) {
      // Nếu chưa có sản phẩm nào, bắt đầu từ SP001
      return 'SP001';
    }

    const lastCode = result.rows[0].code;
    const lastNumber = parseInt(lastCode.substring(2));
    const nextNumber = lastNumber + 1;

    // Format với leading zeros (3 chữ số)
    return `SP${nextNumber.toString().padStart(3, '0')}`;
  } catch (error) {
    console.error('Error in getNextProductCode:', error);
    throw error;
  }
};

/**
 * Kiểm tra tên sản phẩm đã tồn tại
 * @param {string} name - Tên sản phẩm cần kiểm tra
 * @param {number} excludeId - ID sản phẩm cần loại trừ (cho trường hợp update)
 * @returns {Object} Kết quả kiểm tra và suggestions
 */
const checkNameExists = async (name, excludeId = null) => {
  try {
    // Kiểm tra tên trùng hoàn toàn
    let exactQuery = `
      SELECT id, code, name
      FROM products
      WHERE LOWER(TRIM(name)) = LOWER(TRIM($1))
      AND is_active = true
    `;
    let exactParams = [name];

    if (excludeId) {
      exactQuery += ` AND id != $2`;
      exactParams.push(excludeId);
    }

    const exactResult = await pool.query(exactQuery, exactParams);

    // Tìm các tên tương tự (để làm suggestions)
    let similarQuery = `
      SELECT id, code, name,
        SIMILARITY(LOWER(name), LOWER($1)) as similarity
      FROM products
      WHERE SIMILARITY(LOWER(name), LOWER($1)) > 0.3
      AND LOWER(TRIM(name)) != LOWER(TRIM($1))
      AND is_active = true
    `;
    let similarParams = [name];

    if (excludeId) {
      similarQuery += ` AND id != $2`;
      similarParams.push(excludeId);
    }

    similarQuery += ` ORDER BY similarity DESC LIMIT 5`;

    const similarResult = await pool.query(similarQuery, similarParams);

    return {
      exists: exactResult.rows.length > 0,
      exactMatch: exactResult.rows[0] || null,
      suggestions: similarResult.rows || []
    };
  } catch (error) {
    console.error('Error in checkNameExists:', error);
    throw error;
  }
};

module.exports = {
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  checkCodeExists,
  getUnitTypes,
  getNextProductCode,
  checkNameExists
};
