/**
 * PricingTable Component
 * Bảng hiển thị đơn gi<PERSON> sản phẩm trong hợp đồng
 */

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Typography,
  Box,
  Tooltip,
  TablePagination,
  TextField,
  InputAdornment,
  Button,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  GetApp as ExportIcon,
} from '@mui/icons-material';
import { ContractPrice } from '../../types/contractPrice';
import { formatCurrencyVN, formatDateVN } from '../../utils/vietnameseFormatters';

// Temporary inline formatters
const formatCurrency = (amount: number): string => {
  return formatCurrencyVN(amount, true); // true để hiển thị VND
};

const formatDate = (dateString: string): string => {
  if (!dateString) return '';
  return formatDateVN(dateString);
};

interface PricingTableProps {
  prices: ContractPrice[];
  loading?: boolean;
  totalCount?: number;
  page?: number;
  rowsPerPage?: number;
  searchQuery?: string;
  contractId?: number;
  productId?: number;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  onSearch?: (query: string) => void;
  onFilter?: (filters: any) => void;
  onAdd?: () => void;
  onEdit?: (price: ContractPrice) => void;
  onDelete?: (price: ContractPrice) => void;
  onViewHistory?: (price: ContractPrice) => void;
  onExport?: () => void;
  onRefresh?: () => void;
}

const PricingTable: React.FC<PricingTableProps> = ({
  prices,
  loading = false,
  totalCount = 0,
  page = 0,
  rowsPerPage = 10,
  searchQuery = '',
  contractId,
  productId,
  onPageChange,
  onRowsPerPageChange,
  onSearch,
  onFilter,
  onAdd,
  onEdit,
  onDelete,
  onViewHistory,
  onExport,
  onRefresh,
}) => {
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSearch?.(event.target.value);
  };

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    onPageChange?.(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onRowsPerPageChange?.(parseInt(event.target.value, 10));
  };

  const getStatusChip = (price: ContractPrice) => {
    const isActive = price.is_active;
    const isExpired = price.expiry_date && new Date(price.expiry_date) < new Date();

    if (!isActive) {
      return <Chip label="Không hiệu lực" color="default" size="small" />;
    }

    if (isExpired) {
      return <Chip label="Hết hạn" color="error" size="small" />;
    }

    return <Chip label="Hiệu lực" color="success" size="small" />;
  };

  return (
    <Box>
      {/* Header với Search và Actions */}
      <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
        <TextField
          placeholder="Tìm kiếm sản phẩm, hợp đồng..."
          value={searchQuery}
          onChange={handleSearchChange}
          size="small"
          sx={{ minWidth: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />

        <Button
          startIcon={<FilterIcon />}
          onClick={handleFilterClick}
          variant="outlined"
          size="small"
        >
          Bộ lọc
        </Button>

        <Box sx={{ flexGrow: 1 }} />

        <Button
          startIcon={<ExportIcon />}
          onClick={onExport}
          variant="outlined"
          size="small"
        >
          Xuất Excel
        </Button>

        <Button
          startIcon={<AddIcon />}
          onClick={onAdd}
          variant="contained"
          size="small"
        >
          Thêm đơn giá
        </Button>
      </Box>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={() => { onFilter?.({ is_active: true }); handleFilterClose(); }}>
          Chỉ hiển thị giá hiệu lực
        </MenuItem>
        <MenuItem onClick={() => { onFilter?.({ is_active: false }); handleFilterClose(); }}>
          Chỉ hiển thị giá không hiệu lực
        </MenuItem>
        <MenuItem onClick={() => { onFilter?.({}); handleFilterClose(); }}>
          Hiển thị tất cả
        </MenuItem>
      </Menu>

      {/* Table */}
      <TableContainer component={Paper}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>Hợp đồng</TableCell>
              <TableCell>Sản phẩm</TableCell>
              <TableCell align="right">Đơn giá</TableCell>
              <TableCell>Ngày hiệu lực</TableCell>
              <TableCell>Ngày hết hạn</TableCell>
              <TableCell>Trạng thái</TableCell>
              <TableCell>Ghi chú</TableCell>
              <TableCell align="center">Thao tác</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  <Typography>Đang tải...</Typography>
                </TableCell>
              </TableRow>
            ) : prices.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center">
                  <Typography color="textSecondary">
                    Không có dữ liệu đơn giá
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              prices.map((price) => (
                <TableRow key={price.id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {price.contract_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {price.product_name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {price.product_code}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" fontWeight="medium">
                      {formatCurrency(price.price)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(price.effective_date)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {price.expiry_date ? formatDate(price.expiry_date) : 'Không giới hạn'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {getStatusChip(price)}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
                      {price.notes || '-'}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="Lịch sử giá">
                        <IconButton
                          size="small"
                          onClick={() => onViewHistory?.(price)}
                        >
                          <HistoryIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Chỉnh sửa">
                        <IconButton
                          size="small"
                          onClick={() => onEdit?.(price)}
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Xóa">
                        <IconButton
                          size="small"
                          onClick={() => onDelete?.(price)}
                          color="error"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        component="div"
        count={totalCount}
        page={page}
        onPageChange={handlePageChange}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={handleRowsPerPageChange}
        rowsPerPageOptions={[5, 10, 25, 50]}
        labelRowsPerPage="Số dòng mỗi trang:"
        labelDisplayedRows={({ from, to, count }) =>
          `${from}–${to} của ${count !== -1 ? count : `hơn ${to}`}`
        }
      />
    </Box>
  );
};

export default PricingTable;
