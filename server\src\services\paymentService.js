const paymentModel = require('../models/paymentModel');
const { pool } = require('../db');

/**
 * Payment Service
 * Xử lý logic nghiệp vụ cho payments (Thanh toán) với FIFO allocation
 */

/**
 * L<PERSON>y danh sách payments với filtering và pagination
 * @param {Object} options - <PERSON><PERSON>y chọn truy vấn
 * @returns {Object} Kết quả với payments và pagination info
 */
const getPayments = async (options) => {
  try {
    return await paymentModel.getAllPayments(options);
  } catch (error) {
    console.error('Error in paymentService.getPayments:', error);
    throw new Error('Không thể lấy danh sách thanh toán');
  }
};

/**
 * Lấy payment theo ID
 * @param {number} id - ID của payment
 * @returns {Object|null} Thông tin payment
 */
const getPaymentById = async (id) => {
  try {
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    const payment = await paymentModel.getPaymentById(id);
    if (!payment) {
      throw new Error('Không tìm thấy thanh toán');
    }

    // Lấy thêm thông tin phân bổ thanh toán
    const allocations = await paymentModel.getPaymentAllocations(id);
    payment.allocations = allocations;

    return payment;
  } catch (error) {
    console.error('Error in paymentService.getPaymentById:', error);
    throw error;
  }
};

/**
 * Tạo payment mới với FIFO allocation
 * @param {Object} paymentData - Dữ liệu payment
 * @param {number} createdBy - ID người tạo
 * @returns {Object} Payment vừa tạo với allocation info
 */
const createPayment = async (paymentData, createdBy) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    // Validate dữ liệu đầu vào
    await validatePaymentData(paymentData);

    // Validate customer tồn tại
    await validateCustomer(paymentData.customer_id);

    // Tạo payment
    const newPayment = await paymentModel.createPayment(paymentData, createdBy);
    console.log('Created payment:', newPayment);

    // Nếu payment được confirmed, thực hiện FIFO allocation
    console.log('Payment status:', newPayment.status);
    if (newPayment.status === 'confirmed') {
      console.log('Executing FIFO allocation for payment:', newPayment.id);
      try {
        const allocationResult = await allocatePaymentFIFO(newPayment.id, newPayment.customer_id, newPayment.amount, newPayment.payment_date);
        console.log('FIFO allocation result:', allocationResult);
        console.log('FIFO allocation completed for payment:', newPayment.id);
      } catch (error) {
        console.error('Error in FIFO allocation:', error);
        throw error;
      }
    } else {
      console.log('Payment not confirmed, skipping FIFO allocation');
    }

    await client.query('COMMIT');

    // Lấy thông tin đầy đủ của payment vừa tạo
    const fullPayment = await getPaymentById(newPayment.id);
    return fullPayment;
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in paymentService.createPayment:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Cập nhật payment
 * @param {number} id - ID của payment
 * @param {Object} paymentData - Dữ liệu cập nhật
 * @returns {Object} Payment sau khi cập nhật
 */
const updatePayment = async (id, paymentData) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    // Validate ID
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    // Kiểm tra payment tồn tại
    const existingPayment = await paymentModel.getPaymentById(id);
    if (!existingPayment) {
      throw new Error('Không tìm thấy thanh toán');
    }

    // Validate dữ liệu đầu vào
    await validatePaymentData(paymentData);

    // Validate customer tồn tại
    await validateCustomer(paymentData.customer_id);

    // Cập nhật payment
    const updatedPayment = await paymentModel.updatePayment(id, paymentData);
    if (!updatedPayment) {
      throw new Error('Không thể cập nhật thanh toán');
    }

    // Nếu payment amount hoặc status thay đổi, cần re-allocate
    const needReallocation = (
      existingPayment.amount !== paymentData.amount ||
      existingPayment.status !== paymentData.status ||
      existingPayment.customer_id !== paymentData.customer_id
    );

    if (needReallocation) {
      // Xóa allocation cũ
      await clearPaymentAllocations(id);
      
      // Tạo allocation mới nếu status là confirmed
      if (paymentData.status === 'confirmed') {
        await allocatePaymentFIFO(id, paymentData.customer_id, paymentData.amount, paymentData.payment_date);
      }
    }

    await client.query('COMMIT');

    // Lấy thông tin đầy đủ của payment sau khi cập nhật
    return await getPaymentById(id);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in paymentService.updatePayment:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Xóa payment
 * @param {number} id - ID của payment
 * @returns {boolean} True nếu xóa thành công
 */
const deletePayment = async (id) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    // Validate ID
    if (!id || isNaN(id)) {
      throw new Error('ID không hợp lệ');
    }

    // Kiểm tra payment tồn tại
    const existingPayment = await paymentModel.getPaymentById(id);
    if (!existingPayment) {
      throw new Error('Không tìm thấy thanh toán');
    }

    // Xóa payment (sẽ tự động xóa allocations do foreign key)
    const deleted = await paymentModel.deletePayment(id);
    if (!deleted) {
      throw new Error('Không thể xóa thanh toán');
    }

    await client.query('COMMIT');
    return true;
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in paymentService.deletePayment:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Confirm payment và thực hiện FIFO allocation
 * @param {number} id - ID của payment
 * @returns {Object} Payment sau khi confirm
 */
const confirmPayment = async (id) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');

    // Kiểm tra payment tồn tại và đang pending
    const existingPayment = await paymentModel.getPaymentById(id);
    if (!existingPayment) {
      throw new Error('Không tìm thấy thanh toán');
    }

    if (existingPayment.status !== 'pending') {
      throw new Error('Chỉ có thể xác nhận thanh toán đang chờ');
    }

    // Cập nhật status thành confirmed
    await paymentModel.updatePayment(id, { ...existingPayment, status: 'confirmed' });

    // Thực hiện FIFO allocation
    await allocatePaymentFIFO(id, existingPayment.customer_id, existingPayment.amount, existingPayment.payment_date);

    await client.query('COMMIT');

    // Lấy thông tin đầy đủ sau khi confirm
    return await getPaymentById(id);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error in paymentService.confirmPayment:', error);
    throw error;
  } finally {
    client.release();
  }
};

/**
 * Lấy preview FIFO allocation trước khi tạo payment
 * @param {number} customerId - ID khách hàng
 * @param {number} amount - Số tiền thanh toán
 * @param {string} paymentDate - Ngày thanh toán
 * @returns {Array} Danh sách receivables sẽ được allocate
 */
const getPaymentAllocationPreview = async (customerId, amount, paymentDate) => {
  try {
    const query = `
      SELECT
        r.id,
        r.invoice_number,
        r.transaction_date,
        r.due_date,
        r.original_amount,
        COALESCE(SUM(pa.allocated_amount), 0) as total_allocated,
        (r.original_amount - COALESCE(SUM(pa.allocated_amount), 0)) as remaining_balance
      FROM receivables r
      LEFT JOIN payment_allocations pa ON r.id = pa.receivable_id
      WHERE r.customer_id = $1
      AND r.status IN ('active', 'overdue')
      AND r.transaction_date <= $2
      GROUP BY r.id, r.original_amount, r.transaction_date
      HAVING (r.original_amount - COALESCE(SUM(pa.allocated_amount), 0)) > 0
      ORDER BY r.transaction_date ASC, r.id ASC
    `;

    const result = await pool.query(query, [customerId, paymentDate]);
    
    let remainingAmount = parseFloat(amount);
    const allocations = [];

    for (const receivable of result.rows) {
      if (remainingAmount <= 0) break;

      const allocatedAmount = Math.min(remainingAmount, parseFloat(receivable.remaining_balance));
      
      allocations.push({
        receivable_id: receivable.id,
        invoice_number: receivable.invoice_number,
        transaction_date: receivable.transaction_date,
        due_date: receivable.due_date,
        original_amount: receivable.original_amount,
        remaining_balance: receivable.remaining_balance,
        allocated_amount: allocatedAmount,
        remaining_after_allocation: parseFloat(receivable.remaining_balance) - allocatedAmount
      });

      remainingAmount -= allocatedAmount;
    }

    return {
      allocations,
      total_allocated: parseFloat(amount) - remainingAmount,
      unallocated_amount: remainingAmount
    };
  } catch (error) {
    console.error('Error in paymentService.getPaymentAllocationPreview:', error);
    throw error;
  }
};

/**
 * Thực hiện FIFO allocation cho payment
 * @param {number} paymentId - ID payment
 * @param {number} customerId - ID khách hàng
 * @param {number} amount - Số tiền
 * @param {string} paymentDate - Ngày thanh toán
 */
const allocatePaymentFIFO = async (paymentId, customerId, amount, paymentDate) => {
  try {
    // Gọi stored procedure FIFO allocation
    await pool.query(
      'SELECT allocate_payment_fifo($1, $2, $3, $4)',
      [paymentId, customerId, amount, paymentDate]
    );
  } catch (error) {
    console.error('Error in allocatePaymentFIFO:', error);
    throw new Error('Không thể phân bổ thanh toán theo FIFO');
  }
};

/**
 * Xóa tất cả allocations của payment
 * @param {number} paymentId - ID payment
 */
const clearPaymentAllocations = async (paymentId) => {
  try {
    await pool.query('DELETE FROM payment_allocations WHERE payment_id = $1', [paymentId]);
  } catch (error) {
    console.error('Error in clearPaymentAllocations:', error);
    throw error;
  }
};

/**
 * Validate dữ liệu payment
 * @param {Object} paymentData - Dữ liệu cần validate
 */
const validatePaymentData = async (paymentData) => {
  const {
    customer_id,
    payment_date,
    amount,
    payment_method
  } = paymentData;

  // Kiểm tra các trường bắt buộc
  if (!customer_id || !payment_date || !amount || !payment_method) {
    throw new Error('Thiếu thông tin bắt buộc');
  }

  // Validate số tiền
  if (isNaN(amount) || amount <= 0) {
    throw new Error('Số tiền phải là số dương');
  }

  // Validate ngày
  const paymentDateObj = new Date(payment_date);
  if (isNaN(paymentDateObj.getTime())) {
    throw new Error('Ngày thanh toán không hợp lệ');
  }
};

/**
 * Validate customer tồn tại
 * @param {number} customerId - ID khách hàng
 */
const validateCustomer = async (customerId) => {
  const query = 'SELECT id FROM customers WHERE id = $1 AND is_active = true';
  const result = await pool.query(query, [customerId]);
  if (result.rows.length === 0) {
    throw new Error('Khách hàng không tồn tại hoặc đã bị vô hiệu hóa');
  }
};

module.exports = {
  getPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
  confirmPayment,
  getPaymentAllocationPreview
};
