import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  Container,
  Paper,
} from '@mui/material';
import {
  Block as BlockIcon,
  Home as HomeIcon,
  ArrowBack as ArrowBackIcon,
} from '@mui/icons-material';

/**
 * Forbidden Page Component
 * Displayed when user doesn't have permission to access a resource
 */
const Forbidden: React.FC = () => {
  const navigate = useNavigate();

  /**
   * <PERSON><PERSON> go back
   */
  const handleGoBack = () => {
    navigate(-1);
  };

  /**
   * Handle go to dashboard
   */
  const handleGoToDashboard = () => {
    navigate('/dashboard');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'background.default',
        padding: 2,
      }}
    >
      <Container maxWidth="sm">
        <Paper
          elevation={3}
          sx={{
            p: 4,
            textAlign: 'center',
            borderRadius: 3,
          }}
        >
          {/* Error Icon */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              mb: 3,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 120,
                height: 120,
                borderRadius: '50%',
                backgroundColor: 'error.light',
                color: 'error.contrastText',
              }}
            >
              <BlockIcon sx={{ fontSize: 60 }} />
            </Box>
          </Box>

          {/* Error Code */}
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontSize: '4rem',
              fontWeight: 700,
              color: 'error.main',
              mb: 1,
            }}
          >
            403
          </Typography>

          {/* Error Title */}
          <Typography
            variant="h4"
            component="h2"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 2,
            }}
          >
            Truy cập bị từ chối
          </Typography>

          {/* Error Description */}
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{ mb: 4, lineHeight: 1.6 }}
          >
            Bạn không có quyền truy cập vào trang này. 
            Vui lòng liên hệ quản trị viên nếu bạn cho rằng đây là lỗi.
          </Typography>

          {/* Action Buttons */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              gap: 2,
              justifyContent: 'center',
            }}
          >
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleGoBack}
              sx={{
                minWidth: 140,
                py: 1.5,
              }}
            >
              Quay lại
            </Button>
            <Button
              variant="contained"
              startIcon={<HomeIcon />}
              onClick={handleGoToDashboard}
              sx={{
                minWidth: 140,
                py: 1.5,
              }}
            >
              Về trang chủ
            </Button>
          </Box>

          {/* Additional Info */}
          <Box sx={{ mt: 4, pt: 3, borderTop: '1px solid', borderColor: 'divider' }}>
            <Typography variant="caption" color="text.secondary">
              Nếu bạn cần trợ giúp, vui lòng liên hệ với quản trị viên hệ thống.
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default Forbidden;
