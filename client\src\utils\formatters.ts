/**
 * Formatters Utilities
 * Các hàm tiện ích để format dữ liệu hiển thị
 */

/**
 * Format currency (VND)
 */
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
  }).format(amount);
};

/**
 * Format currency to millions (triệu đồng)
 * @param amount - Amount in VND
 * @param showUnit - Whether to show "tr" unit
 * @returns Formatted string like "1.5 tr" or "1.5"
 */
export const formatCurrencyToMillions = (amount: number | string, showUnit: boolean = true): string => {
  const numValue = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numValue) || numValue === 0) return showUnit ? '0 tr' : '0';

  const millions = numValue / 1000000;

  // Format with appropriate decimal places
  let formatted: string;
  if (millions >= 100) {
    formatted = millions.toFixed(0); // No decimals for large numbers
  } else if (millions >= 10) {
    formatted = millions.toFixed(1); // 1 decimal for medium numbers
  } else {
    formatted = millions.toFixed(2); // 2 decimals for small numbers
  }

  // Remove trailing zeros
  formatted = parseFloat(formatted).toString();

  return showUnit ? `${formatted} tr` : formatted;
};

/**
 * Auto format currency based on amount size
 * @param amount - Amount in VND
 * @returns Formatted string with appropriate unit
 */
export const formatCurrencyAuto = (amount: number | string): string => {
  const numValue = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numValue) || numValue === 0) return '0';

  if (numValue >= 1000000000) { // >= 1 billion
    return `${(numValue / 1000000000).toFixed(1)} tỷ`;
  } else if (numValue >= 1000000) { // >= 1 million
    return formatCurrencyToMillions(numValue, true);
  } else if (numValue >= 1000) { // >= 1 thousand
    return `${(numValue / 1000).toFixed(0)} nghìn`;
  } else {
    return numValue.toLocaleString('vi-VN');
  }
};

/**
 * Format number with thousand separators
 */
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('vi-VN').format(num);
};

/**
 * Format date (DD/MM/YYYY)
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(date);
};

/**
 * Format datetime (DD/MM/YYYY HH:mm)
 */
export const formatDateTime = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  return new Intl.DateTimeFormat('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Format time (HH:mm:ss)
 */
export const formatTime = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  return new Intl.DateTimeFormat('vi-VN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(date);
};

/**
 * Format file size
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format percentage
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${value.toFixed(decimals)}%`;
};

/**
 * Format phone number (Vietnam format)
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');

  // Format based on length
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{3})/, '$1 $2 $3');
  } else if (cleaned.length === 11) {
    return cleaned.replace(/(\d{4})(\d{3})(\d{4})/, '$1 $2 $3');
  }

  return phone;
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';

  if (text.length <= maxLength) return text;

  return text.substring(0, maxLength) + '...';
};

/**
 * Capitalize first letter
 */
export const capitalizeFirst = (text: string): string => {
  if (!text) return '';

  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

/**
 * Format contract status
 */
export const formatContractStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    active: 'Đang hoạt động',
    paused: 'Tạm dừng',
    terminated: 'Đã chấm dứt',
    expired: 'Hết hạn',
  };

  return statusMap[status] || status;
};

/**
 * Format product unit type
 */
export const formatUnitType = (unitType: string): string => {
  const unitMap: Record<string, string> = {
    piece: 'Cái/Chiếc',
    kg: 'Kilogram (kg)',
    set: 'Bộ/Set',
    meter: 'Mét (m)',
    liter: 'Lít (l)',
    box: 'Hộp/Thùng',
  };

  return unitMap[unitType] || unitType;
};

/**
 * Format boolean to Vietnamese
 */
export const formatBoolean = (value: boolean): string => {
  return value ? 'Có' : 'Không';
};

/**
 * Format array to comma-separated string
 */
export const formatArray = (arr: string[], separator: string = ', '): string => {
  if (!Array.isArray(arr)) return '';

  return arr.join(separator);
};

/**
 * Format duration in minutes to human readable
 */
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} phút`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (remainingMinutes === 0) {
    return `${hours} giờ`;
  }

  return `${hours} giờ ${remainingMinutes} phút`;
};

/**
 * Format relative time (time ago)
 */
export const formatRelativeTime = (dateString: string): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return '';

  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Vừa xong';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} phút trước`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} giờ trước`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} ngày trước`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} tháng trước`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} năm trước`;
};

/**
 * Parse currency string to number
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString) return 0;

  // Remove currency symbols and spaces, keep only digits and decimal point
  const cleaned = currencyString.replace(/[^\d.,]/g, '');

  // Replace comma with dot for decimal point
  const normalized = cleaned.replace(',', '.');

  return parseFloat(normalized) || 0;
};

/**
 * Format quantity with unit
 */
export const formatQuantity = (quantity: number, unit: string): string => {
  const formattedQuantity = formatNumber(quantity);
  const formattedUnit = formatUnitType(unit);

  return `${formattedQuantity} ${formattedUnit}`;
};

/**
 * Format price for input display (with thousand separators)
 * Sử dụng cho input fields để hiển thị số tiền với dấu phẩy
 */
export const formatPriceInput = (value: number | string): string => {
  if (!value && value !== 0) return '';

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '';

  // Format với dấu phẩy ngăn cách hàng nghìn (định dạng Việt Nam)
  return new Intl.NumberFormat('vi-VN').format(numValue);
};

/**
 * Parse price input string to number
 * Chuyển đổi chuỗi có dấu phẩy thành số để lưu vào database
 */
export const parsePriceInput = (inputValue: string): number => {
  if (!inputValue) return 0;

  // Loại bỏ tất cả dấu phẩy và khoảng trắng, chỉ giữ lại số và dấu thập phân
  const cleaned = inputValue.replace(/[,\s]/g, '');

  // Chuyển đổi thành số
  const parsed = parseFloat(cleaned);

  return isNaN(parsed) ? 0 : parsed;
};

/**
 * Format price input on change event
 * Xử lý real-time formatting khi người dùng nhập
 */
export const handlePriceInputChange = (inputValue: string): {
  displayValue: string;
  numericValue: number
} => {
  // Loại bỏ tất cả ký tự không phải số
  const numbersOnly = inputValue.replace(/[^\d]/g, '');

  if (!numbersOnly) {
    return { displayValue: '', numericValue: 0 };
  }

  // Chuyển đổi thành số
  const numericValue = parseInt(numbersOnly, 10);

  // Format với dấu phẩy
  const displayValue = formatPriceInput(numericValue);

  return { displayValue, numericValue };
};
