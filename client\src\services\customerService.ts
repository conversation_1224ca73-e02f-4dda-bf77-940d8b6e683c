import { get, post, put, del } from './api';
import { Customer, CustomerParams, CustomerResult } from '../types/customer';

/**
 * Customer Service
 * Handles all customer-related API calls
 */
class CustomerService {
  private readonly baseUrl = '/customers';

  /**
   * Get all customers with pagination and search
   */
  async getAllCustomers(params?: CustomerParams): Promise<CustomerResult> {
    const queryParams = new URLSearchParams();

    if (params?.page) {
      queryParams.append('page', params.page.toString());
    }

    if (params?.limit) {
      queryParams.append('limit', params.limit.toString());
    }

    if (params?.search) {
      queryParams.append('search', params.search);
    }

    if (params?.sort) {
      queryParams.append('sort', params.sort);
    }

    if (params?.order) {
      queryParams.append('order', params.order);
    }

    const url = `${this.baseUrl}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await get<Customer[]>(url);

    return {
      customers: Array.isArray(response.data) ? response.data : (Array.isArray(response) ? response : []),
      pagination: response.meta?.pagination
    };
  }

  /**
   * Get all customers (alias for compatibility)
   */
  async getAll(params?: { limit?: number; search?: string; page?: number }) {
    const queryParams = new URLSearchParams();

    if (params?.page) {
      queryParams.append('page', params.page.toString());
    }

    if (params?.limit) {
      queryParams.append('limit', params.limit.toString());
    }

    if (params?.search) {
      queryParams.append('search', params.search);
    }

    const url = `${this.baseUrl}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    const response = await get<Customer[]>(url);
    return {
      success: true,
      data: Array.isArray(response.data) ? response.data : (Array.isArray(response) ? response : []),
      meta: response.meta,
      pagination: response.meta?.pagination
    };
  }

  /**
   * Get customers (for receivables compatibility)
   */
  async getCustomers(params?: { limit?: number; search?: string; page?: number }) {
    const response = await this.getAllCustomers(params);
    // Return in expected format for receivables
    return {
      customers: response.customers || response.data || [],
      pagination: response.pagination || response.meta?.pagination || {
        page: 1,
        limit: params?.limit || 10,
        total: Array.isArray(response.customers) ? response.customers.length : Array.isArray(response.data) ? response.data.length : 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(id: number): Promise<Customer> {
    const response = await get<Customer>(`${this.baseUrl}/${id}`);
    return response.data;
  }

  /**
   * Create new customer
   */
  async createCustomer(customerData: Omit<Customer, 'id' | 'created_at' | 'updated_at'>): Promise<Customer> {
    const response = await post<Customer>(this.baseUrl, customerData);
    return response.data;
  }

  /**
   * Update customer
   */
  async updateCustomer(id: number, customerData: Partial<Omit<Customer, 'id' | 'created_at' | 'updated_at'>>): Promise<Customer> {
    const response = await put<Customer>(`${this.baseUrl}/${id}`, customerData);
    return response.data;
  }

  /**
   * Delete customer
   */
  async deleteCustomer(id: number): Promise<{ message: string; id: number }> {
    const response = await del<{ message: string; id: number }>(`${this.baseUrl}/${id}`);
    return response.data;
  }

  /**
   * Search customers
   */
  async searchCustomers(searchTerm: string, limit = 10): Promise<Customer[]> {
    const params: CustomerParams = {
      search: searchTerm,
      limit,
      page: 1
    };

    const result = await this.getAllCustomers(params);
    return result.customers;
  }

  /**
   * Get customers by tax code
   */
  async getCustomerByTaxCode(taxCode: string): Promise<Customer | null> {
    try {
      const result = await this.searchCustomers(taxCode, 1);
      const customer = result.find(c => c.tax_code === taxCode);
      return customer || null;
    } catch (error) {
      console.error('Error searching customer by tax code:', error);
      return null;
    }
  }

  /**
   * Validate customer data
   */
  validateCustomerData(customerData: Partial<Customer>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!customerData.name || customerData.name.trim().length === 0) {
      errors.push('Tên khách hàng là bắt buộc');
    }

    // Name length
    if (customerData.name && customerData.name.length > 200) {
      errors.push('Tên khách hàng không được quá 200 ký tự');
    }

    // Tax code length
    if (customerData.tax_code && customerData.tax_code.length > 20) {
      errors.push('Mã số thuế không được quá 20 ký tự');
    }

    // Short name length
    if (customerData.short_name && customerData.short_name.length > 100) {
      errors.push('Tên viết tắt không được quá 100 ký tự');
    }

    // Address length
    if (customerData.address && customerData.address.length > 1000) {
      errors.push('Địa chỉ không được quá 1000 ký tự');
    }

    // Contact person length
    if (customerData.contact_person && customerData.contact_person.length > 100) {
      errors.push('Tên người liên hệ không được quá 100 ký tự');
    }

    // Phone length
    if (customerData.phone && customerData.phone.length > 20) {
      errors.push('Số điện thoại không được quá 20 ký tự');
    }

    // Email validation
    if (customerData.email) {
      const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
      if (!emailRegex.test(customerData.email)) {
        errors.push('Email không đúng định dạng');
      }
      if (customerData.email.length > 100) {
        errors.push('Email không được quá 100 ký tự');
      }
    }

    // Phone validation (Vietnam format)
    if (customerData.phone) {
      const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
      const cleanPhone = customerData.phone.replace(/\s/g, '');
      if (!phoneRegex.test(cleanPhone)) {
        errors.push('Số điện thoại không đúng định dạng Việt Nam');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format customer data for display
   */
  formatCustomerForDisplay(customer: Customer): Customer {
    return {
      ...customer,
      name: customer.name?.trim() || '',
      short_name: customer.short_name?.trim() || '',
      tax_code: customer.tax_code?.trim() || '',
      address: customer.address?.trim() || '',
      contact_person: customer.contact_person?.trim() || '',
      phone: customer.phone?.trim() || '',
      email: customer.email?.trim() || '',
    };
  }

  /**
   * Export customers to CSV format
   */
  exportToCSV(customers: Customer[]): string {
    const headers = [
      'ID',
      'Mã số thuế',
      'Tên khách hàng',
      'Tên viết tắt',
      'Địa chỉ',
      'Người liên hệ',
      'Số điện thoại',
      'Email',
      'Ngày tạo'
    ];

    const csvContent = [
      headers.join(','),
      ...customers.map(customer => [
        customer.id,
        customer.tax_code || '',
        `"${customer.name || ''}"`,
        `"${customer.short_name || ''}"`,
        `"${customer.address || ''}"`,
        `"${customer.contact_person || ''}"`,
        customer.phone || '',
        customer.email || '',
        customer.created_at ? new Date(customer.created_at).toLocaleDateString('vi-VN') : ''
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  /**
   * Download customers as CSV file
   */
  downloadCSV(customers: Customer[], filename = 'customers.csv'): void {
    const csvContent = this.exportToCSV(customers);
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
}

// Export singleton instance
export const customerService = new CustomerService();
export default customerService;
