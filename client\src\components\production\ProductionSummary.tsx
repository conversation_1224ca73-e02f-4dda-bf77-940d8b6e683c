/**
 * ProductionSummary Component
 * Widget tổng kết sản lượng theo ngày/tháng
 */

import React from 'react';
import {
  Paper,
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  MonetizationOn as MoneyIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';

interface ProductionSummaryData {
  totalProduction: number;
  totalAmount: number;
  totalContracts: number;
  totalProducts: number;
  dailyAverage: number;
  monthlyGrowth: number;
  topProducts: Array<{
    product_name: string;
    total_quantity: number;
    total_amount: number;
  }>;
  recentProductions: Array<{
    production_date: string;
    total_quantity: number;
    total_amount: number;
  }>;
}

interface ProductionSummaryProps {
  data: ProductionSummaryData;
  loading?: boolean;
  period?: 'today' | 'week' | 'month' | 'year';
  onPeriodChange?: (period: string) => void;
}

const ProductionSummary: React.FC<ProductionSummaryProps> = ({
  data,
  loading = false,
  period = 'month'
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const getPeriodLabel = (period: string): string => {
    const labels = {
      today: 'Hôm nay',
      week: 'Tuần này',
      month: 'Tháng này',
      year: 'Năm này'
    };
    return labels[period as keyof typeof labels] || 'Tháng này';
  };

  if (loading) {
    return (
      <Paper sx={{ p: 3 }}>
        <Typography>Đang tải dữ liệu...</Typography>
      </Paper>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          Tổng kết sản lượng
        </Typography>
        <Chip 
          label={getPeriodLabel(period)} 
          color="primary" 
          variant="outlined" 
          size="small" 
        />
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InventoryIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="body2" color="textSecondary">
                  Tổng sản lượng
                </Typography>
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', fontSize: '1.5rem' }}>
                {formatNumber(data.totalProduction)}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Đơn vị sản phẩm
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MoneyIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="body2" color="textSecondary">
                  Tổng giá trị
                </Typography>
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', fontSize: '1.5rem' }}>
                {formatCurrency(data.totalAmount)}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Doanh thu sản xuất
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AssessmentIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="body2" color="textSecondary">
                  Số hợp đồng
                </Typography>
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', fontSize: '1.5rem' }}>
                {formatNumber(data.totalContracts)}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Hợp đồng có sản xuất
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <TrendingUpIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="body2" color="textSecondary">
                  Trung bình/ngày
                </Typography>
              </Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', fontSize: '1.5rem' }}>
                {formatNumber(data.dailyAverage)}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                Sản lượng hàng ngày
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Top Products */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
              Sản phẩm hàng đầu
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {data.topProducts.length === 0 ? (
              <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', mt: 4 }}>
                Chưa có dữ liệu sản phẩm
              </Typography>
            ) : (
              <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                {data.topProducts.map((product, index) => (
                  <Box key={index} sx={{ mb: 2, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {product.product_name}
                      </Typography>
                      <Chip 
                        label={`#${index + 1}`} 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                      />
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                      <Typography variant="caption" color="textSecondary">
                        SL: {formatNumber(product.total_quantity)}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        GT: {formatCurrency(product.total_amount)}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Recent Productions */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 2 }}>
              Sản lượng gần đây
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {data.recentProductions.length === 0 ? (
              <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', mt: 4 }}>
                Chưa có dữ liệu sản lượng
              </Typography>
            ) : (
              <Box sx={{ maxHeight: 200, overflowY: 'auto' }}>
                {data.recentProductions.map((production, index) => (
                  <Box key={index} sx={{ mb: 2, p: 1, bgcolor: 'grey.50', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {new Date(production.production_date).toLocaleDateString('vi-VN')}
                      </Typography>
                      <Typography variant="body2" color="primary" sx={{ fontWeight: 'bold' }}>
                        {formatCurrency(production.total_amount)}
                      </Typography>
                    </Box>
                    <Typography variant="caption" color="textSecondary">
                      Sản lượng: {formatNumber(production.total_quantity)} đơn vị
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Growth Indicator */}
      {data.monthlyGrowth !== 0 && (
        <Box sx={{ mt: 2, p: 2, bgcolor: data.monthlyGrowth > 0 ? 'success.light' : 'error.light', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <TrendingUpIcon 
              sx={{ 
                mr: 1, 
                color: data.monthlyGrowth > 0 ? 'success.dark' : 'error.dark',
                transform: data.monthlyGrowth < 0 ? 'rotate(180deg)' : 'none'
              }} 
            />
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {data.monthlyGrowth > 0 ? 'Tăng trưởng' : 'Giảm'} {Math.abs(data.monthlyGrowth).toFixed(1)}% so với kỳ trước
            </Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default ProductionSummary;
