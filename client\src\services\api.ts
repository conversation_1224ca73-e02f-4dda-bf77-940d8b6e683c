import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

/**
 * API Response Interface
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  links?: {
    self?: string;
    first?: string;
    prev?: string;
    next?: string;
    last?: string;
  };
}

/**
 * API Error Response Interface
 */
export interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details: string[];
  };
}

/**
 * API Configuration
 */
const API_BASE_URL = import.meta.env.VITE_API_URL ||
  (import.meta.env.PROD ?
    (window.location.hostname.includes('ivc-audit-app') ?
      'https://ivc-audit-app-706c36cfdde0.herokuapp.com/api/v1' :
      'https://laundry-management-app-cd86fae6d1d0.herokuapp.com/api/v1') :
    'http://localhost:8500/api/v1');

/**
 * Create Axios Instance
 */
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * Request Interceptor
 * Automatically add auth token to requests
 * NOTE: Authentication tạm thời bị tắt để tập trung phát triển core business features
 */
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // NOTE: Authentication tạm thời bị tắt ở backend để tập trung phát triển core business features
    // const token = localStorage.getItem('token') || localStorage.getItem('authToken');
    // if (token && config.headers) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }

    // Log request in development
    if (import.meta.env.DEV) {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
        data: config.data,
        params: config.params,
      });
    }

    return config;
  },
  (error: AxiosError) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

/**
 * Response Interceptor
 * Handle responses and errors globally
 */
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // Log response in development
    if (import.meta.env.DEV) {
      console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
        status: response.status,
        data: response.data,
      });
    }

    return response;
  },
  async (error: AxiosError<ApiErrorResponse>) => {
    const originalRequest = error.config;

    // Log error in development
    if (import.meta.env.DEV) {
      console.error(`❌ API Error: ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url}`, {
        status: error.response?.status,
        data: error.response?.data,
      });
    }

    // NOTE: Tạm thời comment out logic refresh token vì authentication đã bị tắt
    // Handle 401 Unauthorized - Token expired
    // if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
    //   originalRequest._retry = true;

    //   try {
    //     const refreshToken = localStorage.getItem('refreshToken');

    //     if (refreshToken) {
    //       // Try to refresh token
    //       const refreshResponse = await axios.post(`${API_BASE_URL}/auth/refresh`, {
    //         refreshToken,
    //       });

    //       const { token: newToken, refreshToken: newRefreshToken } = refreshResponse.data.data;

    //       // Update stored tokens
    //       localStorage.setItem('token', newToken);
    //       localStorage.setItem('refreshToken', newRefreshToken);

    //       // Update authorization header
    //       if (originalRequest.headers) {
    //         originalRequest.headers.Authorization = `Bearer ${newToken}`;
    //       }

    //       // Retry original request
    //       return api(originalRequest);
    //     }
    //   } catch (refreshError) {
    //     // Refresh failed, redirect to login
    //     localStorage.removeItem('token');
    //     localStorage.removeItem('refreshToken');

    //     // Dispatch logout event
    //     window.dispatchEvent(new CustomEvent('auth:logout'));

    //     return Promise.reject(refreshError);
    //   }
    // }

    // Handle other errors
    const errorMessage = error.response?.data?.error?.message || 'Có lỗi xảy ra';
    const errorDetails = error.response?.data?.error?.details || [];

    // Create standardized error object
    const standardError = {
      message: errorMessage,
      details: errorDetails,
      status: error.response?.status,
      code: error.response?.data?.error?.code,
      originalError: error,
    };

    return Promise.reject(standardError);
  }
);

/**
 * API Helper Functions
 */

/**
 * GET request
 */
export const get = async <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  const response = await api.get<ApiResponse<T>>(url, config);
  return response.data;
};

/**
 * POST request
 */
export const post = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  const response = await api.post<ApiResponse<T>>(url, data, config);
  return response.data;
};

/**
 * PUT request
 */
export const put = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  const response = await api.put<ApiResponse<T>>(url, data, config);
  return response.data;
};

/**
 * DELETE request
 */
export const del = async <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  const response = await api.delete<ApiResponse<T>>(url, config);
  return response.data;
};

/**
 * PATCH request
 */
export const patch = async <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<ApiResponse<T>> => {
  const response = await api.patch<ApiResponse<T>>(url, data, config);
  return response.data;
};

/**
 * Upload file
 */
export const upload = async <T = any>(
  url: string,
  file: File,
  onUploadProgress?: (progressEvent: any) => void
): Promise<ApiResponse<T>> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await api.post<ApiResponse<T>>(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });

  return response.data;
};

/**
 * Health check
 */
export const healthCheck = async (): Promise<any> => {
  const response = await axios.get(`${API_BASE_URL.replace('/api/v1', '')}/health`);
  return response.data;
};

// Export new services
export { default as productService } from './productService';
export { default as contractService } from './contractService';
export { default as contractPriceService } from './contractPriceService';
export { default as dailyProductionService } from './dailyProductionService';

export default api;
