/**
 * Validation Utilities
 * Common validation functions for forms and data
 */

/**
 * Validation Result Interface
 */
export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

/**
 * Form Validation Result Interface
 */
export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Email validation (optional field)
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email || email.trim() === '') {
    return { isValid: true }; // Email is optional
  }

  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Email không đúng định dạng' };
  }

  if (email.length > 100) {
    return { isValid: false, message: '<PERSON><PERSON> không được quá 100 ký tự' };
  }

  return { isValid: true };
};

/**
 * Email validation (required field)
 */
export const validateEmailRequired = (email: string): ValidationResult => {
  if (!email || email.trim() === '') {
    return { isValid: false, message: 'Email là bắt buộc' };
  }

  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Email không đúng định dạng' };
  }

  if (email.length > 100) {
    return { isValid: false, message: 'Email không được quá 100 ký tự' };
  }

  return { isValid: true };
};

/**
 * Password validation
 */
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, message: 'Mật khẩu là bắt buộc' };
  }
  
  if (password.length < 6) {
    return { isValid: false, message: 'Mật khẩu phải có ít nhất 6 ký tự' };
  }
  
  if (password.length > 100) {
    return { isValid: false, message: 'Mật khẩu không được quá 100 ký tự' };
  }
  
  return { isValid: true };
};

/**
 * Phone number validation (Vietnam format)
 */
export const validatePhone = (phone: string): ValidationResult => {
  if (!phone) {
    return { isValid: true }; // Phone is optional
  }
  
  // Remove spaces and special characters
  const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
  
  // Vietnam phone number regex
  const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
  
  if (!phoneRegex.test(cleanPhone)) {
    return { isValid: false, message: 'Số điện thoại không đúng định dạng Việt Nam' };
  }
  
  return { isValid: true };
};

/**
 * Required field validation
 */
export const validateRequired = (value: string, fieldName: string): ValidationResult => {
  if (!value || value.trim().length === 0) {
    return { isValid: false, message: `${fieldName} là bắt buộc` };
  }
  
  return { isValid: true };
};

/**
 * String length validation
 */
export const validateLength = (
  value: string,
  minLength: number,
  maxLength: number,
  fieldName: string
): ValidationResult => {
  if (value.length < minLength) {
    return { isValid: false, message: `${fieldName} phải có ít nhất ${minLength} ký tự` };
  }
  
  if (value.length > maxLength) {
    return { isValid: false, message: `${fieldName} không được quá ${maxLength} ký tự` };
  }
  
  return { isValid: true };
};

/**
 * Tax code validation (Vietnam format)
 */
export const validateTaxCode = (taxCode: string): ValidationResult => {
  if (!taxCode) {
    return { isValid: true }; // Tax code is optional
  }
  
  // Remove spaces and special characters
  const cleanTaxCode = taxCode.replace(/[\s\-]/g, '');
  
  // Vietnam tax code is 10 or 13 digits
  const taxCodeRegex = /^[0-9]{10}([0-9]{3})?$/;
  
  if (!taxCodeRegex.test(cleanTaxCode)) {
    return { isValid: false, message: 'Mã số thuế phải có 10 hoặc 13 chữ số' };
  }
  
  return { isValid: true };
};

/**
 * URL validation
 */
export const validateUrl = (url: string): ValidationResult => {
  if (!url) {
    return { isValid: true }; // URL is optional
  }
  
  try {
    new URL(url);
    return { isValid: true };
  } catch {
    return { isValid: false, message: 'URL không đúng định dạng' };
  }
};

/**
 * Number validation
 */
export const validateNumber = (
  value: string,
  min?: number,
  max?: number,
  fieldName?: string
): ValidationResult => {
  const num = parseFloat(value);
  
  if (isNaN(num)) {
    return { isValid: false, message: `${fieldName || 'Giá trị'} phải là số` };
  }
  
  if (min !== undefined && num < min) {
    return { isValid: false, message: `${fieldName || 'Giá trị'} phải lớn hơn hoặc bằng ${min}` };
  }
  
  if (max !== undefined && num > max) {
    return { isValid: false, message: `${fieldName || 'Giá trị'} phải nhỏ hơn hoặc bằng ${max}` };
  }
  
  return { isValid: true };
};

/**
 * Date validation
 */
export const validateDate = (date: string, fieldName?: string): ValidationResult => {
  if (!date) {
    return { isValid: true }; // Date is optional
  }
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, message: `${fieldName || 'Ngày'} không đúng định dạng` };
  }
  
  return { isValid: true };
};

/**
 * Date range validation
 */
export const validateDateRange = (
  startDate: string,
  endDate: string,
  startFieldName = 'Ngày bắt đầu',
  endFieldName = 'Ngày kết thúc'
): ValidationResult => {
  if (!startDate || !endDate) {
    return { isValid: true }; // Optional validation
  }
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return { isValid: false, message: 'Ngày không đúng định dạng' };
  }
  
  if (start > end) {
    return { isValid: false, message: `${startFieldName} phải nhỏ hơn ${endFieldName}` };
  }
  
  return { isValid: true };
};

/**
 * File validation
 */
export const validateFile = (
  file: File,
  allowedTypes: string[],
  maxSize: number // in bytes
): ValidationResult => {
  if (!file) {
    return { isValid: false, message: 'Vui lòng chọn file' };
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return { 
      isValid: false, 
      message: `Chỉ chấp nhận file: ${allowedTypes.join(', ')}` 
    };
  }
  
  // Check file size
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return { 
      isValid: false, 
      message: `File không được vượt quá ${maxSizeMB}MB` 
    };
  }
  
  return { isValid: true };
};

/**
 * Password confirmation validation
 */
export const validatePasswordConfirmation = (
  password: string,
  confirmPassword: string
): ValidationResult => {
  if (password !== confirmPassword) {
    return { isValid: false, message: 'Mật khẩu xác nhận không khớp' };
  }
  
  return { isValid: true };
};

/**
 * Customer form validation
 */
export const validateCustomerForm = (data: {
  name: string;
  tax_code?: string;
  short_name?: string;
  address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
}): FormValidationResult => {
  const errors: Record<string, string> = {};
  
  // Name validation
  const nameValidation = validateRequired(data.name, 'Tên khách hàng');
  if (!nameValidation.isValid) {
    errors.name = nameValidation.message!;
  } else {
    const lengthValidation = validateLength(data.name, 1, 200, 'Tên khách hàng');
    if (!lengthValidation.isValid) {
      errors.name = lengthValidation.message!;
    }
  }
  
  // Tax code validation
  if (data.tax_code) {
    const taxCodeValidation = validateTaxCode(data.tax_code);
    if (!taxCodeValidation.isValid) {
      errors.tax_code = taxCodeValidation.message!;
    }
  }
  
  // Short name validation
  if (data.short_name) {
    const lengthValidation = validateLength(data.short_name, 0, 100, 'Tên viết tắt');
    if (!lengthValidation.isValid) {
      errors.short_name = lengthValidation.message!;
    }
  }
  
  // Address validation
  if (data.address) {
    const lengthValidation = validateLength(data.address, 0, 1000, 'Địa chỉ');
    if (!lengthValidation.isValid) {
      errors.address = lengthValidation.message!;
    }
  }
  
  // Contact person validation
  if (data.contact_person) {
    const lengthValidation = validateLength(data.contact_person, 0, 100, 'Người liên hệ');
    if (!lengthValidation.isValid) {
      errors.contact_person = lengthValidation.message!;
    }
  }
  
  // Phone validation
  if (data.phone) {
    const phoneValidation = validatePhone(data.phone);
    if (!phoneValidation.isValid) {
      errors.phone = phoneValidation.message!;
    }
  }
  
  // Email validation
  if (data.email) {
    const emailValidation = validateEmail(data.email);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.message!;
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Login form validation
 */
export const validateLoginForm = (data: {
  email: string;
  password: string;
}): FormValidationResult => {
  const errors: Record<string, string> = {};
  
  // Email validation (required for login)
  const emailValidation = validateEmailRequired(data.email);
  if (!emailValidation.isValid) {
    errors.email = emailValidation.message!;
  }
  
  // Password validation
  const passwordValidation = validateRequired(data.password, 'Mật khẩu');
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.message!;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
