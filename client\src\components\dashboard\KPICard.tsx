import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  useTheme
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';

interface KPICardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    label?: string;
    isPositive?: boolean;
  };
  onClick?: () => void;
  loading?: boolean;
}

const KPICard: React.FC<KPICardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  onClick,
  loading = false
}) => {
  const theme = useTheme();

  const formatValue = (val: string | number): string => {
    if (typeof val === 'number') {
      return val.toLocaleString('vi-VN');
    }
    
    // Nếu là string số, format theo locale Việt Nam
    const numValue = parseFloat(val);
    if (!isNaN(numValue)) {
      return numValue.toLocaleString('vi-VN');
    }
    
    return val;
  };

  const getTrendColor = () => {
    if (!trend) return 'default';
    if (trend.isPositive !== undefined) {
      return trend.isPositive ? 'success' : 'error';
    }
    return trend.value >= 0 ? 'success' : 'error';
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    const isPositive = trend.isPositive !== undefined ? trend.isPositive : trend.value >= 0;
    return isPositive ? <TrendingUpIcon fontSize="small" /> : <TrendingDownIcon fontSize="small" />;
  };

  return (
    <Card
      elevation={2}
      className="laundry-hover-lift"
      onClick={onClick}
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
        border: `1px solid ${theme.palette[color].light}20`,
        borderRadius: 2,
        transition: 'all 0.3s ease',
        '&:hover': onClick ? {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
          borderColor: `${theme.palette[color].main}40`
        } : {},
        fontSize: '0.85rem' // User preference for smaller font
      }}
    >
      <CardContent sx={{ p: 2.5 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5 }}>
          <Typography 
            variant="body2" 
            color="text.secondary"
            sx={{ 
              fontWeight: 500,
              fontSize: '0.85rem'
            }}
          >
            {title}
          </Typography>
          {icon && (
            <Box
              sx={{
                color: theme.palette[color].main,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              {icon}
            </Box>
          )}
        </Box>

        {/* Main Value */}
        <Typography
          variant="h4"
          component="div"
          sx={{
            fontWeight: 700,
            color: theme.palette[color].main,
            mb: 1,
            fontSize: '1.75rem'
          }}
        >
          {loading ? '...' : formatValue(value)}
        </Typography>

        {/* Subtitle */}
        {subtitle && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ 
              mb: trend ? 1 : 0,
              fontSize: '0.8rem'
            }}
          >
            {subtitle}
          </Typography>
        )}

        {/* Trend */}
        {trend && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Chip
              icon={getTrendIcon()}
              label={`${trend.value > 0 ? '+' : ''}${trend.value}%`}
              size="small"
              color={getTrendColor()}
              variant="outlined"
              sx={{ 
                fontSize: '0.75rem',
                height: 24
              }}
            />
            {trend.label && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ fontSize: '0.75rem' }}
              >
                {trend.label}
              </Typography>
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default KPICard;
